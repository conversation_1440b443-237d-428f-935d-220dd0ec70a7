import request from '@/utils/axios'

// 获取项目列表
export function getProjectList(params) {
  return request({
    path: '/api/system/project',
    method: 'get',
    params
  })
}

// 获取项目详情
export function getProjectDetail(id) {
  return request({
    path: `/api/system/project/${id}`,
    method: 'get'
  })
}

// 获取账户项目列表
export function getAccountProjects(accountId) {
  return request({
    path: `/api/system/project/account-projects/${accountId}`,
    method: 'get'
  })
}

// 绑定项目到账户
export function bindProjectToAccount(data) {
  return request({
    path: '/api/system/project/bind-account',
    method: 'post',
    data
  })
}

// 创建项目
export function createProject(data) {
  return request({
    path: '/api/system/project',
    method: 'post',
    data
  })
}

// 更新项目
export function updateProject(data) {
  return request({
    path: `/api/system/project`,
    method: 'put',
    data
  })
}

// 删除项目
export function deleteProject(id) {
  return request({
    path: `/api/system/project/${id}`,
    method: 'delete'
  })
}

// 更新项目状态
export function updateProjectStatus(id, status) {
  return request({
    path: `/api/projects/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 更新项目进度
export function updateProjectProgress(id, progress) {
  return request({
    path: `/api/projects/${id}/progress`,
    method: 'put',
    data: { progress }
  })
} 