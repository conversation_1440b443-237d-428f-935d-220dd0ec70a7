<template>
  <div class="alarm-info-container">
    <!-- 报警统计信息 -->
    <div class="alarm-stats">
      <div class="alarm-stat-item">
        <div class="alarm-stat-label">今日报警</div>
        <div class="alarm-stat-value alarm-total">{{ statistics.today.total || todayAlarms.length }}</div>
      </div>
      <div class="alarm-stat-item">
        <div class="alarm-stat-label">待处理</div>
        <div class="alarm-stat-value status-pending">{{ statistics.today.pending || pendingAlarms.length }}</div>
      </div>
      <div class="alarm-stat-item">
        <div class="alarm-stat-label">已处理</div>
        <div class="alarm-stat-value status-processed">{{ statistics.today.handled || processedAlarms.length }}</div>
      </div>
    </div>

    <!-- 模块统计信息 -->
    <div class="module-stats" v-if="hasModuleStats">
      <div class="module-stat-item">
        <i class="fas fa-server"></i>
        <span>设备: {{ statistics.modules.device }}</span>
      </div>
      <div class="module-stat-item">
        <i class="fas fa-broadcast-tower"></i>
        <span>网关: {{ statistics.modules.gateway }}</span>
      </div>
      <div class="module-stat-item">
        <i class="fas fa-microchip"></i>
        <span>传感器: {{ statistics.modules.sensor }}</span>
      </div>
    </div>

    <!-- 报警分类标签 -->
    <div class="alarm-tabs">
      <div
        class="alarm-tab"
        :class="{ 'active': activeTab === 'today' }"
        @click="activeTab = 'today'"
      >
        <i class="fas fa-calendar-day"></i> 今日报警
      </div>
      <div
        class="alarm-tab"
        :class="{ 'active': activeTab === 'pending' }"
        @click="activeTab = 'pending'"
      >
        <i class="fas fa-exclamation-circle"></i> 未处理报警
      </div>
    </div>

    <!-- 处理状态提示 -->
    <div v-if="processingStatus.processing || processingStatus.success || processingStatus.error"
         class="status-notification"
         :class="{
           'processing': processingStatus.processing,
           'success': processingStatus.success,
           'error': processingStatus.error
         }">
      <i class="fas"
         :class="{
           'fa-spinner fa-spin': processingStatus.processing,
           'fa-check-circle': processingStatus.success,
           'fa-exclamation-circle': processingStatus.error
         }"></i>
      <span>{{ processingStatus.message }}</span>
    </div>

    <!-- 图形化报警卡片 -->
    <div class="alarm-cards-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-card">
        <i class="fas fa-spinner fa-spin"></i>
        <span>加载报警数据中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-card">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ errorMessage }}</span>
        <button class="retry-btn" @click="fetchAlarmData">
          <i class="fas fa-sync-alt"></i> 重试
        </button>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="filteredAlarms.length === 0" class="no-data-card">
        <i class="fas fa-info-circle"></i>
        <span>暂无报警数据</span>
      </div>

      <div
        v-for="(alarm, index) in filteredAlarms"
        :key="index"
        class="alarm-card"
        :class="{ 'unprocessed': alarm.status === '待处理' }"
      >
        <!-- 设备信息和状态 -->
        <div class="alarm-card-header">
          <div class="device-icon" :title="alarm.device">
            <i :class="getDeviceIcon(alarm.device)"></i>
          </div>
          <div class="device-info">
            <div class="device-name">{{ alarm.device }}</div>
            <div class="gateway-name" v-if="alarm.gateway">{{ alarm.gateway }}</div>
          </div>
          <div class="alarm-time">
            <i class="fas fa-clock"></i>
            {{ formatDisplayTime(alarm.time) }}
          </div>
          <div class="status-badge" :class="getStatusClass(alarm.status)">
            <i :class="getStatusIcon(alarm.status)"></i>
          </div>
        </div>

        <!-- 传感器和报警值 -->
        <div class="alarm-card-body">
          <div class="sensor-icon" :title="alarm.sensor">
            <i :class="getSensorIcon(alarm.sensor)"></i>
          </div>
          <div class="sensor-info">
            <div class="sensor-name">{{ alarm.sensor }}</div>
            <div class="sensor-value-row">
              <div class="alarm-value" :class="getValueClass(alarm)">
                {{ alarm.value || getRandomValue(alarm.sensor) }} {{ alarm.unit || getSensorUnit(alarm.sensor) }}
              </div>
              <div class="alarm-description">
                {{ getAlarmDescription(alarm) }}
              </div>
            </div>
          </div>
          <div class="status-indicator" :class="getStatusClass(alarm.status)">
            <span>{{ alarm.status }}</span>
            <div class="alarm-actions" v-if="alarm.status === '待处理'">
              <div v-if="processingStatus.processing" class="action-btn processing-btn" title="处理中...">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <template v-else>
                <button class="action-btn process-btn" @click="processAlarm(index)" title="处理报警">
                  <i class="fas fa-check"></i>
                </button>
                <button class="action-btn ignore-btn" @click="ignoreAlarm(index)" title="忽略报警">
                  <i class="fas fa-ban"></i>
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getProjectAlarmsData, updateAlarmStatus } from '@/api/system/alarms'

export default {
  name: 'AlarmTable',
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      alarms: [],
      devices: ['左边设备', '右边设备', '主桥塔', '辅助塔', '监控站'],
      sensors: ['环境温度', '环境湿度', '环境风速', '环境风力', '环境噪声', '大气压力', '设备压力', '设备温度'],
      apiRefreshTimer: null,
      isLoading: false,
      hasError: false,
      errorMessage: '',
      activeTab: 'today', // 默认显示今日报警
      // 统计数据
      statistics: {
        today: { total: 0, pending: 0, handled: 0 },
        modules: { device: 0, gateway: 0, sensor: 0 }
      },
      // 告警处理状态
      processingStatus: {
        processing: false,
        success: false,
        error: false,
        message: ''
      },
      deviceIcons: {
        '左边设备': 'fas fa-server',
        '右边设备': 'fas fa-hdd',
        '主桥塔': 'fas fa-archway',
        '辅助塔': 'fas fa-tower-observation',
        '监控站': 'fas fa-broadcast-tower'
      },
      sensorIcons: {
        '环境温度': 'fas fa-thermometer-half',
        '环境湿度': 'fas fa-tint',
        '环境风速': 'fas fa-wind',
        '环境风力': 'fas fa-fan',
        '环境噪声': 'fas fa-volume-up',
        '大气压力': 'fas fa-tachometer-alt',
        '设备压力': 'fas fa-compress-alt',
        '设备温度': 'fas fa-fire'
      },
      sensorUnits: {
        '环境温度': '℃',
        '环境湿度': '%RH',
        '环境风速': 'm/s',
        '环境风力': '级',
        '环境噪声': 'dB',
        '大气压力': 'kPa',
        '设备压力': 'MPa',
        '设备温度': '℃'
      },
      sensorThresholds: {
        '环境温度': { min: 15, max: 35, critical: 40 },
        '环境湿度': { min: 30, max: 70, critical: 85 },
        '环境风速': { min: 0, max: 5, critical: 10 },
        '环境风力': { min: 0, max: 4, critical: 6 },
        '环境噪声': { min: 0, max: 60, critical: 80 },
        '大气压力': { min: 95, max: 105, critical: 110 },
        '设备压力': { min: 0.5, max: 2.5, critical: 3 },
        '设备温度': { min: 20, max: 60, critical: 75 }
      },
      statusIcons: {
        '待处理': 'fas fa-exclamation-triangle',
        '已处理': 'fas fa-check-circle',
        '已忽略': 'fas fa-ban'
      }
    }
  },
  computed: {
    // 今日报警
    todayAlarms() {
      const today = new Date().toISOString().split('T')[0].replace(/-/g, '/');
      return this.alarms.filter(alarm => alarm.time.startsWith(today));
    },
    // 待处理报警
    pendingAlarms() {
      return this.alarms.filter(alarm => alarm.status === '待处理');
    },
    // 已处理报警
    processedAlarms() {
      return this.alarms.filter(alarm => alarm.status === '已处理');
    },
    // 根据当前标签过滤报警
    filteredAlarms() {
      if (this.activeTab === 'today') {
        return this.todayAlarms;
      } else if (this.activeTab === 'pending') {
        return this.pendingAlarms;
      }
      return this.alarms;
    },
    // 是否有模块统计数据
    hasModuleStats() {
      return this.statistics.modules.device > 0 ||
             this.statistics.modules.gateway > 0 ||
             this.statistics.modules.sensor > 0;
    }
  },
  mounted() {
    // 如果有项目ID，获取报警数据
    if (this.projectId) {
      this.fetchAlarmData();
      this.startApiDataRefresh();
    } else {
      // 如果没有项目ID，使用模拟数据
      this.addHistoricalAlarms();
    }
  },
  beforeDestroy() {
    // 清除API刷新定时器
    if (this.apiRefreshTimer) {
      clearInterval(this.apiRefreshTimer);
      this.apiRefreshTimer = null;
    }
  },
  watch: {
    projectId: {
      handler(newVal) {
        // 清除现有定时器
        if (this.apiRefreshTimer) {
          clearInterval(this.apiRefreshTimer);
          this.apiRefreshTimer = null;
        }

        // 如果有新的项目ID，获取报警数据
        if (newVal) {
          this.fetchAlarmData();
          this.startApiDataRefresh();
        } else {
          // 如果没有项目ID，使用模拟数据
          this.addHistoricalAlarms();
        }
      },
      immediate: true
    }
  },
  methods: {
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    },
    getStatusClass(status) {
      switch(status) {
        case '待处理':
          return 'status-pending';
        case '已处理':
          return 'status-processed';
        case '已忽略':
          return 'status-ignored';
        default:
          return '';
      }
    },
    getDeviceIcon(device) {
      return this.deviceIcons[device] || 'fas fa-server';
    },
    getSensorIcon(sensor) {
      return this.sensorIcons[sensor] || 'fas fa-microchip';
    },
    getSensorUnit(sensor) {
      return this.sensorUnits[sensor] || '';
    },
    getStatusIcon(status) {
      return this.statusIcons[status] || 'fas fa-question-circle';
    },
    formatDisplayTime(timeString) {
      // 如果是完整时间格式，只显示时间部分
      if (timeString.includes(' ')) {
        return timeString.split(' ')[1];
      }
      return timeString;
    },
    getRandomValue(sensor) {
      // 根据传感器类型生成合理的随机值
      const threshold = this.sensorThresholds[sensor];
      if (!threshold) return '0';

      // 生成一个超出正常范围但不超过临界值的数值
      const value = threshold.max + Math.random() * (threshold.critical - threshold.max);

      // 根据传感器类型格式化数值
      if (sensor === '环境风力') {
        return Math.floor(value).toString();
      } else if (sensor === '设备压力') {
        return value.toFixed(2);
      } else {
        return value.toFixed(1);
      }
    },
    getValueClass(alarm) {
      // 如果没有值，返回默认样式
      if (!alarm.value) return '';

      const sensor = alarm.sensor;
      const value = parseFloat(alarm.value);
      const threshold = this.sensorThresholds[sensor];

      if (!threshold) return '';

      if (value >= threshold.critical) {
        return 'value-critical';
      } else if (value > threshold.max) {
        return 'value-warning';
      } else if (value < threshold.min) {
        return 'value-warning';
      }

      return 'value-normal';
    },
    getAlarmDescription(alarm) {
      // 如果报警对象有描述，直接使用
      if (alarm.description) {
        return alarm.description;
      }

      const sensor = alarm.sensor;
      const value = alarm.value || this.getRandomValue(sensor);
      const unit = alarm.unit || this.getSensorUnit(sensor);
      const threshold = alarm.threshold || this.sensorThresholds[sensor]?.max;

      if (!threshold) return `${sensor}异常`;

      // 使用报警阈值
      if (parseFloat(value) > parseFloat(threshold)) {
        return `${sensor}值(${value}${unit})超过阈值(${threshold}${unit})`;
      } else if (alarm.level >= 3) {
        return `${sensor}值(${value}${unit})达到严重级别`;
      } else if (alarm.level >= 2) {
        return `${sensor}值(${value}${unit})达到警告级别`;
      }

      return `${sensor}异常(${value}${unit})`;
    },
    processAlarm(index) {
      if (index >= 0 && index < this.filteredAlarms.length) {
        const alarm = this.filteredAlarms[index];
        const alarmIndex = this.alarms.findIndex(a => a === alarm);

        if (alarmIndex !== -1) {
          // 设置处理中状态
          this.processingStatus = {
            processing: true,
            success: false,
            error: false,
            message: '正在处理告警...'
          };

          // 如果有API和报警ID，调用API更新服务器端状态
          if (this.projectId && alarm.id) {
            console.log(`处理报警: ${alarm.id}, 项目ID: ${this.projectId}`);

            // 调用API更新告警状态
            updateAlarmStatus(alarm.id, 'processed', '手动处理', '已确认并处理')
              .then(response => {
                console.log('报警状态更新成功:', response);

                // 更新本地状态
                this.alarms[alarmIndex].status = '已处理';

                // 设置成功状态
                this.processingStatus = {
                  processing: false,
                  success: true,
                  error: false,
                  message: '告警处理成功'
                };

                // 3秒后清除成功状态
                setTimeout(() => {
                  this.processingStatus.success = false;
                }, 3000);

                // 刷新告警数据
                this.fetchAlarmData();
              })
              .catch(error => {
                console.error('报警状态更新失败:', error);

                // 设置错误状态
                this.processingStatus = {
                  processing: false,
                  success: false,
                  error: true,
                  message: '告警处理失败: ' + (error.message || '未知错误')
                };

                // 5秒后清除错误状态
                setTimeout(() => {
                  this.processingStatus.error = false;
                }, 5000);
              });
          } else {
            // 如果没有API或报警ID，直接更新本地状态
            this.alarms[alarmIndex].status = '已处理';

            // 设置成功状态
            this.processingStatus = {
              processing: false,
              success: true,
              error: false,
              message: '告警处理成功(本地)'
            };

            // 3秒后清除成功状态
            setTimeout(() => {
              this.processingStatus.success = false;
            }, 3000);
          }
        }
      }
    },
    ignoreAlarm(index) {
      if (index >= 0 && index < this.filteredAlarms.length) {
        const alarm = this.filteredAlarms[index];
        const alarmIndex = this.alarms.findIndex(a => a === alarm);

        if (alarmIndex !== -1) {
          // 设置处理中状态
          this.processingStatus = {
            processing: true,
            success: false,
            error: false,
            message: '正在忽略告警...'
          };

          // 如果有API和报警ID，调用API更新服务器端状态
          if (this.projectId && alarm.id) {
            console.log(`忽略报警: ${alarm.id}, 项目ID: ${this.projectId}`);

            // 调用API更新告警状态
            updateAlarmStatus(alarm.id, 'ignored', '手动忽略', '已确认并忽略')
              .then(response => {
                console.log('报警状态更新成功:', response);

                // 更新本地状态
                this.alarms[alarmIndex].status = '已忽略';

                // 设置成功状态
                this.processingStatus = {
                  processing: false,
                  success: true,
                  error: false,
                  message: '告警已忽略'
                };

                // 3秒后清除成功状态
                setTimeout(() => {
                  this.processingStatus.success = false;
                }, 3000);

                // 刷新告警数据
                this.fetchAlarmData();
              })
              .catch(error => {
                console.error('报警状态更新失败:', error);

                // 设置错误状态
                this.processingStatus = {
                  processing: false,
                  success: false,
                  error: true,
                  message: '忽略告警失败: ' + (error.message || '未知错误')
                };

                // 5秒后清除错误状态
                setTimeout(() => {
                  this.processingStatus.error = false;
                }, 5000);
              });
          } else {
            // 如果没有API或报警ID，直接更新本地状态
            this.alarms[alarmIndex].status = '已忽略';

            // 设置成功状态
            this.processingStatus = {
              processing: false,
              success: true,
              error: false,
              message: '告警已忽略(本地)'
            };

            // 3秒后清除成功状态
            setTimeout(() => {
              this.processingStatus.success = false;
            }, 3000);
          }
        }
      }
    },
    // 从API获取报警数据
    fetchAlarmData() {
      if (!this.projectId) {
        console.warn('No project ID provided for alarm data');
        return;
      }

      console.log('获取项目报警数据，项目ID:', this.projectId);
      this.isLoading = true;
      this.hasError = false;

      getProjectAlarmsData(this.projectId)
        .then(response => {
          if (response && response.code === 0 && response.data) {
            console.log('获取到报警数据:', response.data);

            // 保存统计数据
            this.statistics = {
              today: response.data.today_statistics || { total: 0, pending: 0, handled: 0 },
              modules: response.data.module_statistics || { device: 0, gateway: 0, sensor: 0 }
            };

            // 处理API返回的报警数据
            if (response.data.alarm_list) {
              console.log('报警列表数据条数:', Array.isArray(response.data.alarm_list) ? response.data.alarm_list.length : '不是数组');
              if (Array.isArray(response.data.alarm_list) && response.data.alarm_list.length > 0) {
                console.log('报警数据第一条:', response.data.alarm_list[0]);
              }
              this.processAlarmData(response.data.alarm_list);
            } else {
              console.warn('报警列表数据为空或格式不正确，使用模拟数据');
              this.processAlarmData([]);
            }
          } else {
            console.warn('报警数据API返回错误或空数据:', response);
            this.hasError = true;
            this.errorMessage = '获取报警数据失败';

            // 如果没有数据，使用模拟数据
            if (this.alarms.length === 0) {
              this.addHistoricalAlarms();
            }
          }
        })
        .catch(error => {
          console.error('获取报警数据失败:', error);
          this.hasError = true;
          this.errorMessage = '获取报警数据失败: ' + (error.message || '未知错误');

          // 如果获取失败，使用模拟数据
          if (this.alarms.length === 0) {
            this.addHistoricalAlarms();
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    // 处理API返回的报警数据
    processAlarmData(alarmList) {
      console.log('processAlarmData 接收到的数据:', alarmList, '类型:', typeof alarmList);

      if (!Array.isArray(alarmList)) {
        console.warn('报警数据不是数组格式:', alarmList);
        // 如果没有有效数据，使用模拟数据
        this.addHistoricalAlarms();
        return;
      }

      if (alarmList.length === 0) {
        console.warn('报警数据数组为空');
        // 如果数组为空，使用模拟数据
        this.addHistoricalAlarms();
        return;
      }

      // 清空现有报警数据
      this.alarms = [];

      // 处理每条报警数据
      alarmList.forEach(alarm => {
        // 提取设备名称
        const deviceName = alarm.device_name || '未知设备';

        // 提取网关名称
        const gatewayName = alarm.gateway_name || '未知网关';

        // 提取传感器名称
        const sensorName = alarm.sensor_name || '未知传感器';

        // 提取报警时间
        let alarmTime = alarm.alarm_start_time || new Date().toISOString();

        // 格式化时间
        try {
          const date = new Date(alarmTime);
          alarmTime = this.formatDate(date);
        } catch (e) {
          console.warn('时间格式化失败:', e);
        }

        // 提取报警状态
        let status = '待处理';
        if (alarm.alarm_status === 1) {
          status = '已处理';
        } else if (alarm.alarm_status === 2) {
          status = '已忽略';
        }

        // 提取报警值和单位
        const value = alarm.alarm_value || '';
        const unit = alarm.unit || '';

        // 创建报警对象
        const alarmObj = {
          id: alarm.alarm_id || `alarm-${this.alarms.length}`,
          device: deviceName,
          gateway: gatewayName,
          sensor: sensorName,
          time: alarmTime,
          status: status,
          value: value,
          unit: unit,
          threshold: alarm.threshold_value,
          description: alarm.description || '',
          level: alarm.alarm_level || 1,
          type: alarm.alarm_type || 1,
          name: alarm.alarm_name || '未知报警'
        };

        // 添加到报警列表
        this.alarms.push(alarmObj);
      });

      // 按时间排序，最新的在前面
      this.sortAlarms();
    },

    // 启动API数据刷新定时器
    startApiDataRefresh() {
      // 每30秒刷新一次API数据
      this.apiRefreshTimer = setInterval(() => {
        this.fetchAlarmData();
      }, 30000); // 30秒
    },

    // 模拟数据生成（当API不可用时使用）
    startDataSimulation() {
      // 添加一些初始报警数据
      this.addHistoricalAlarms();
    },
    addHistoricalAlarms() {
      // 清空现有报警数据
      this.alarms = [];

      // 添加与图片一致的报警数据
      this.alarms.push({
        device: '左边设备',
        sensor: '环境温度',
        time: '2025/5/9 18:08:40',
        status: '待处理',
        value: '38.5'
      });

      // 添加一些其他的报警数据
      const now = new Date();
      const today = this.formatDate(now).split(' ')[0]; // 只取日期部分

      // 添加今天的一些报警
      const todayAlarms = [
        {
          device: '主桥塔',
          sensor: '环境风速',
          time: `${today} ${this.formatTime(9, 15, 22)}`,
          status: '已处理',
          value: '8.2'
        },
        {
          device: '右边设备',
          sensor: '设备温度',
          time: `${today} ${this.formatTime(10, 30, 45)}`,
          status: '待处理',
          value: '72.5'
        },
        {
          device: '监控站',
          sensor: '环境湿度',
          time: `${today} ${this.formatTime(11, 45, 10)}`,
          status: '已处理',
          value: '82.3'
        },
        {
          device: '辅助塔',
          sensor: '大气压力',
          time: `${today} ${this.formatTime(14, 20, 35)}`,
          status: '待处理',
          value: '108.5'
        }
      ];

      this.alarms = this.alarms.concat(todayAlarms);

      // 添加昨天的一些未处理报警
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = this.formatDate(yesterday).split(' ')[0]; // 只取日期部分

      const yesterdayAlarms = [
        {
          device: '主桥塔',
          sensor: '环境噪声',
          time: `${yesterdayStr} ${this.formatTime(8, 30, 15)}`,
          status: '待处理',
          value: '75.8'
        },
        {
          device: '左边设备',
          sensor: '设备压力',
          time: `${yesterdayStr} ${this.formatTime(16, 45, 30)}`,
          status: '待处理',
          value: '2.85'
        }
      ];

      this.alarms = this.alarms.concat(yesterdayAlarms);

      // 按时间排序，最新的在前面
      this.sortAlarms();
    },

    // 格式化时间辅助方法
    formatTime(hours, minutes, seconds) {
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    },
    addNewAlarm() {
      const now = new Date();
      const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
      const randomSensor = this.sensors[Math.floor(Math.random() * this.sensors.length)];

      // 生成一个超出阈值的随机值
      const value = this.getRandomValue(randomSensor);

      this.alarms.unshift({
        device: randomDevice,
        sensor: randomSensor,
        time: this.formatDate(now),
        status: '待处理',
        value: value
      });

      // 如果报警太多，移除最旧的
      if (this.alarms.length > 10) {
        this.alarms.pop();
      }
    },
    processRandomAlarm() {
      // 找到一个待处理的报警
      const pendingAlarms = this.alarms.filter(alarm => alarm.status === '待处理');
      if (pendingAlarms.length > 0) {
        const randomIndex = Math.floor(Math.random() * pendingAlarms.length);
        const alarmIndex = this.alarms.findIndex(alarm => alarm === pendingAlarms[randomIndex]);

        if (alarmIndex !== -1) {
          // 80%的概率处理，20%的概率忽略
          this.alarms[alarmIndex].status = Math.random() > 0.2 ? '已处理' : '已忽略';
        }
      }
    },
    sortAlarms() {
      // 按时间排序，最新的在前面
      this.alarms.sort((a, b) => {
        return new Date(b.time) - new Date(a.time);
      });
    }
  }
}
</script>

<style scoped>
.alarm-info-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 2px;
  background: transparent !important;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 168, 255, 0.3) rgba(0, 30, 60, 0.1);
}

/* 自定义滚动条样式 */
.alarm-info-container::-webkit-scrollbar {
  width: 4px;
}

.alarm-info-container::-webkit-scrollbar-track {
  background: rgba(0, 30, 60, 0.1) !important;
  border-radius: 2px;
}

.alarm-info-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 168, 255, 0.15) !important;
  border-radius: 2px;
}

/* 报警统计信息样式优化 */
.alarm-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  padding: 0;
  gap: 4px;
}

.alarm-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.1) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.05) !important;
  transition: all 0.3s ease;
  backdrop-filter: blur(1px);
}

.alarm-stat-item:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 168, 255, 0.25) !important;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.15) !important;
}

.alarm-stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

.alarm-total {
  color: #7fdbff;
}

.status-pending {
  color: #ffaa00;
  text-shadow: 0 0 5px rgba(255, 170, 0, 0.3) !important;
}

.status-processed {
  color: #00ff9d;
  text-shadow: 0 0 5px rgba(0, 255, 157, 0.3) !important;
}

.alarm-stat-label {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
}

/* 模块统计信息样式优化 */
.module-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  padding: 4px 8px;
  border: 1px solid rgba(0, 168, 255, 0.1) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.05) !important;
  backdrop-filter: blur(1px);
}

.module-stat-item {
  display: flex;
  align-items: center;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
}

.module-stat-item i {
  margin-right: 4px;
  color: #00a8ff;
  font-size: 0.7rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

/* 报警分类标签样式优化 */
.alarm-tabs {
  display: flex;
  margin-bottom: 6px;
  gap: 4px;
}

.alarm-tab {
  flex: 1;
  padding: 4px 8px;
  text-align: center;
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.1) !important;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(1px);
}

.alarm-tab i {
  margin-right: 4px;
  font-size: 0.8rem;
}

.alarm-tab:hover {
  background-color: rgba(0, 40, 80, 0.15) !important;
  border-color: rgba(0, 168, 255, 0.25) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
}

.alarm-tab.active {
  background-color: rgba(0, 168, 255, 0.08) !important;
  border-color: rgba(0, 168, 255, 0.15) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

/* 处理状态提示样式优化 */
.status-notification {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  transition: all 0.3s;
  animation: fadeIn 0.3s ease-in-out;
  backdrop-filter: blur(1px);
}

.status-notification i {
  margin-right: 8px;
  font-size: 1rem;
}

.status-notification.processing {
  background-color: rgba(0, 168, 255, 0.08) !important;
  border: 1px solid rgba(0, 168, 255, 0.15) !important;
  color: #00a8ff;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.05) !important;
}

.status-notification.success {
  background-color: rgba(0, 255, 157, 0.08) !important;
  border: 1px solid rgba(0, 255, 157, 0.15) !important;
  color: #00ff9d;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.05) !important;
  animation: fadeInOut 3s ease-in-out;
}

.status-notification.error {
  background-color: rgba(255, 85, 85, 0.08) !important;
  border: 1px solid rgba(255, 85, 85, 0.15) !important;
  color: #ff5555;
  box-shadow: 0 0 5px rgba(255, 85, 85, 0.05) !important;
}

/* 报警卡片容器样式优化 */
.alarm-cards-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 报警卡片样式优化 */
.alarm-card {
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.1) !important;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.05) !important;
  backdrop-filter: blur(1px);
}

.alarm-card:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 168, 255, 0.25) !important;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.15) !important;
}

.alarm-card.unprocessed {
  border-color: rgba(255, 85, 85, 0.15) !important;
  box-shadow: 0 0 5px rgba(255, 85, 85, 0.08) !important;
  animation: alarm-pulse 2s infinite;
}

@keyframes alarm-pulse {
  0% { border-color: rgba(255, 85, 85, 0.15); box-shadow: 0 0 5px rgba(255, 85, 85, 0.08); }
  50% { border-color: rgba(255, 85, 85, 0.25); box-shadow: 0 0 8px rgba(255, 85, 85, 0.15); }
  100% { border-color: rgba(255, 85, 85, 0.15); box-shadow: 0 0 5px rgba(255, 85, 85, 0.08); }
}

/* 报警卡片头部样式优化 */
.alarm-card-header {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  background-color: rgba(0, 40, 80, 0.15) !important;
  border-bottom: 1px solid rgba(0, 168, 255, 0.1) !important;
}

.device-icon {
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(0, 168, 255, 0.08) !important;
  margin-right: 6px;
  color: #7fdbff;
  font-size: 0.7rem;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-name {
  font-size: 0.75rem;
  font-weight: bold;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

.gateway-name {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.alarm-time {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 6px;
  white-space: nowrap;
}

.alarm-time i {
  margin-right: 2px;
  color: #7fdbff;
}

.status-badge {
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 0.6rem;
}

.status-badge.pending {
  background-color: rgba(255, 170, 0, 0.08) !important;
  color: #ffaa00;
  box-shadow: 0 0 5px rgba(255, 170, 0, 0.15) !important;
}

.status-badge.processed {
  background-color: rgba(0, 255, 157, 0.08) !important;
  color: #00ff9d;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.15) !important;
}

.status-badge.ignored {
  background-color: rgba(127, 127, 127, 0.08) !important;
  color: #cccccc;
  box-shadow: 0 0 5px rgba(127, 127, 127, 0.15) !important;
}

/* 报警卡片主体样式优化 */
.alarm-card-body {
  display: flex;
  padding: 6px;
}

.sensor-icon {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(0, 168, 255, 0.08) !important;
  margin-right: 8px;
  color: #7fdbff;
  font-size: 0.8rem;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
}

.sensor-info {
  flex: 1;
  min-width: 0;
}

.sensor-name {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sensor-value-row {
  display: flex;
  align-items: center;
  gap: 6px;
}

.alarm-value {
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

.alarm-value.warning {
  color: #ffaa00;
  text-shadow: 0 0 5px rgba(255, 170, 0, 0.3) !important;
}

.alarm-value.critical {
  color: #ff5555;
  text-shadow: 0 0 5px rgba(255, 85, 85, 0.3) !important;
}

.alarm-description {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.7);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-indicator {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  padding-left: 6px;
}

.status-indicator span {
  font-size: 0.65rem;
  padding: 2px 4px;
  border-radius: 3px;
  white-space: nowrap;
}

.status-indicator.pending span {
  background-color: rgba(255, 170, 0, 0.08) !important;
  color: #ffaa00;
  border: 1px solid rgba(255, 170, 0, 0.15) !important;
}

.status-indicator.processed span {
  background-color: rgba(0, 255, 157, 0.08) !important;
  color: #00ff9d;
  border: 1px solid rgba(0, 255, 157, 0.15) !important;
}

.status-indicator.ignored span {
  background-color: rgba(127, 127, 127, 0.08) !important;
  color: #cccccc;
  border: 1px solid rgba(127, 127, 127, 0.15) !important;
}

.alarm-actions {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.action-btn {
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 1px solid;
  font-size: 0.65rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.process-btn {
  background-color: rgba(0, 255, 157, 0.08) !important;
  border-color: rgba(0, 255, 157, 0.15) !important;
  color: #00ff9d;
}

.process-btn:hover {
  background-color: rgba(0, 255, 157, 0.15) !important;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.15) !important;
}

.ignore-btn {
  background-color: rgba(127, 127, 127, 0.08) !important;
  border-color: rgba(127, 127, 127, 0.15) !important;
  color: #cccccc;
}

.ignore-btn:hover {
  background-color: rgba(127, 127, 127, 0.15) !important;
  box-shadow: 0 0 5px rgba(127, 127, 127, 0.15) !important;
}

.processing-btn {
  background-color: rgba(0, 168, 255, 0.08) !important;
  border-color: rgba(0, 168, 255, 0.15) !important;
  color: #00a8ff;
  cursor: not-allowed;
}

/* 加载状态卡片样式优化 */
.loading-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.1) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.05) !important;
  color: #7fdbff;
  backdrop-filter: blur(1px);
}

.loading-card i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.3) !important;
}

.loading-card span {
  font-size: 0.8rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

/* 错误状态卡片样式优化 */
.error-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: rgba(0, 30, 60, 0.08) !important;
  border-radius: 4px;
  border: 1px solid rgba(255, 85, 85, 0.15) !important;
  box-shadow: 0 0 5px rgba(255, 85, 85, 0.08) !important;
  color: #ff5555;
  padding: 10px;
  backdrop-filter: blur(1px);
}

.error-card i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(255, 85, 85, 0.3) !important;
}

.error-card span {
  font-size: 0.8rem;
  text-align: center;
  margin-bottom: 8px;
  text-shadow: 0 0 5px rgba(255, 85, 85, 0.3) !important;
}

.retry-btn {
  background-color: rgba(0, 168, 255, 0.08) !important;
  border: 1px solid rgba(0, 168, 255, 0.15) !important;
  color: #00a8ff;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.retry-btn:hover {
  background-color: rgba(0, 168, 255, 0.15) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
}

/* 无数据状态卡片样式优化 */
.no-data-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: rgba(0, 30, 60, 0.05) !important;
  border-radius: 4px;
  border: 1px dashed rgba(0, 168, 255, 0.1) !important;
  color: rgba(127, 219, 255, 0.6);
  backdrop-filter: blur(1px);
}

.no-data-card i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  animation: float 3s ease-in-out infinite;
}

.no-data-card span {
  font-size: 0.8rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-10px); }
  20% { opacity: 1; transform: translateY(0); }
  80% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}
</style>
