// 创建数字雨效果
export function createDigitalRain() {
  const digitalRain = document.querySelector('.digital-rain');
  if (!digitalRain) return;

  // 清空现有内容
  digitalRain.innerHTML = '';

  // 创建数字雨列 - 增加列数以覆盖更大区域
  const columnCount = Math.floor(window.innerWidth / 20); // 每20px一列，增加密度

  for (let i = 0; i < columnCount; i++) {
    const column = document.createElement('div');
    column.className = 'rain-column';

    // 扩大分布范围，确保覆盖整个屏幕
    column.style.left = `${i * 20 - window.innerWidth/2 + Math.random() * 10}px`;

    // 随机字符 - 增加更多字符
    const chars = '01';
    let text = '';
    const length = 8 + Math.floor(Math.random() * 20); // 增加长度

    for (let j = 0; j < length; j++) {
      text += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    column.textContent = text;

    // 随机动画持续时间和延迟 - 增加变化
    const duration = 15 + Math.random() * 25; // 增加持续时间
    const delay = Math.random() * 20;

    column.style.animationDuration = `${duration}s`;
    column.style.animationDelay = `${delay}s`;

    // 随机透明度
    column.style.opacity = 0.1 + Math.random() * 0.3;

    digitalRain.appendChild(column);
  }
}

// 创建光晕效果
export function createGlowEffects() {
  const techBackground = document.querySelector('.tech-background');
  if (!techBackground) return;

  // 添加4个光晕效果
  for (let i = 0; i < 4; i++) {
    const glowEffect = document.createElement('div');
    glowEffect.className = 'glow-effect';

    // 随机位置
    if (i === 0) {
      // 第一个光晕固定在左上
      glowEffect.style.top = '10%';
      glowEffect.style.left = '10%';
    } else if (i === 1) {
      // 第二个光晕固定在右下
      glowEffect.style.top = '60%';
      glowEffect.style.left = '80%';
    } else {
      // 其他光晕随机位置
      glowEffect.style.top = `${Math.random() * 80}%`;
      glowEffect.style.left = `${Math.random() * 80}%`;
    }

    // 随机大小
    const size = 400 + Math.random() * 300;
    glowEffect.style.width = `${size}px`;
    glowEffect.style.height = `${size}px`;

    // 随机动画延迟
    glowEffect.style.animationDelay = `-${Math.random() * 20}s`;

    techBackground.appendChild(glowEffect);
  }
}

// 初始化背景效果
export function initTechBackground() {
  createGlowEffects();
  createDigitalRain();

  // 窗口大小变化时重新创建背景效果
  window.addEventListener('resize', () => {
    // 清除现有效果
    const techBackground = document.querySelector('.tech-background');
    if (techBackground) {
      // 确保数字雨容器存在
      techBackground.querySelector('.digital-rain');

      // 移除所有光晕效果
      const glowEffects = techBackground.querySelectorAll('.glow-effect');
      glowEffects.forEach(effect => effect.remove());

      // 重新创建效果
      createGlowEffects();
      createDigitalRain();
    }
  });

  // 全屏变化时重新创建背景效果
  document.addEventListener('fullscreenchange', () => {
    setTimeout(() => {
      createGlowEffects();
      createDigitalRain();
    }, 500);
  });
}
