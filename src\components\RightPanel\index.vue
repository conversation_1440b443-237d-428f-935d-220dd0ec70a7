<template>
  <div ref="rightPanel" :class="{show:show}" class="rightPanel-container" v-show="!isFullscreen">
    <div class="rightPanel-background"/>
    <div class="rightPanel">
      <div v-drag class="handle-button" :style="{'top':buttonTop+'px','background-color':theme}" @click="show=!show">
        <i :style="{color:variables.menuActiveText||'#fff'}" :class="show?'el-icon-close':'el-icon-setting'"/>
      </div>
      <div class="rightPanel-items">
        <slot/>
      </div>
    </div>
  </div>
</template>

<script>
import {addClass, removeClass} from '@/utils'
import variables from '@/styles/variables.scss'
export default {
  name: 'RightPanel',
  props: {
    clickNotClose: {
      default: false,
      type: Boolean
    },
    buttonTop: {
      default: 250,
      type: Number
    }
  },
  data() {
    return {
      show: false,
      isFullscreen: false
    }
  },
  directives: {
    drag: {
      // 指令的定义
      bind: function (el) {
        let oDiv = el;  // 获取当前元素
        oDiv.onmousedown = (e) => {
          // 算出鼠标相对元素的位置
          let disX = e.clientX - oDiv.offsetLeft;
          let disY = e.clientY - oDiv.offsetTop;
          document.onmousemove = (e) => {
            // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
            let left = e.clientX - disX;
            let top = e.clientY - disY;
            // oDiv.style.left = left + 'px';
            oDiv.style.top = top + 'px';
          };
          document.onmouseup = (e) => {
            document.onmousemove = null;
            document.onmouseup = null;
          }
        }
      }
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    },
    variables() {
      return variables
    },
  },
  watch: {
    show(value) {
      if (value && !this.clickNotClose) {
        this.addEventClick()
      }
      if (value) {
        addClass(document.body, 'showRightPanel')
      } else {
        removeClass(document.body, 'showRightPanel')
      }
    }
  },
  mounted() {
    this.insertToBody()

    // 监听全屏变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)

    // 初始检查全屏状态
    this.checkFullscreenState()
  },
  beforeDestroy() {
    const elx = this.$refs.rightPanel
    elx.remove()

    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  methods: {
    addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    closeSidebar(evt) {
      const parent = evt.target.closest('.rightPanel')
      if (!parent) {
        this.show = false
        window.removeEventListener('click', this.closeSidebar)
      }
    },
    insertToBody() {
      const elx = this.$refs.rightPanel
      const body = document.querySelector('body')
      body.insertBefore(elx, body.firstChild)
    },

    // 处理全屏变化事件
    handleFullscreenChange() {
      this.checkFullscreenState()
    },

    // 检查全屏状态
    checkFullscreenState() {
      // 检查全屏状态 - 兼容不同浏览器
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      )

      console.log('RightPanel: 全屏状态变化为', this.isFullscreen ? '全屏' : '非全屏')

      // 如果进入全屏模式，关闭设置面板
      if (this.isFullscreen && this.show) {
        this.show = false
      }
    }
  }
}
</script>

<style>
.showRightPanel {
  overflow: hidden;
  position: relative;
  width: calc(100% - 15px);
}
</style>

<style lang="scss" scoped>
.rightPanel-background {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
  background: rgba(0, 0, 0, .2);
  z-index: -1;
}

.rightPanel {
  width: 100%;
  max-width: 260px;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);
  transition: all .25s cubic-bezier(.7, .3, .1, 1);
  transform: translate(100%);
  background: #fff;
  z-index: 40000;
}

.show {
  transition: all .3s cubic-bezier(.7, .3, .1, 1);

  .rightPanel-background {
    z-index: 20000;
    opacity: 1;
    width: 100%;
    height: 100%;
  }

  .rightPanel {
    transform: translate(0);
  }
}

.handle-button {
  width: 48px;
  height: 48px;
  position: absolute;
  //top: 60% !important;
  left: -48px;
  text-align: center;
  font-size: 24px;
  border-radius: 6px 0 0 6px !important;
  box-shadow: 0 0 3px #dedede;
  z-index: 0;
  pointer-events: auto;
  cursor: pointer;
  color: #fff;
  line-height: 48px;

  // 在全屏模式下隐藏
  body.fullscreen-mode &,
  html.fullscreen-mode &,
  body.fullscreen-active &,
  html.fullscreen-active & {
    display: none !important;
  }

  i {
    font-size: 24px;
    line-height: 48px;
  }
}
</style>
