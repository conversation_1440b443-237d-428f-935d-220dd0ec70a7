/* 环境监控中的摄像头样式 */
.env-camera-card {
  grid-row: span 1;
}

.env-camera-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  height: 100%;
  padding: 10px;
}

.env-camera-item {
  position: relative;
  height: 100px;
  overflow: hidden;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: transform 0.2s;
}

.env-camera-item:hover {
  transform: scale(1.02);
  box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
}

.env-camera-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.env-camera-preview.no-stream {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.env-camera-preview .no-stream-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.5);
}

.env-camera-preview .no-stream-message i {
  font-size: 24px;
  margin-bottom: 5px;
}

.env-camera-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 5px 8px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
}

.env-camera-name {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.env-camera-status {
  font-size: 10px;
  display: flex;
  align-items: center;
}

.env-camera-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.env-camera-status.online .status-dot {
  background-color: #67C23A;
  box-shadow: 0 0 5px #67C23A;
}

.env-camera-status.offline .status-dot {
  background-color: #F56C6C;
  box-shadow: 0 0 5px #F56C6C;
}

/* 环境监控模块样式 */
.env-monitor-item {
  background-color: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(0, 150, 255, 0.3);
  transition: all 0.25s ease;
}

.env-monitor-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 150, 255, 0.6);
}

.env-monitor-content {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 6px;
  background: linear-gradient(135deg, rgba(0, 40, 80, 0.7), rgba(0, 20, 40, 0.9));
}

.env-monitor-info {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
}

.env-monitor-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 20, 40, 0.4);
  border: 1px dashed rgba(0, 150, 255, 0.3);
  min-height: 150px;
  grid-column: span 2;
}

/* 平铺样式修改 */
.camera-grid.camera-grid-four {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto;
  gap: 15px;
  height: calc(100% - 40px);
  width: 100%;
  padding: 10px;
  grid-auto-flow: row;
  grid-auto-rows: minmax(160px, auto);
}

/* 卡片数量适配样式 */
.camera-grid.camera-grid-four.items-1 {
  /* 只有1个卡片时 */
  grid-template-columns: 1fr;
}

.camera-grid.camera-grid-four.items-2 {
  /* 有2个卡片时横向排列 */
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
}

/* 响应式调整 */
@media (min-width: 1440px) {
  .camera-grid.camera-grid-four {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: auto;
  }
  
  .camera-grid.camera-grid-four.items-2 {
    /* 大屏时2个卡片也是横向排列 */
    grid-template-columns: repeat(2, 1fr);
  }
  
  .camera-grid.camera-grid-four.items-3 {
    /* 大屏时3个卡片的排列 */
    grid-template-columns: repeat(3, 1fr);
  }
}

.camera-item.env-monitor-item {
  background-color: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(0, 150, 255, 0.3);
  transition: all 0.25s ease;
  height: 100%;
  min-height: 140px;
  max-height: 200px;
  display: flex;
  flex-direction: column;
}

.camera-item.env-monitor-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 150, 255, 0.6);
}

.camera-item.env-monitor-item .camera-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.env-monitor-content {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 6px;
  background: linear-gradient(135deg, rgba(0, 40, 80, 0.7), rgba(0, 20, 40, 0.9));
}

.env-monitor-info {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
}

/* 修改环境卡片项目样式，简化平铺布局 */
.env-card-item {
  display: flex;
  align-items: center;
  padding: 3px;
  background-color: rgba(0, 50, 100, 0.3);
  border-radius: 4px;
  transition: all 0.3s;
  height: 100%;
}

.env-item-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.env-item-info {
  flex: 1;
}

.env-item-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
}

.env-item-value {
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 200, 255, 0.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 图标样式 */
.env-item-icon.temp {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ff4b4b' viewBox='0 0 24 24'%3E%3Cpath d='M15 13V5c0-1.7-1.3-3-3-3S9 3.3 9 5v8c-1.2 1.1-2 2.7-2 4.5 0 3.3 2.7 6 6 6s6-2.7 6-6c0-1.8-.8-3.4-2-4.5M12 4c.6 0 1 .4 1 1v3H11V5c0-.6.4-1 1-1z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(255, 75, 75, 0.5));
}

.env-item-icon.humidity {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234091f7' viewBox='0 0 24 24'%3E%3Cpath d='M12 2c.5 0 1 .4 1 1v7.6c.6.6 1 1.4 1 2.4 0 1.7-1.3 3-3 3s-3-1.3-3-3c0-1 .4-1.8 1-2.4V3c0-.6.5-1 1-1m7 11c1.1 0 2 .9 2 2 0 1-1.8 3.4-2 3.8-.2-.4-2-2.8-2-3.8 0-1.1.9-2 2-2M8 17c1.1 0 2 .9 2 2 0 1-1.8 3.4-2 3.8-.2-.4-2-2.8-2-3.8 0-1.1.9-2 2-2z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(64, 145, 247, 0.5));
}

.env-item-icon.pressure {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2357cd85' viewBox='0 0 24 24'%3E%3Cpath d='M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10 10 10 0 0 0 10-10A10 10 0 0 0 12 2m0 2a8 8 0 0 1 8 8 8 8 0 0 1-8 8 8 8 0 0 1-8-8 8 8 0 0 1 8-8m0 2a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6m-1 2h2v6h-2V8m0 7h2v2h-2v-2z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(87, 205, 133, 0.5));
}

.env-item-icon.wind {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffc100' viewBox='0 0 24 24'%3E%3Cpath d='M4 10h12v2H4v-2M4 6h12v2H4V6m4 10h8v2H8v-2m12-8a2 2 0 0 1-2 2V4a2 2 0 0 1 2 2Z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(255, 193, 0, 0.5));
} 