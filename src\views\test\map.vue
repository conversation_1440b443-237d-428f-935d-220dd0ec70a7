<template>
  <div class="map-test-container">
    <div class="control-panel">
      <h3>地图测试控制面板</h3>
      
      <el-form :model="mapControls" label-width="120px" size="small">
        <el-form-item label="地图区域代码">
          <el-input v-model.number="mapControls.adCode" placeholder="地区代码，默认100000(中国)"></el-input>
        </el-form-item>
        
        <el-form-item label="中心点坐标">
          <el-col :span="11">
            <el-input v-model.number="mapControls.centerX" placeholder="经度"></el-input>
          </el-col>
          <el-col :span="2" style="text-align: center">-</el-col>
          <el-col :span="11">
            <el-input v-model.number="mapControls.centerY" placeholder="纬度"></el-input>
          </el-col>
        </el-form-item>
        
        <el-form-item label="缩放比例">
          <el-slider v-model="mapControls.scale" :min="10" :max="300" :step="10"></el-slider>
        </el-form-item>
        
        <el-form-item label="标记点显示">
          <el-switch
            v-model="showMarkers"
            active-text="显示标记点"
            inactive-text="隐藏标记点"
            @change="toggleMarkers"
          ></el-switch>
        </el-form-item>
        
        <el-form-item label="地图主题">
          <el-select v-model="mapTheme" placeholder="选择地图主题">
            <el-option label="默认主题" value="default"></el-option>
            <el-option label="黑暗主题" value="dark"></el-option>
            <el-option label="蓝色主题" value="blue"></el-option>
            <el-option label="绿色主题" value="green"></el-option>
          </el-select>
          <el-button type="text" style="margin-left: 10px;" @click="applyMapTheme">应用主题</el-button>
        </el-form-item>
        
        <el-form-item label="自定义颜色">
          <el-color-picker v-model="customColor" @change="applyCustomColor"></el-color-picker>
          <el-button type="primary" size="small" style="margin-left: 10px;" @click="applyRandomColors">随机颜色</el-button>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="applyMapSettings">应用设置</el-button>
          <el-button @click="resetMapSettings">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="map-stats">
        <h4>地图统计信息</h4>
        <div class="stat-item">
          <span class="label">省份数量:</span>
          <span class="value">{{ mapStats.provinces }}</span>
        </div>
        <div class="stat-item">
          <span class="label">总面积:</span>
          <span class="value">{{ formattedTotalArea }}</span>
        </div>
      </div>
      
      <div class="marker-manager">
        <h4>标记点管理 <el-button type="text" icon="el-icon-plus" @click="showAddMarkerForm = true">添加</el-button></h4>
        
        <el-collapse-transition>
          <div v-if="showAddMarkerForm" class="add-marker-form">
            <el-form :model="newMarker" label-width="80px" size="small">
              <el-form-item label="名称" required>
                <el-input v-model="newMarker.name" placeholder="标记点名称"></el-input>
              </el-form-item>
              <el-form-item label="经度" required>
                <el-input v-model="newMarker.longitude" placeholder="经度坐标，如: 116.4074"></el-input>
              </el-form-item>
              <el-form-item label="纬度" required>
                <el-input v-model="newMarker.latitude" placeholder="纬度坐标，如: 39.9042"></el-input>
              </el-form-item>
              <el-form-item label="颜色">
                <el-color-picker v-model="newMarker.color"></el-color-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="addNewMarker">确认添加</el-button>
                <el-button size="small" @click="showAddMarkerForm = false">取消</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-transition>
        
        <div class="marker-list">
          <div v-for="(marker, index) in markers" :key="index" class="marker-item">
            <div class="marker-color" :style="{backgroundColor: marker.color}"></div>
            <div class="marker-name">{{ marker.name }}</div>
            <div class="marker-position">[{{ marker.position[0].toFixed(2) }}, {{ marker.position[1].toFixed(2) }}]</div>
            <el-button 
              type="danger" 
              size="mini" 
              icon="el-icon-delete" 
              circle
              @click="removeMarker(index)"
            ></el-button>
          </div>
          <div v-if="markers.length === 0" class="no-markers">
            暂无标记点，请点击"添加"按钮创建
          </div>
        </div>
      </div>
      
      <div class="map-tips">
        <h4>使用说明</h4>
        <p>1. 双击地图区域可以自动进入下一级行政区</p>
        <p>2. 鼠标滚轮可以缩放地图</p>
        <p>3. 按住鼠标左键可以拖拽地图</p>
        <p>4. 区域代码参考：<el-link type="primary" href="https://geo.datav.aliyun.com/areas_v3/bound/index.html" target="_blank">地区编码查询</el-link></p>
      </div>
    </div>
    
    <div class="map-container" ref="mapContainer" :class="'theme-' + mapTheme">
      <div v-if="geoJson" class="map-content">
        <Map3D 
          ref="map3dComponent"
          :key="mapThemeKey"
          :geoJson="geoJson" 
          :dblClickFn="handleMapDblClick" 
          :projectionFnParam="projectionFnParam"
        />
      </div>
      <div v-else-if="mapError" class="map-guide">
        <el-alert
          title="地图数据加载失败"
          type="warning"
          description="由于CORS策略限制，无法从远程服务器加载地图数据。请尝试以下解决方案之一："
          show-icon
          :closable="false"
        >
        </el-alert>
        <div class="guide-steps">
          <h4>方案一：本地文件</h4>
          <ol>
            <li>从 <a href="https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json" target="_blank">此链接</a> 下载中国地图数据</li>
            <li>将下载的文件保存为 <code>public/assets/map/100000_full.json</code></li>
            <li>重新加载页面</li>
          </ol>
          
          <h4>方案二：使用代理服务器</h4>
          <ol>
            <li>应用已配置好的代理服务器 <code>/api/map</code> → <code>https://geo.datav.aliyun.com/areas_v3/bound</code></li>
            <li>如果代理服务器仍无法访问，可能是因为您的开发服务器未启用代理配置</li>
            <li>确保运行 <code>npm run dev</code> 启动包含代理配置的开发服务器</li>
          </ol>
        </div>
      </div>
      <div v-else class="loading-container">
        <el-loading type="primary" text="加载地图数据中..."></el-loading>
      </div>
    </div>
  </div>
</template>

<script>
import Map3D from '@/components/Map3D/map3d'
import axios from 'axios'

// 地图放大倍率
const MapScale = {
  province: 100,
  city: 150,
  district: 300,
}

export default {
  name: 'MapTest',
  components: {
    Map3D
  },
  data() {
    return {
      geoJson: null,
      mapControls: {
        adCode: 100000, // 默认中国
        centerX: 104.0,
        centerY: 37.5,
        scale: 40
      },
      projectionFnParam: {
        center: [104.0, 37.5],
        scale: 40,
      },
      loadingMap: false,
      mapError: false,
      // 添加地图统计信息
      mapStats: {
        features: 0,
        provinces: 0,
        totalArea: 0
      },
      // 添加标记点数据
      markers: [
        { name: '北京', position: [116.4074, 39.9042], color: '#FF5722', population: '2154万' },
        { name: '上海', position: [121.4737, 31.2304], color: '#2196F3', population: '2487万' },
        { name: '广州', position: [113.2644, 23.1291], color: '#4CAF50', population: '1530万' },
        { name: '深圳', position: [114.0579, 22.5431], color: '#9C27B0', population: '1756万' }
      ],
      showMarkers: true,
      mapTheme: 'default',
      mapThemeKey: 0, // 添加key用于强制刷新地图组件
      customColor: '#409EFF', // 自定义颜色选择器
      // 新增标记点表单
      newMarker: {
        name: '',
        longitude: '',
        latitude: '',
        color: '#409EFF'
      },
      showAddMarkerForm: false,
      resizeTimer: null
    }
  },
  methods: {
    // 计算地图统计信息
    calculateMapStats() {
      if (!this.geoJson || !this.geoJson.features) return
      
      // 重置统计信息
      this.mapStats = {
        features: this.geoJson.features.length,
        provinces: 0,
        totalArea: 0
      }
      
      // 统计省份数量和总面积
      this.geoJson.features.forEach(feature => {
        if (feature.properties && feature.properties.level === 'province') {
          this.mapStats.provinces++
        }
        
        // 如果有面积数据则累加
        if (feature.properties && feature.properties.area) {
          this.mapStats.totalArea += parseFloat(feature.properties.area)
        }
      })
    },
    
    // 请求地图数据
    async queryMapData(code) {
      this.loadingMap = true
      try {
        let response
        try {
          // 尝试从本地加载地图数据
          response = await axios.get(`/assets/map/${code}_full.json`)
          this.mapError = false
        } catch (localError) {
          console.warn(`未能从本地加载地图数据: ${localError.message}`)
          
          // 尝试通过代理服务器获取数据
          try {
            console.log('尝试通过代理服务器获取地图数据')
            response = await axios.get(`/api/map/${code}_full.json`)
            this.mapError = false
          } catch (proxyError) {
            console.warn(`通过代理获取地图数据失败: ${proxyError.message}`)
            this.mapError = true
            this.$message({
              message: '地图数据加载失败，请确保地图文件已下载到本地',
              type: 'warning'
            })
            this.loadingMap = false
            return false
          }
        }
        this.geoJson = response.data
        this.loadingMap = false
        
        // 计算地图统计信息
        this.calculateMapStats()
        
        // 在地图上添加标记点
        if (this.showMarkers) {
          this.addMarkersToMap()
        }
        
        return true
      } catch (error) {
        console.error('地图数据加载失败:', error)
        this.$message.error('地图数据加载失败')
        this.mapError = true
        this.loadingMap = false
        return false
      }
    },
    
    // 添加标记点到地图
    addMarkersToMap() {
      // 此功能依赖于Map3D组件是否支持该API
      // 由于Map3D组件实际上不支持直接添加标记，此处为示例功能
      console.log('标记点功能需要自行实现，当前仅做展示', this.markers)
    },
    
    // 切换标记点显示
    toggleMarkers() {
      if (this.showMarkers) {
        this.addMarkersToMap()
      } else {
        console.log('隐藏标记点功能需要自行实现')
      }
    },
    
    // 双击地图事件处理
    handleMapDblClick(customProperties) {
      this.mapControls.adCode = customProperties.adcode
      this.mapControls.centerX = customProperties.centroid[0]
      this.mapControls.centerY = customProperties.centroid[1]
      this.mapControls.scale = MapScale[customProperties.level] || 100
      
      // 应用新设置
      this.applyMapSettings()
      
      // 显示信息提示
      this.$message({
        message: `已切换到: ${customProperties.name}`,
        type: 'success'
      })
    },
    
    // 应用地图设置
    applyMapSettings() {
      // 更新投影参数
      this.projectionFnParam = {
        center: [this.mapControls.centerX, this.mapControls.centerY],
        scale: this.mapControls.scale,
      }
      
      // 重新加载地图数据
      this.queryMapData(this.mapControls.adCode)
    },
    
    // 重置地图设置
    resetMapSettings() {
      this.mapControls = {
        adCode: 100000,
        centerX: 104.0,
        centerY: 37.5,
        scale: 40
      }
      this.applyMapSettings()
    },
    
    // 添加新标记点
    addNewMarker() {
      if (!this.newMarker.name || !this.newMarker.longitude || !this.newMarker.latitude) {
        this.$message.warning('请完整填写标记点信息')
        return
      }
      
      // 添加新标记点到列表
      const marker = {
        name: this.newMarker.name,
        position: [parseFloat(this.newMarker.longitude), parseFloat(this.newMarker.latitude)],
        color: this.newMarker.color
      }
      
      this.markers.push(marker)
      
      // 重置表单
      this.newMarker = {
        name: '',
        longitude: '',
        latitude: '',
        color: '#409EFF'
      }
      
      // 关闭添加表单
      this.showAddMarkerForm = false
      
      // 刷新标记点
      if (this.showMarkers) {
        this.addMarkersToMap()
      }
      
      this.$message.success('标记点添加成功')
    },
    
    // 删除标记点
    removeMarker(index) {
      this.$confirm('确定要删除该标记点吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.markers.splice(index, 1)
        
        // 刷新标记点
        if (this.showMarkers) {
          // 重新添加所有标记点
          this.addMarkersToMap()
        }
        
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {})
    },
    
    // 获取mapConfig模块
    getMapConfig() {
      try {
        // 动态导入mapConfig
        const mapConfigModule = require('@/components/Map3D/map3d/mapConfig.js')
        return mapConfigModule.mapConfig
      } catch (error) {
        console.error('获取mapConfig失败:', error)
        return null
      }
    },
    
    // 应用地图主题
    applyMapTheme() {
      // 获取mapConfig
      const mapConfig = this.getMapConfig()
      if (!mapConfig) {
        this.$message.warning('无法获取地图配置')
        return
      }
      
      try {
        // 设置地图颜色 - 首先确保它不是null
        mapConfig.mapColor = this.currentThemeColors.area
        mapConfig.mapSideColor1 = this.currentThemeColors.border
        mapConfig.mapSideColor2 = this.currentThemeColors.border
        
        // 确保透明设置正确
        mapConfig.mapTransparent = false
        mapConfig.mapOpacity = 1.0
        
        // 重置地图组件
        this.mapThemeKey = Date.now()
        
        // 提示用户
        this.$message.success(`已应用主题: ${this.mapTheme}`)
        
        // 重新加载地图数据
        setTimeout(() => {
          this.queryMapData(this.mapControls.adCode)
        }, 100)
      } catch (error) {
        console.error('应用主题失败:', error)
        this.$message.error('应用主题失败')
      }
    },
    
    // 应用随机颜色
    applyRandomColors() {
      try {
        // 获取mapConfig
        const mapConfig = this.getMapConfig()
        if (!mapConfig) {
          this.$message.warning('无法获取地图配置')
          return
        }
        
        console.log('开始应用随机颜色...')
        
        // ===== 核心修改 =====
        // 确保mapColor为null - 注意：不是undefined，必须显式为null
        mapConfig.mapColor = null
        
        // 打乱颜色数组以获得新的随机效果
        this.shuffleColorArray(mapConfig.mapColorGradient)
        
        // 设置边框颜色为深色，使颜色对比更明显
        mapConfig.mapSideColor1 = '#000000'
        mapConfig.mapSideColor2 = '#333333'
        
        // 确保不透明，使颜色完全显示
        mapConfig.mapTransparent = false
        mapConfig.mapOpacity = 1.0
        
        // 强制重置mapThemeKey以触发Map3D组件重新创建
        this.mapThemeKey = Date.now() + Math.random()
        
        // 提示用户
        this.$message.success('正在应用随机颜色...')
        
        // 重新加载地图数据 - 核心步骤
        setTimeout(() => {
          this.queryMapData(this.mapControls.adCode)
        }, 50)
      } catch (error) {
        console.error('应用随机颜色失败:', error)
        this.$message.error('应用随机颜色失败')
      }
    },
    
    // 打乱颜色数组
    shuffleColorArray(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    },
    
    // 应用自定义颜色
    applyCustomColor(color) {
      try {
        // 获取mapConfig
        const mapConfig = this.getMapConfig()
        if (!mapConfig) {
          this.$message.warning('无法获取地图配置')
          return
        }
        
        // 设置自定义颜色
        mapConfig.mapColor = color
        
        // 修改drawFunc.js中的绘制函数
        try {
          // 打开地图绘制模块
          const drawFuncModule = require('@/components/Map3D/map3d/drawFunc.js')
          
          // 将原始的drawExtrudeMesh函数保存在可访问的地方
          const originalDrawExtrudeMesh = drawFuncModule.drawExtrudeMesh
          
          // 修改drawExtrudeMesh函数，使用自定义颜色
          drawFuncModule.drawExtrudeMesh = function(point, projectionFn) {
            const result = originalDrawExtrudeMesh(point, projectionFn)
            
            // 修改材质颜色
            if (result && result.mesh && result.mesh.material && result.mesh.material[0]) {
              result.mesh.material[0].color.set(color)
              result.mesh.material[0].needsUpdate = true
            }
            
            return result
          }
        } catch (funcError) {
          console.error('修改绘制函数失败:', funcError)
        }
        
        // 重置地图组件
        this.mapThemeKey = Date.now()
        
        // 提示用户
        this.$message.success(`已应用自定义颜色: ${color}`)
        
        // 重新加载地图数据
        setTimeout(() => {
          this.queryMapData(this.mapControls.adCode)
        }, 100)
      } catch (error) {
        console.error('应用自定义颜色失败:', error)
        this.$message.error('应用自定义颜色失败')
      }
    },
    
    // 窗口大小改变事件处理
    onWindowResize() {
      // 强制重新渲染地图
      this.mapThemeKey = Date.now()
      
      // 短暂延迟后重新加载地图数据
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        if (this.geoJson) {
          this.queryMapData(this.mapControls.adCode)
        }
      }, 500)
    },
  },
  computed: {
    // 格式化显示的总面积
    formattedTotalArea() {
      if (!this.mapStats.totalArea) return '未知'
      return (this.mapStats.totalArea / 10000).toFixed(2) + ' 万平方公里'
    },
    // 当前主题颜色
    currentThemeColors() {
      const themeColors = {
        default: {
          background: '#f5f7fa',
          area: '#e0e0e0',
          border: '#ffffff'
        },
        dark: {
          background: '#1a1a1a',
          area: '#2c2c2c',
          border: '#444444'
        },
        blue: {
          background: '#d0e9ff',
          area: '#90c7ff',
          border: '#3a84ff'
        },
        green: {
          background: '#e0f2e9',
          area: '#a8dfc0',
          border: '#5bc0a0'
        }
      }
      return themeColors[this.mapTheme] || themeColors.default
    }
  },
  mounted() {
    // 初始化加载地图数据
    this.queryMapData(this.mapControls.adCode)
    
    // 添加窗口大小改变事件，确保地图正确渲染
    window.addEventListener('resize', this.onWindowResize)
  },
  beforeDestroy() {
    // 地图资源会由Vue组件自动清理
    console.log('地图测试组件销毁')
    
    // 移除窗口大小改变事件
    window.removeEventListener('resize', this.onWindowResize)
  },
  watch: {
    // 监听主题变化
    mapTheme(newTheme) {
      // 主题变化时自动应用
      this.applyMapTheme() // 取消注释，启用主题自动应用
    }
  }
}
</script>

<style lang="scss" scoped>
.map-test-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 100px);
  background-color: var(--map-background-color, #f5f7fa);
  
  .control-panel {
    width: 300px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: #303133;
      font-size: 18px;
      text-align: center;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 10px;
    }
    
    .map-stats {
      margin-top: 20px;
      padding: 10px 15px;
      background-color: #ecf5ff;
      border-radius: 4px;
      
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #409eff;
        font-size: 16px;
      }
      
      .stat-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .label {
          color: #606266;
        }
        
        .value {
          font-weight: bold;
          color: #303133;
        }
      }
    }
    
    .marker-manager {
      margin-top: 20px;
      
      h4 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0;
        margin-bottom: 15px;
        color: #409eff;
        font-size: 16px;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 10px;
      }
      
      .add-marker-form {
        margin-bottom: 15px;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
      
      .marker-list {
        max-height: 200px;
        overflow-y: auto;
        
        .marker-item {
          display: flex;
          align-items: center;
          padding: 8px 10px;
          margin-bottom: 6px;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          
          .marker-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
          }
          
          .marker-name {
            flex: 1;
            font-weight: bold;
            margin-right: 10px;
          }
          
          .marker-position {
            color: #909399;
            font-size: 12px;
            margin-right: 10px;
          }
        }
        
        .no-markers {
          padding: 20px;
          text-align: center;
          color: #909399;
          font-size: 14px;
        }
      }
    }
    
    .map-tips {
      margin-top: 20px;
      padding: 15px;
      background-color: #f0f9eb;
      border-radius: 4px;
      
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #67c23a;
      }
      
      p {
        margin: 5px 0;
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .map-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    
    /* 设置与control-panel一致的背景色 */
    background-color: #fff;
    
    .map-content {
      width: 100%;
      height: 100%;
      
      canvas {
        /* 确保 Three.js 渲染器的背景是透明的，让容器背景显示 */
        background-color: transparent !important;
      }
    }
    
    .loading-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      
      div {
        margin-top: 15px;
        color: #909399;
      }
    }
    
    .map-guide {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      max-width: 600px;
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      z-index: 10;
      
      .guide-steps {
        margin-top: 15px;
        
        h4 {
          margin-top: 15px;
          margin-bottom: 10px;
          color: #409eff;
        }
        
        ol {
          padding-left: 20px;
          
          li {
            margin-bottom: 8px;
          }
        }
        
        code {
          background-color: #f5f7fa;
          padding: 2px 4px;
          border-radius: 4px;
          color: #409eff;
        }
      }
    }
  }
}

/* 主题样式 - 只影响容器边框，不影响地图内容 */
.theme-default {
  --map-background-color: #f5f7fa;
  --map-area-color: #e0e0e0;
  --map-border-color: #ffffff;
}

.theme-dark {
  --map-background-color: #1a1a1a;
  --map-area-color: #2c2c2c;
  --map-border-color: #444444;
}

.theme-blue {
  --map-background-color: #d0e9ff;
  --map-area-color: #90c7ff;
  --map-border-color: #3a84ff;
}

.theme-green {
  --map-background-color: #e0f2e9;
  --map-area-color: #a8dfc0;
  --map-border-color: #5bc0a0;
}

/* 全局变量用于地图主题 */
:root {
  --map-background-color: #f5f7fa;
  --map-area-color: #e0e0e0;
  --map-border-color: #999999;
}
</style>
