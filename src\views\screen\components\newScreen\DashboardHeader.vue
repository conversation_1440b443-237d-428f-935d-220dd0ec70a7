<template>
  <div class="header" style="height: 70px !important; margin: 0 !important; padding: 0 !important; display: flex !important; flex-direction: column !important; justify-content: flex-start !important; min-height: 70px !important; max-height: 70px !important; top: 0;">
    <div class="logo-section" style="padding: 0 20px !important; margin: 0 !important;">
      <div class="title-container">
        <h1 style="margin: 0 !important; padding: 0 !important; font-size: 1.1rem !important; color: white !important;">{{ projectTitle }}</h1>
        <div class="subtitle" style="margin-top: 0 !important; font-size: 0.6rem !important; color: #7fdbff !important;">{{ projectDescription }}</div>
      </div>
      <div class="system-info" style="gap: 0.5vw !important; font-size: 0.7rem !important;">
        <div class="weather-details" style="gap: 0.3vw !important;">
          <div v-if="weatherLoading" class="weather-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载天气数据...</span>
          </div>
          <div v-else-if="weatherError" class="weather-error">
            <i class="fas fa-exclamation-triangle"></i>
            <span>天气数据获取失败</span>
          </div>
          <template v-else>
            <div class="weather-detail-item location">
              <i class="fas fa-map-marker-alt"></i>
              <span class="detail-value">{{ shortCity }}</span>
            </div>
            <div class="weather-detail-item weather-condition">
              <i class="fas fa-cloud-sun"></i>
              <span class="detail-value">{{ weatherCondition }}</span>
            </div>
            <div class="weather-detail-item temperature">
              <i class="fas fa-thermometer-half"></i>
              <span class="detail-value">{{ temperature }}°C</span>
            </div>
            <div class="weather-detail-item humidity">
              <i class="fas fa-tint"></i>
              <span class="detail-value">{{ humidity }}%</span>
            </div>
            <div class="weather-detail-item pressure">
              <i class="fas fa-tachometer-alt"></i>
              <span class="detail-value">{{ pressure }} hPa</span>
            </div>
            <div class="weather-detail-item wind">
              <i class="fas fa-wind"></i>
              <span class="detail-value">{{ windSpeed }} m/s</span>
            </div>
            <div class="weather-detail-item visibility">
              <i class="fas fa-eye"></i>
              <span class="detail-value">{{ visibility }} km</span>
            </div>
          </template>
        </div>

        <div class="datetime-container">
          <div class="weather-detail-item datetime">
            <i class="fas fa-calendar-alt"></i>
            <span class="detail-value">{{ currentDate }} {{ currentTime }}</span>
          </div>
        </div>

        <button class="header-btn back-btn" title="返回地图" @click="backToMap">
          <i class="fas fa-map-marked-alt"></i>
        </button>
        <button id="fullscreen-toggle" class="header-btn fullscreen-btn" title="全屏切换" @click="$emit('toggle-fullscreen')">
          <i :class="fullscreenIcon"></i>
        </button>
      </div>
    </div>
    <nav class="main-nav" style="margin: 0 !important; padding: 0 !important; height: 30px; width: 100%;">
      <ul style="margin: 0 !important; padding: 0 !important; height: 100%; width: 100%;">
        <li v-for="(item, index) in navItems" :key="index" :class="{ active: activeNavIndex === index }"
          @click="setActiveNav(index)">
          <i :class="item.icon"></i> {{ item.text }}
        </li>
      </ul>
    </nav>

    <!-- 项目信息和报警信息区域 -->
    <slot name="project-info"></slot>
  </div>
</template>

<script>
import { getProjectWeather } from '@/api/system/weather'

/**
 * DashboardHeader 组件
 * 
 * 使用说明:
 * 1. 该组件提供了一个名为 "project-info" 的插槽，用于显示项目信息和报警信息
 * 2. 在父组件中使用此插槽时，请添加 class="project-info-section" 以应用正确的样式
 * 
 * 示例:
 * <dashboard-header>
 *   <template #project-info>
 *     <div class="project-info-section">
 *       <!-- 项目信息内容 -->
 *       <div class="project-details">...</div>
 *       <!-- 报警信息内容 -->
 *       <div class="alarm-info">...</div>
 *     </div>
 *   </template>
 * </dashboard-header>
 */
export default {
  name: 'DashboardHeader',
  props: {
    projectData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      currentDate: '',
      currentTime: '',
      city: 'Beijing',
      temperature: 21,
      weatherCondition: '晴',
      humidity: 47,
      pressure: 1013,
      windSpeed: 4.03,
      visibility: 10.0,
      weatherLoading: false,
      weatherError: false,
      weatherUpdateInterval: null,
      isFullscreen: false,
      activeNavIndex: 0,
      navItems: [
        { text: '综合展示', icon: 'fas fa-th-large' },
        { text: '设备管理', icon: 'fas fa-microchip' },
        { text: '摄像头管理', icon: 'fas fa-video' },
        // { text: '人员管理', icon: 'fas fa-users' },
        // { text: '合同文档', icon: 'fas fa-file-contract' },
        // { text: '系统设置', icon: 'fas fa-cog' }
      ]
    }
  },
  computed: {
    fullscreenIcon() {
      return this.isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
    },
    projectTitle() {
      return this.projectData && this.projectData.name
        ? this.projectData.name
        : '';
    },
    projectDescription() {
      return this.projectData && this.projectData.description
        ? this.projectData.description
        : '';
    },
    // 缩短城市名称，最多显示6个字符
    shortCity() {
      if (!this.city) return '';

      // 如果城市名称包含"市"或"区"，先去掉这些后缀
      let cityName = this.city;

      // 移除"市"、"区"、"县"等后缀
      cityName = cityName.replace(/[市区县].*$/, '');

      // 如果还是太长，截取前6个字符
      if (cityName.length > 6) {
        return cityName.substring(0, 6);
      }

      return cityName;
    }
  },
  watch: {
    projectData: {
      handler(newVal) {
        if (newVal && newVal.number) {
          // 当项目数据变化时，重新获取天气数据
          this.fetchWeatherData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.updateDateTime();
    setInterval(this.updateDateTime, 1000); // 每秒更新一次

    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler);
    document.addEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
    document.addEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
    document.addEventListener('MSFullscreenChange', this.fullscreenChangeHandler);

    // 初始化天气数据
    this.fetchWeatherData();

    // 设置定时更新天气数据 (每30分钟更新一次)
    this.weatherUpdateInterval = setInterval(this.fetchWeatherData, 30 * 60 * 1000);
  },

  beforeDestroy() {
    // 清除天气更新定时器
    if (this.weatherUpdateInterval) {
      clearInterval(this.weatherUpdateInterval);
    }

    // 移除全屏事件监听器
    document.removeEventListener('fullscreenchange', this.fullscreenChangeHandler);
    document.removeEventListener('webkitfullscreenchange', this.fullscreenChangeHandler);
    document.removeEventListener('mozfullscreenchange', this.fullscreenChangeHandler);
    document.removeEventListener('MSFullscreenChange', this.fullscreenChangeHandler);
  },
  methods: {
    updateDateTime() {
      const now = new Date();

      // 格式化日期: YYYY年MM月DD日
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      this.currentDate = `${year}年${month}月${day}日`;

      // 格式化时间: HH:MM:SS
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${hours}:${minutes}:${seconds}`;

      // 不再使用随机更新天气数据，改为通过API获取
    },

    // 获取项目天气数据
    fetchWeatherData() {
      // 检查是否有项目数据
      if (!this.projectData || !this.projectData.number) {
        console.warn('无法获取天气数据：缺少项目编号');
        this.weatherError = true;
        return;
      }

      this.weatherLoading = true;
      this.weatherError = false;

      console.log('获取项目天气数据，项目编号:', this.projectData.number);

      getProjectWeather(this.projectData.number)
        .then(response => {
          if (response && response.code === 0 && response.data) {
            console.log('获取到天气数据:', response.data);

            // 更新天气数据
            const weatherData = response.data;
            this.city = weatherData.city || 'Unknown';
            this.temperature = weatherData.temperature || 0;
            this.weatherCondition = weatherData.weather_condition || '未知';
            this.humidity = weatherData.humidity || 0;
            this.pressure = weatherData.pressure || 0;
            this.windSpeed = weatherData.wind_speed || 0;
            this.visibility = weatherData.visibility || 0;

            this.weatherError = false;
          } else {
            console.warn('天气数据API返回错误或空数据:', response);
            this.weatherError = true;
          }
        })
        .catch(error => {
          console.error('获取天气数据失败:', error);
          this.weatherError = true;
        })
        .finally(() => {
          this.weatherLoading = false;
        });
    },
    
    fullscreenChangeHandler() {
      // 检查全屏状态 - 兼容不同浏览器
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      console.log('DashboardHeader: 全屏状态变化为', this.isFullscreen ? '全屏' : '非全屏');
    },
    
    setActiveNav(index) {
      this.activeNavIndex = index;
      // 发出导航变化事件
      this.$emit('nav-change', {
        index: index,
        item: this.navItems[index]
      });
    },

    // 返回地图页面
    backToMap() {
      console.log('返回中国地图页面');

      // 检查当前是否处于全屏状态
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      console.log('当前全屏状态:', isFullscreen ? '全屏' : '非全屏');

      // 先触发返回列表事件，确保当前项目详情被关闭
      this.$emit('back-to-list');

      // 获取父组件引用，用于设置视图模式和全屏状态
      let screenComponent = null;
      if (this.$parent && this.$parent.$parent) {
        screenComponent = this.$parent.$parent;
        if (screenComponent.viewMode) {
          screenComponent.viewMode = 'map';
        }

        // 如果父组件有 isFullscreen 属性，设置它与当前全屏状态一致
        if (screenComponent.hasOwnProperty('isFullscreen')) {
          screenComponent.isFullscreen = isFullscreen;
        }
      }

      // 使用路由导航返回到地图页面
      this.$nextTick(() => {
        // 构建查询参数，保留 view=map 和全屏状态
        const query = { view: 'map' };

        // 如果当前是全屏状态，添加全屏参数
        if (isFullscreen) {
          query.fullscreen = 'true';
        }

        // 使用路由导航
        this.$router.push({
          path: '/screen',
          query: query
        }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航错误:', err);
          }
        });

        // 如果当前是全屏状态，确保返回后也是全屏状态
        if (isFullscreen && screenComponent) {
          // 给浏览器一点时间来处理导航
          setTimeout(() => {
            if (typeof screenComponent.toggleFullScreen === 'function' && !document.fullscreenElement) {
              console.log('恢复全屏状态');
              screenComponent.toggleFullScreen();
            }
          }, 300);
        }
      });
    }
  }
}
</script>

<style scoped>
.header {
  background-color: #001529; /* 深蓝色背景，匹配图片 */
  border: none; /* 移除边框，使界面更干净 */
  border-radius: 0;
  padding: 0 !important;
  margin: 0 !important;
  backdrop-filter: none;
  width: 100%;
  height: auto !important; /* 允许高度自适应内容 */
  min-height: 70px !important;
  box-sizing: border-box;
  box-shadow: none;
  z-index: 200;
  pointer-events: auto;
  transform: none;
  animation: none;
  transform-origin: top center;
  position: relative;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  top: 0; /* 确保顶格显示 */
  margin-bottom: 0 !important; /* 确保下方无间隙 */
}

/* 大屏幕模式下头部容器调整 */
.large-screen-mode .header {
  padding: 0 !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
  animation: none;
  transform: none;
  box-shadow: none;
  border: none;
  height: auto !important; /* 允许高度自适应内容 */
  min-height: 70px !important;
  top: 0;
}

.logo-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  margin: 0;
  position: relative;
  z-index: 100;
  width: 100%;
  height: 40px; /* 增加高度从30px到40px */
  background-color: #001529; /* 确保背景色一致 */
}

/* 大屏幕模式下logo区域调整 */
.large-screen-mode .logo-section {
  padding: 0 20px;
  min-height: 0;
  height: 40px; /* 增加高度从32px到40px */
}

.title-container {
  display: flex;
  flex-direction: column;
}

.logo-section h1 {
  font-size: 1.2rem;
  color: #00a8ff; /* 亮蓝色标题，匹配图片 */
  text-shadow: none;
  letter-spacing: 1px;
  margin: 0;
  padding: 0;
  font-weight: bold; /* 加粗标题 */
}

/* 大屏幕模式下标题字体大小调整 */
.large-screen-mode .logo-section h1 {
  font-size: 1rem;
  margin: 0;
  padding: 0;
  color: #00a8ff; /* 保持一致的颜色 */
  font-weight: bold;
}

.subtitle {
  font-size: 0.55rem;
  color: #7fdbff;
  letter-spacing: 0.8px;
  margin-top: 2px;
}

/* 大屏幕模式下副标题字体大小调整 */
.large-screen-mode .subtitle {
  font-size: 0.6rem;
  margin-top: 0.1rem;
  letter-spacing: 0.5px;
}

.system-info {
  display: flex;
  gap: calc(0.8vw * var(--scale-ratio, 1));
  font-size: calc(0.9rem * var(--scale-ratio, 1));
  align-items: center;
  flex-wrap: nowrap;
  position: relative;
  z-index: 100;
}

/* 大屏幕模式下系统信息字体大小调整 */
.large-screen-mode .system-info {
  font-size: 1.1rem;
  gap: 0.9vw;
}

/* 小屏幕模式下系统信息字体大小调整 */
.small-screen-mode .system-info {
  font-size: 1.1rem;
  gap: 1.8vw;
}

/* 中等屏幕模式下系统信息字体大小调整 */
.medium-screen-mode .system-info {
  font-size: 0.85rem;
  gap: 1.2vw;
}

.system-info span {
  color: #7fdbff;
  position: relative;
  white-space: nowrap;
}

.system-info i {
  margin-right: 0.3rem;
  color: #00a8ff;
}

/* 大屏幕模式下图标大小调整 */
.large-screen-mode .system-info i {
  margin-right: 0.2rem;
  font-size: 0.9em;
}

.weather-details {
  display: flex;
  gap: calc(0.3vw * var(--scale-ratio, 1));
  align-items: center;
  flex-wrap: nowrap;
  position: relative;
  z-index: 100;
}

/* 大屏幕模式下天气详情间距调整 */
.large-screen-mode .weather-details {
  gap: 0.4vw;
  font-size: 0.8rem;
}

/* 大屏幕模式下天气详情项调整 */
.large-screen-mode .weather-detail-item {
  padding: 0 0.15vw;
  white-space: nowrap;
}

/* 大屏幕模式下天气图标调整 */
.large-screen-mode .weather-detail-item i {
  font-size: 0.6rem;
  margin-right: 0.2rem;
}

/* 小屏幕模式下天气详情间距调整 */
.small-screen-mode .weather-details {
  gap: 1.5vw;
  font-size: 1.1rem;
}

/* 小屏幕模式下天气详情项调整 */
.small-screen-mode .weather-detail-item {
  padding: 0 0.4vw;
  white-space: nowrap;
}

/* 小屏幕模式下天气图标调整 */
.small-screen-mode .weather-detail-item i {
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

/* 中等屏幕模式下天气详情间距调整 */
.medium-screen-mode .weather-details {
  gap: 1vw;
  font-size: 0.85rem;
}

/* 中等屏幕模式下天气详情项调整 */
.medium-screen-mode .weather-detail-item {
  padding: 0 0.25vw;
  white-space: nowrap;
}

/* 中等屏幕模式下天气图标调整 */
.medium-screen-mode .weather-detail-item i {
  font-size: 0.85rem;
  margin-right: 0.35rem;
}

.weather-detail-item {
  display: flex;
  align-items: center;
  background-color: transparent;
  padding: calc(0.1rem * var(--scale-ratio, 1)) calc(0.2rem * var(--scale-ratio, 1));
  border: none;
  min-width: auto;
  white-space: nowrap;
  transform-origin: center center;
  transform: scale(var(--scale-ratio, 1));
}

.weather-detail-item i {
  margin-right: calc(0.3rem * var(--scale-ratio, 1));
  font-size: calc(1.1em * var(--scale-ratio, 1));
}

.detail-value {
  font-size: calc(0.9rem * var(--scale-ratio, 1));
  font-weight: 600;
  text-shadow: none;
}

.weather-loading, .weather-error {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: none;
  font-size: 0.7rem;
  padding: 0 10px;
}

.weather-loading i, .weather-error i {
  margin-right: 5px;
}

.weather-loading i {
  color: #7fdbff;
}

.weather-error i {
  color: #ff7f7f;
}

/* 不同天气指标的颜色 */
.location i,
.location .detail-value {
  color: #ffff64;
}

.weather-condition i,
.weather-condition .detail-value {
  color: #64c8ff;
}

.temperature i,
.temperature .detail-value {
  color: #ff6464;
}

.humidity i,
.humidity .detail-value {
  color: #6496ff;
}

.pressure i,
.pressure .detail-value {
  color: #ffb464;
}

.wind i,
.wind .detail-value {
  color: #64ff96;
}

.visibility i,
.visibility .detail-value {
  color: #c896ff;
}

.datetime-container {
  margin-left: auto;
  position: relative;
  z-index: 100;
  transform: scale(var(--scale-ratio, 1));
  transform-origin: center center;
  min-width: 180px;
}

.datetime {
  border: none;
  box-shadow: none;
  background-color: transparent;
  display: flex;
  align-items: center;
}

.datetime i,
.datetime .detail-value {
  color: #ffffff;
  font-size: calc(1rem * var(--scale-ratio, 1));
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.datetime .detail-value {
  position: relative;
}

.header-btn {
  background-color: rgba(0, 40, 80, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.5);
  color: #00a8ff;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  margin-left: 8px;
  font-size: 0.85rem;
  box-shadow: 0 0 4px rgba(0, 168, 255, 0.3);
  position: relative;
  z-index: 200;
}

.fullscreen-btn {
  width: auto;
  padding: 0 5px;
  animation: pulse 2s infinite;
}

.back-btn {
  width: auto;
  padding: 0 10px;
  margin-right: 10px;
  background-color: rgba(0, 80, 40, 0.7);
  border: 1px solid rgba(0, 255, 168, 0.6);
  color: #00ffa8;
  font-weight: bold;
  animation: pulse-green 2s infinite;
}

.back-btn:hover {
  background-color: rgba(0, 255, 168, 0.4);
  box-shadow: 0 0 15px rgba(0, 255, 168, 0.7);
  transform: scale(1.05);
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 168, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(0, 255, 168, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 168, 0);
  }
}

.btn-text {
  margin-left: 5px;
  font-size: 0.8rem;
}

.fullscreen-text {
  margin-left: 5px;
  font-size: 0.8rem;
}

.header-btn:hover {
  background-color: rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
}

.main-nav {
  margin: 0 !important;
  padding: 0 !important;
  height: 30px; /* 设置导航栏高度为30px */
  width: 100%;
  border-bottom: none; /* 确保底部无边框 */
  background-color: #00264d; /* 确保背景色一致 */
}

.main-nav ul {
  display: flex;
  list-style: none;
  background-color: #00264d; /* 稍微浅一点的蓝色，匹配图片 */
  margin: 0;
  padding: 0;
  position: relative;
  overflow: hidden;
  border-radius: 0;
  box-shadow: none;
  border-top: 1px solid rgba(0, 168, 255, 0.3);
  border-bottom: 1px solid rgba(0, 168, 255, 0.3); /* 确保底部无边框 */
  height: 100%;
  width: 100%;
}

.main-nav li {
  padding: 0 30px; /* 增加水平内边距，匹配图片 */
  cursor: pointer;
  border-right: 1px solid rgba(0, 168, 255, 0.3);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  text-shadow: none;
  font-size: 0.9rem;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 大屏幕模式下导航菜单调整 */
.large-screen-mode .main-nav li {
  padding: 0 30px; /* 增加水平内边距，匹配图片 */
  font-size: 0.8rem;
  border-right: 1px solid rgba(0, 168, 255, 0.3);
}

/* 大屏幕模式下导航菜单整体调整 */
.large-screen-mode .main-nav ul {
  margin: 0 !important;
  padding: 0 !important;
  border-top: 1px solid rgba(0, 168, 255, 0.3);
  width: 100%;
}

.main-nav li:hover {
  background-color: rgba(0, 168, 255, 0.3);
  color: #fff;
}

.main-nav li.active {
  background-color: rgba(0, 168, 255, 0.3);
  color: #fff;
  border-bottom: 2px solid #00a8ff; /* 添加底部边框指示活动状态，匹配图片 */
}

.main-nav li i {
  margin-right: 8px;
  font-size: 1rem; /* 增大图标大小，匹配图片 */
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 168, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(0, 168, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 168, 255, 0);
  }
}

/* 项目信息和报警信息插槽样式 */
:deep(.project-info-section) {
  display: flex;
  width: 100%;
  background-color: #001529;
  padding: 0;
  margin: 0;
  border-top: none;
}
</style>
