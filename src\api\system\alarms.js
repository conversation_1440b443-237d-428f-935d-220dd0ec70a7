import request from '@/utils/axios'

// 获取项目报警数据
export function getProjectAlarmsData(projectId) {
  console.log('Calling alarms API for project:', projectId);

  // 确保使用小写的方法名
  return request({
    path: `/api/system/alarms/project/${projectId}`,
    method: 'get'
  }).then(response => {
    console.log('Alarms API response received:', response);
    return response;
  }).catch(error => {
    console.error('Alarms API error:', error);
    throw error;
  });
}

// 更新告警状态
export function updateAlarmStatus(alarmId, status, handleMethod = '', handleResult = '') {
  console.log(`Updating alarm status: ID=${alarmId}, status=${status}`);

  // 状态值映射
  const statusMap = {
    'processed': 1, // 已处理
    'ignored': 2,   // 已忽略
    'pending': 0    // 待处理
  };

  // 确保状态值有效
  const alarmStatus = statusMap[status] !== undefined ? statusMap[status] : status;

  // 构建请求数据
  const data = {
    alarm_status: alarmStatus,
    handle_method: handleMethod,
    handle_result: handleResult,
    handle_time: new Date().toISOString()
  };

  return request({
    path: `/api/system/alarms/update/${alarmId}`,
    method: 'post',
    data
  }).then(response => {
    console.log('Alarm status update response:', response);
    return response;
  }).catch(error => {
    console.error('Alarm status update error:', error);
    throw error;
  });
}