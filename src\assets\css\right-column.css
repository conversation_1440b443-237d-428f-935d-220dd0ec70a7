/* 右侧列样式 */
.widget {
  background-color: rgba(0, 30, 60, 0.3);
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
  backdrop-filter: blur(3px);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5vh 1vw;
  background-color: rgba(0, 40, 80, 0.5);
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  color: #00a8ff;
  font-size: 14px !important; /* 统一字体大小 */
  font-weight: 600 !important;
  line-height: 1.2 !important;
}

.widget-content {
  padding: 0.5vh 0.5vw;
  height: calc(100% - 30px);
  overflow: hidden;
  font-size: 12px !important; /* 统一字体大小 */
  line-height: 1.4 !important;
}

.alarm-info {
  height: 300px; /* 减小高度 */
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.monitoring {
  height: 320px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.device-title {
  display: flex;
  align-items: center;
  padding: 5px 8px;
  margin-bottom: 5px;
  background-color: rgba(0, 40, 80, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  color: #ffffff;
  font-size: 14px !important; /* 统一字体大小 */
  font-weight: bold;
  line-height: 1.2 !important;
}

.device-title i {
  margin-right: 8px;
  color: #00a8ff;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
  height: calc(100% - 70px);
}

.widget-controls {
  display: flex;
  align-items: center;
}

.page-indicator {
  margin-right: 10px;
  font-size: 11px !important; /* 统一字体大小 */
  color: #7fdbff;
  line-height: 1.2 !important;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin-top: 5px;
  flex-direction: row;
}

.page-btn {
  width: 24px;
  height: 24px;
  background-color: rgba(0, 60, 120, 0.5);
  border: 1px solid rgba(0, 168, 255, 0.4);
  color: #7fdbff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 10px !important; /* 统一字体大小 */
  line-height: 1 !important;
}

.page-btn:hover:not(:disabled) {
  background-color: rgba(0, 80, 160, 0.7);
  border-color: rgba(0, 255, 157, 0.6);
  color: #ffffff;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
}

.page-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(0, 168, 255, 0.3);
  margin: 0 3px;
  transition: all 0.3s;
  cursor: pointer;
}

.page-dot.active {
  background-color: #00ff9d;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.7);
}

.camera-feed {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  background-color: rgba(0, 20, 40, 0.5);
  transition: all 0.3s;
}

.camera-feed:hover {
  border-color: rgba(0, 255, 157, 0.5);
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
  transform: scale(1.02);
}

.camera-label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 2px 5px;
  background-color: rgba(0, 30, 60, 0.8);
  color: #7fdbff;
  font-size: 10px !important; /* 统一字体大小 */
  z-index: 2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
  line-height: 1.2 !important;
}

.camera-feed img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s;
}

.camera-feed:hover img {
  transform: scale(1.05);
}

.camera-status {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 2;
}

.rec-indicator {
  color: #ff5555;
  font-size: 10px !important; /* 统一字体大小 */
  background-color: rgba(0, 20, 40, 0.7);
  padding: 2px 5px;
  border-radius: 3px;
  border: 1px solid rgba(255, 85, 85, 0.5);
  animation: blink 1s infinite;
  line-height: 1.2 !important;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.attitude-status {
  height: 400px; /* 调整高度以与左侧环境监控模块对齐 */
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* 设备名称样式 */
.device-name {
  font-size: 11px !important; /* 统一字体大小 */
  color: #00a8ff;
  font-weight: bold;
  margin-right: 5px;
  line-height: 1.2 !important;
}

.device-sensor {
  display: flex;
  flex-direction: column;
  margin-bottom: 3px;
}

/* 图表容器样式 */
.chart-container {
  height: 340px; /* 增加高度以适应更大的姿态水平状态模块 */
  width: 100%;
  margin-bottom: 10px;
  background-color: rgba(0, 20, 40, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.2);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 380px;
}

/* 当前值显示样式 */
.current-values {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  max-height: 40px;
  overflow-y: auto;
}

.current-values::-webkit-scrollbar {
  width: 4px;
}

.current-values::-webkit-scrollbar-track {
  background: rgba(0, 40, 80, 0.3);
  border-radius: 2px;
}

.current-values::-webkit-scrollbar-thumb {
  background: rgba(0, 168, 255, 0.5);
  border-radius: 2px;
}

.value-item {
  flex: 1;
  min-width: 90px;
  max-width: 120px;
  padding: 3px 6px;
  background-color: rgba(0, 30, 60, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.2);
  transition: all 0.3s;
}

.value-item.highlight {
  background-color: rgba(0, 168, 255, 0.2);
  animation: highlight-fade 3s;
  border-color: rgba(0, 168, 255, 0.5);
}

@keyframes highlight-fade {
  0% { background-color: rgba(0, 168, 255, 0.4); }
  100% { background-color: rgba(0, 30, 60, 0.5); }
}

.sensor-name {
  font-size: 11px !important; /* 统一字体大小 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2 !important;
}

.sensor-value {
  font-size: 12px !important; /* 统一字体大小 */
  font-weight: bold;
  text-align: right;
  line-height: 1.2 !important;
}

.sensor-value.negative {
  color: #ff5555;
}

.sensor-value.positive {
  color: #00ff9d;
}

.sensor-value.neutral {
  color: #7fdbff;
}
