/* 大屏详情页面布局修复样式 */


/* 确保大屏幕模式下内容正确显示 */
.large-screen-mode .dashboard-container {
  padding: 1px !important;
}

/* 确保左侧列正确显示 */
.left-column {
  margin-left: 10px !important;  
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* 确保右侧列正确显示 */
.right-column {
  margin-right: 10px !important;
  width: 380px !important;
  min-width: 380px !important;
  max-width: 380px !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* 确保右侧列包装器正确显示 */
.right-column-wrapper {
  position: absolute !important;
  right: 0 !important;
  top: 60px !important;
  height: calc(100% - 60px) !important;
  z-index: 100 !important;
  display: flex !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 确保左侧列包装器正确显示 */
.left-column-wrapper {
  position: absolute !important;
  left: 0 !important;
  top: 60px !important;
  height: calc(100% - 60px) !important;
  z-index: 100 !important;
  display: flex !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 确保头部容器正确显示 */
.header-container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  margin-bottom: 0 !important;
  height: 60px !important;
  z-index: 1000 !important;
}

/* 确保主内容区域正确显示 */
.main-content {
  padding: 0 !important;
  margin-top: 0 !important;
  height: calc(100% - 60px) !important;
  position: absolute !important;
  top: 60px !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 10 !important;
}

/* 确保所有模块正确显示 */
.widget {
  margin-bottom: 10px !important;
}

/* 确保图表容器正确显示 */
.chart-container {
  min-height: 200px !important;
}

/* 确保环境监控模块正确显示 */
.environment-monitor {
  min-height: 200px !important;
}

/* 确保系统信息模块正确显示 */
.system-info-chart {
  min-height: 200px !important;
}
