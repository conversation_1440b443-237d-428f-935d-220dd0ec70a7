import request from '@/utils/axios'

// 创建新设备
export function createDevice(data) {
  return request({
    path: '/api/system/device/devices',
    method: 'post',
    data
  })
}

// 获取设备列表
export function getDeviceList(params) {
  return request({
    path: '/api/system/device/devices',
    method: 'get',
    params
  })
}

// 获取设备详情
export function getDeviceDetail(id) {
  return request({
    path: `/api/system/device/devices/${id}`,
    method: 'get'
  })
}

// 更新设备信息
export function updateDevice(data) {
  return request({
    path: `/api/system/device/devices/${data.id}`,
    method: 'put',
    data
  })
}

// 删除设备
export function deleteDevice(id) {
  return request({
    path: `/api/system/device/devices/${id}`,
    method: 'delete'
  })
}

// 批量删除设备
export function batchDeleteDevices(ids) {
  return request({
    path: '/api/system/device/devices/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 更新设备状态
export function updateDeviceStatus(id, status) {
  return request({
    path: `/api/system/device/devices/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 获取设备统计信息
export function getDeviceStatistics() {
  return request({
    path: '/api/system/device/devices/statistics',
    method: 'get'
  })
}

// 获取设备绑定的网关列表
export function getDeviceGateways(deviceId) {
  return request({
    path: `/api/system/gateway/device/${deviceId}/gateways`,
    method: 'get'
  })
}

// 获取设备绑定的摄像头列表
export function getDeviceCameras(deviceId) {
  return request({
    path: `/api/system/camera/device/${deviceId}/cameras`,
    method: 'get'
  })
}

// 根据项目ID获取设备列表
export function getProjectDevices(projectId) {
  return request({
    path: `/api/system/device/project/${projectId}/devices`,
    method: 'get'
  })
}