<template>
  <div class="device-container">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增设备
      </el-button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索设备名称/设备号"
        style="width: 200px; margin-left: 16px"
        clearable
        @clear="handleSearch"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
    </div>

    <!-- 设备列表 -->
    <el-table
      v-loading="loading"
      :data="deviceList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="设备ID" width="80" fixed="left" />
      <el-table-column prop="device_code" label="设备号" width="150" fixed="left" />
      <el-table-column prop="device_name" label="设备名称" min-width="150" />
      <el-table-column label="所属项目" min-width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.project_name">{{ scope.row.project_name }}</span>
          <span v-else class="no-data">未关联项目</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="设备状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" prop="created_at" min-width="150">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
              class="operation-button"
            >编辑</el-button>
            <el-button
              size="mini"
              type="info"
              @click="handleViewBindings(scope.row)"
              class="operation-button"
            >查看绑定设备</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              class="operation-button"
            >删除</el-button>
            <el-button
              size="mini"
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              @click="updateStatus(scope.row, scope.row.status === 1 ? 0 : 1)"
            >
              {{ scope.row.status === 1 ? '设为离线' : '设为在线' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 设备表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="deviceForm"
        :model="deviceForm"
        :rules="deviceRules"
        label-width="120px"
      >
        <el-form-item label="设备号" prop="device_code">
          <el-input v-model="deviceForm.device_code" disabled placeholder="设备创建后自动生成"></el-input>
        </el-form-item>
        <el-form-item label="设备名称" prop="device_name">
          <el-input v-model="deviceForm.device_name" placeholder="请输入设备名称"></el-input>
        </el-form-item>
        <el-form-item label="所属项目" prop="project_id">
          <el-select v-model="deviceForm.project_id" placeholder="请选择所属项目" clearable>
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            ></el-option>
          </el-select>
          <div class="form-hint">可选，不绑定项目将作为独立设备管理</div>
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="deviceForm.status" placeholder="请选择设备状态">
            <el-option label="离线" :value="0"></el-option>
            <el-option label="在线" :value="1"></el-option>
            <el-option label="故障" :value="2"></el-option>
            <el-option label="维护中" :value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 绑定设备对话框 -->
    <el-dialog
      title="查看绑定设备"
      :visible.sync="bindDialogVisible"
      width="800px"
    >
      <el-tabs v-model="activeTab">
        <!-- 网关标签页 -->
        <el-tab-pane label="网关" name="gateways">
          <div class="bind-device-container">
            <div class="device-list">
              <el-table
                :data="gatewayList"
                border
                style="width: 100%"
              >
                <el-table-column prop="gateway_name" label="网关名称"></el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="传感器">
                  <template slot-scope="scope">
                    <el-tag 
                      v-for="sensor in scope.row.sensors" 
                      :key="sensor.id"
                      size="mini"
                      style="margin-right: 4px"
                    >
                      {{ sensor.sensor_name }}
                    </el-tag>
                    <span v-if="!scope.row.sensors || scope.row.sensors.length === 0" class="no-data">暂无传感器</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <!-- 摄像头标签页 -->
        <el-tab-pane label="摄像头" name="cameras">
          <div class="bind-device-container">
            <div class="device-list">
              <el-table
                :data="cameraList"
                border
                style="width: 100%"
              >
                <el-table-column prop="camera_name" label="摄像头名称"></el-table-column>
                <el-table-column prop="camera_type" label="类型">
                  <template slot-scope="scope">
                    <el-tag :type="getTypeTag(scope.row.camera_type)">
                      {{ getTypeText(scope.row.camera_type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="bindDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDeviceList,
  createDevice,
  updateDevice,
  deleteDevice,
  getDeviceDetail,
  batchDeleteDevices,
  updateDeviceStatus,
  getDeviceGateways,
  getDeviceCameras
} from '@/api/device'

export default {
  name: 'DeviceManagement',
  data() {
    return {
      loading: false,
      searchQuery: '',
      deviceList: [],
      projectList: [],
      gatewayList: [],
      cameraList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      bindDialogVisible: false,
      dialogTitle: '',
      activeTab: 'gateways',
      deviceForm: {
        id: undefined,         // 设备ID，编辑时使用
        device_code: '',       // 设备号
        device_name: '',       // 设备名称
        project_id: '',        // 所属项目ID
        status: 0              // 设备状态: 0-离线，1-在线，2-故障，3-维护中
      },
      deviceRules: {
        device_name: [
          { required: true, message: '请输入设备名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        project_id: [
          // 非必填，可以保留其他验证规则，如有的话
        ],
        status: [
          { required: true, message: '请选择设备状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchDeviceList()
    this.fetchProjectList()
    this.fetchGatewayList()
    this.fetchCameraList()
  },
  methods: {
    // 获取设备列表
    async fetchDeviceList() {
      this.loading = true
      try {
        // 使用 API 获取设备列表
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          query: this.searchQuery
        }
        
        const response = await getDeviceList(params)
        
        if (response && response.code === 0 && response.data) {
          // 处理设备列表数据，确保项目名称正确显示
          this.deviceList = (response.data.list || []).map(device => {
            // 确保 project_name 字段存在
            if (device.project && device.project.name && !device.project_name) {
              device.project_name = device.project.name;
            } else if (!device.project_name && device.project_id) {
              // 如果只有project_id，尝试从projectList中查找对应的项目名称
              const project = this.projectList.find(p => p.id === device.project_id);
              if (project) {
                device.project_name = project.name;
              }
            }
            return device;
          });
          this.total = response.data.total || 0;
        } else {
          this.$message.error(response?.message || '获取设备列表失败');
          this.deviceList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取设备列表失败:', error);
        this.$message.error('获取设备列表失败，请稍后重试');
        this.deviceList = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 获取项目列表
    async fetchProjectList() {
      try {
        // 导入项目API
        const { getProjectList } = require('@/api/project')
        
        // 从API获取项目列表
        const params = {
          pageSize: 100 // 获取足够多的项目以便选择
        }
        const response = await getProjectList(params)
        
        if (response && response.code === 0 && response.data) {
          this.projectList = response.data.list || []
        } else {
          console.warn('获取项目列表返回错误:', response)
          this.projectList = []
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.projectList = []
      }
    },

    // 获取网关列表
    async fetchGatewayList() {
      try {
        // TODO: 调用后端API获取网关列表
        // const { data } = await getGatewayList()
        // this.gatewayList = data
        
        // 模拟数据
        this.gatewayList = [
          {
            id: 1,
            name: '示例网关1',
            status: 'online',
            sensors: [
              { id: 1, name: '温度传感器1' },
              { id: 2, name: '湿度传感器1' }
            ]
          }
        ]
      } catch (error) {
        console.error('获取网关列表失败:', error)
      }
    },

    // 获取摄像头列表
    async fetchCameraList() {
      try {
        // TODO: 调用后端API获取摄像头列表
        // const { data } = await getCameraList()
        // this.cameraList = data
        
        // 模拟数据
        this.cameraList = [
          { id: 1, name: '摄像头1', type: '多角度', status: 'online' }
        ]
      } catch (error) {
        console.error('获取摄像头列表失败:', error)
      }
    },

    // 生成设备号
    generateDeviceNo() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `DEV${year}${month}${day}${random}`
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchDeviceList()
    },

    // 新增设备
    handleAdd() {
      this.dialogTitle = '新增设备'
      this.dialogVisible = true
      this.deviceForm = {
        id: undefined,
        device_code: this.generateDeviceNo(),
        device_name: '',
        project_id: '',
        status: 0  // 默认为离线(0)
      }
    },

    // 编辑设备
    handleEdit(row) {
      this.dialogTitle = '编辑设备'
      this.dialogVisible = true
      
      // 获取设备详情
      getDeviceDetail(row.id).then(response => {
        if (response && response.code === 0 && response.data) {
          const deviceData = response.data;
          
          // 确保字段映射正确
          this.deviceForm = {
            id: deviceData.id,
            device_code: deviceData.device_code,
            device_name: deviceData.device_name,
            // 优先使用 project_id，如果不存在则尝试从 project 对象获取
            project_id: deviceData.project_id || (deviceData.project ? deviceData.project.id : ''),
            status: deviceData.status
          };
        } else {
          this.$message.error(response?.message || '获取设备详情失败');
        }
      }).catch(error => {
        console.error('获取设备详情失败:', error);
        this.$message.error('获取设备详情失败，请稍后重试');
      });
    },

    // 删除设备
    handleDelete(row) {
      this.$confirm('确认删除该设备吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteDevice(row.id)
          
          if (response && response.code === 0) {
            this.$message.success('删除成功')
            this.fetchDeviceList()
          } else {
            this.$message.error(response?.message || '删除设备失败')
          }
        } catch (error) {
          console.error('删除设备失败:', error)
          this.$message.error('删除设备失败，请稍后重试')
        }
      }).catch(() => {})
    },

    // 提交表单
    handleSubmit() {
      this.$refs.deviceForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            const submitData = { ...this.deviceForm }
            
            // 如果没有选择项目，将 project_id 设为 null 或删除
            if (!submitData.project_id) {
              submitData.project_id = null // 或者删除: delete submitData.project_id
            }
            
            if (submitData.id) {
              // 更新设备
              response = await updateDevice(submitData)
            } else {
              // 创建设备
              delete submitData.id // 确保创建时不提交 id 字段
              response = await createDevice(submitData)
            }
            
            if (response && response.code === 0) {
              this.$message.success('保存成功')
              this.dialogVisible = false
              this.fetchDeviceList()
            } else {
              this.$message.error(response?.message || '保存设备失败')
            }
          } catch (error) {
            console.error('保存设备失败:', error)
            this.$message.error('保存设备失败，请稍后重试')
          }
        }
      })
    },

    // 设备状态
    getStatusType(status) {
      const statusMap = {
        0: 'info',      // 离线
        1: 'success',   // 在线
        2: 'danger',    // 故障
        3: 'warning'    // 维护中
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        0: '离线',
        1: '在线',
        2: '故障',
        3: '维护中'
      }
      return statusMap[status] || '未知'
    },

    // 查看绑定设备
    async handleViewBindings(row) {
      this.bindDialogVisible = true
      this.currentDevice = row
      
      try {
        // 获取设备绑定的网关列表
        const gatewayResponse = await getDeviceGateways(row.id)
        console.log("获取网关列表",gatewayResponse)
        if (gatewayResponse && gatewayResponse.code === 0) {
          this.gatewayList = gatewayResponse.data || []
        } else {
          this.$message.error(gatewayResponse?.message || '获取网关列表失败')
          this.gatewayList = []
        }

        // 获取设备绑定的摄像头列表
        const cameraResponse = await getDeviceCameras(row.id)
        console.log("获取摄像头列表",cameraResponse)
        if (cameraResponse && cameraResponse.code === 0) {
          this.cameraList = cameraResponse.data || []
        } else {
          this.$message.error(cameraResponse?.message || '获取摄像头列表失败')
          this.cameraList = []
        }
      } catch (error) {
        console.error('获取绑定设备列表失败:', error)
        this.$message.error('获取绑定设备列表失败，请稍后重试')
        this.gatewayList = []
        this.cameraList = []
      }
    },

    // 更新设备状态
    async updateStatus(row, newStatus) {
      try {
        const response = await updateDeviceStatus(row.id, newStatus)
        
        if (response && response.code === 0) {
          this.$message.success(`设备状态已更新为${this.getStatusText(newStatus)}`)
          this.fetchDeviceList() // 刷新列表
        } else {
          this.$message.error(response?.message || '更新设备状态失败')
        }
      } catch (error) {
        console.error('更新设备状态失败:', error)
        this.$message.error('更新设备状态失败，请稍后重试')
      }
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchDeviceList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchDeviceList()
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.deviceForm.resetFields()
    },

    // 摄像头类型
    getTypeTag(type) {
      const typeMap = {
        1: 'success', // 球机
        2: 'info',   // 枪机
        3: 'warning', // 半球
        4: 'info'    // 其他
      }
      return typeMap[type] || 'info'
    },

    getTypeText(type) {
      const typeMap = {
        1: '球机',
        2: '枪机',
        3: '半球',
        4: '其他'
      }
      return typeMap[type] || '未知'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
  }
}
</script>

<style lang="scss" scoped>
.device-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.bind-device-container {
  .device-list {
    margin-top: 20px;
  }
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-start;
  align-items: flex-start;
  padding-left: 8px;
}

.operation-button {
  width: 90px;
  text-align: center;
  padding: 0;
  margin: 0;
  height: 28px;
  line-height: 26px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.no-data {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}
</style> 