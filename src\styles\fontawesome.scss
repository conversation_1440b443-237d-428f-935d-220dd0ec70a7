/* Font Awesome 图标样式优化 */

// 基础图标样式
.fas, .fa, .far, .fab {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

// 图标间距
.fas, .fa {
  margin-right: 5px;
}

// 图标大小
.fa-xs {
  font-size: 0.75em;
}

.fa-sm {
  font-size: 0.875em;
}

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -0.0667em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

// 图标颜色
.fa-primary {
  color: #00a8ff;
}

.fa-success {
  color: #00ff9d;
}

.fa-warning {
  color: #ffba00;
}

.fa-danger {
  color: #ff5555;
}

// 图标动画
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-spin 1s infinite steps(8);
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 特定组件中的图标样式
.widget-header .fas {
  color: #00a8ff;
}

.widget-status .fas {
  color: #00ff9d;
  font-size: 0.7em;
  margin-right: 3px;
}

.device-title .fas {
  color: #00a8ff;
  margin-right: 5px;
}

// 修复特定图标样式
.fa-exclamation-triangle,
.fa-video,
.fa-ruler-combined,
.fa-server,
.fa-chevron-left,
.fa-chevron-right,
.fa-circle {
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
}
