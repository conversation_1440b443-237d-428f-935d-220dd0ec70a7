import * as THREE from 'three';
import TWEEN from '@tweenjs/tween.js';

// 初始化地图悬浮效果
function initMapFloat(camera, mapModel) {
  // 创建射线拾取器
  const raycaster = new THREE.Raycaster();
  // 创建鼠标向量
  const mouse = new THREE.Vector2();
  
  // 当前悬浮的省份
  let currentProvince = null;
  
  // 监听鼠标移动
  document.addEventListener('mousemove', (event) => {
    // 计算鼠标在标准化设备坐标中的位置
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    
    // 更新射线
    raycaster.setFromCamera(mouse, camera);
    
    // 检测射线与地图模型的交叉
    const intersects = raycaster.intersectObject(mapModel, true);
    
    // 找到第一个有效的省份对象
    const provinceObj = intersects.find(item => {
      return item.object.parent && item.object.parent.name && item.object.parent.name !== '中国地图';
    });
    
    // 如果鼠标悬浮在省份上
    if (provinceObj) {
      const province = provinceObj.object.parent;
      
      // 如果悬浮的是新省份
      if (currentProvince !== province) {
        // 重置之前的省份
        if (currentProvince) {
          new TWEEN.Tween(currentProvince.position)
            .to({ y: 0 }, 300)
            .easing(TWEEN.Easing.Sinusoidal.Out)
            .start();
        }
        
        // 设置当前省份
        currentProvince = province;
        
        // 让当前省份浮起来
        new TWEEN.Tween(currentProvince.position)
          .to({ y: 2 }, 300)
          .easing(TWEEN.Easing.Sinusoidal.Out)
          .start();
          
        // 更新省份名称显示
        const provinceNameDiv = document.getElementById('provinceName');
        if (provinceNameDiv) {
          provinceNameDiv.textContent = province.name;
          provinceNameDiv.style.display = 'block';
          provinceNameDiv.style.left = `${event.clientX}px`;
          provinceNameDiv.style.top = `${event.clientY - 30}px`;
        }
      }
    } else {
      // 如果鼠标不在任何省份上，重置当前省份
      if (currentProvince) {
        new TWEEN.Tween(currentProvince.position)
          .to({ y: 0 }, 300)
          .easing(TWEEN.Easing.Sinusoidal.Out)
          .start();
          
        currentProvince = null;
        
        // 隐藏省份名称
        const provinceNameDiv = document.getElementById('provinceName');
        if (provinceNameDiv) {
          provinceNameDiv.style.display = 'none';
        }
      }
    }
  });
}

export { initMapFloat }; 