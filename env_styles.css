﻿/* 鎶ヨ淇℃伅娴獥 */
.alarm-stats {
  height: 280px;
}

/* 鏁呴殰鎶ヤ慨娴獥 */
.fault-stats {
  height: 200px;
}

/* 璁惧浼犳劅鍣ㄦā鍧?*/
.sensor-stats {
  height: 280px;
}

/* 鎽勫儚澶寸洃鎺фā鍧?*/
.camera-grid-stats {
  height: 280px;
}

/* 鍘嗗彶鐢ㄧ數娴獥 */
.history-stats {
  height: 200px;
}

/* 鍋滅數淇℃伅娴獥 */
.outage-stats {
  height: 180px;
}

<style>
/* 淇敼鐜鐩戞帶涓?瀹牸甯冨眬 */
.env-grid {
  display: grid;
  gap: 8px;
  margin-bottom: 10px;
  height: auto !important;
  min-height: 300px !important;
}

.env-grid-four {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.env-item {
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(0, 200, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 160px !important;
  min-height: 160px !important;
  margin-bottom: 8px;
}

.env-item:hover {
  transform: scale(1.03);
  box-shadow: 0 0 15px rgba(0, 200, 255, 0.4);
  border-color: rgba(0, 200, 255, 0.7);
  z-index: 2;
}

.env-header {
  display: flex;
  align-items: center;
  padding: 3px 8px;
  background: rgba(0, 40, 80, 0.8);
  border-bottom: 1px solid rgba(0, 200, 255, 0.3);
  height: 25px;
}

.env-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.env-status-indicator.online {
  background: #4CAF50;
  box-shadow: 0 0 5px #4CAF50;
}

.env-status-indicator.offline {
  background: #f44336;
  box-shadow: 0 0 5px #f44336;
}

.env-title {
  flex: 1;
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: rgba(0, 200, 255, 0.9);
}

.env-timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
}

.env-content {
  position: relative;
  height: 120px !important;
  min-height: 120px !important;
  flex: 1;
  display: flex;
  overflow: hidden;
  background-color: rgba(0, 20, 40, 0.4);
}

.env-data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
  padding: 8px;
  width: 100%;
  height: 100%;
}

.env-data-item {
  display: flex;
  align-items: center;
  background: rgba(0, 40, 80, 0.3);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
}

.env-data-item:hover {
  background: rgba(0, 60, 100, 0.5);
  transform: scale(1.05);
}

.env-data-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.env-data-icon.temp {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ff4b4b' viewBox='0 0 24 24'%3E%3Cpath d='M12 2c1.1 0 2 .9 2 2v10.5c1.2.7 2 2 2 3.5 0 2.2-1.8 4-4 4s-4-1.8-4-4c0-1.5.8-2.8 2-3.5V4c0-1.1.9-2 2-2m0 2v10.1c-1.2.4-2 1.5-2 2.9 0 1.7 1.3 3 3 3s3-1.3 3-3c0-1.4-.8-2.5-2-2.9V4h-2z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(255, 75, 75, 0.5));
}

.env-data-icon.humidity {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234091f7' viewBox='0 0 24 24'%3E%3Cpath d='M12 2c.5 0 1 .4 1 1v7.6c.6.6 1 1.4 1 2.4 0 1.7-1.3 3-3 3s-3-1.3-3-3c0-1 .4-1.8 1-2.4V3c0-.6.5-1 1-1m7 11c1.1 0 2 .9 2 2 0 1-1.8 3.4-2 3.8-.2-.4-2-2.8-2-3.8 0-1.1.9-2 2-2M8 17c1.1 0 2 .9 2 2 0 1-1.8 3.4-2 3.8-.2-.4-2-2.8-2-3.8 0-1.1.9-2 2-2z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(64, 145, 247, 0.5));
}

.env-data-icon.pressure {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2357cd85' viewBox='0 0 24 24'%3E%3Cpath d='M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10 10 10 0 0 0 10-10A10 10 0 0 0 12 2m0 2a8 8 0 0 1 8 8 8 8 0 0 1-8 8 8 8 0 0 1-8-8 8 8 0 0 1 8-8m0 2a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6m-1 2h2v6h-2V8m0 7h2v2h-2v-2z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(87, 205, 133, 0.5));
}

.env-data-icon.wind {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffc100' viewBox='0 0 24 24'%3E%3Cpath d='M4 10h12v2H4v-2M4 6h12v2H4V6m4 10h8v2H8v-2m12-8a2 2 0 0 1-2 2V4a2 2 0 0 1 2 2Z'/%3E%3C/svg%3E");
  filter: drop-shadow(0 0 3px rgba(255, 193, 0, 0.5));
}

.env-data-info {
  display: flex;
  flex-direction: column;
}

.env-data-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.env-data-value {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 200, 255, 0.5);
}

.env-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 5px 8px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.env-location {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.env-status {
  display: flex;
  align-items: center;
  font-size: 11px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.8);
}

.env-indicator {
  width: 6px;
  height: 6px;
  background: #4CAF50;
  border-radius: 50%;
  margin-right: 3px;
  animation: blink 1s infinite;
}

.env-item-empty {
  grid-column: span 2;
  grid-row: span 2;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 20, 40, 0.3);
  border: 1px dashed rgba(0, 200, 255, 0.3);
}

.env-item-empty-content {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

/* Media queries for environment monitoring */
@media (max-height: 900px) {
  .env-grid {
    height: 280px;
  }
}

@media (max-height: 800px) {
  .env-grid {
    height: 250px;
  }
}

@media (max-height: 700px) {
  .env-grid {
    height: 200px;
  }
}
</style> 
