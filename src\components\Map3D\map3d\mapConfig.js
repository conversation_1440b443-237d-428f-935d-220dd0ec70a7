export const mapConfig = {
  // 地图厚度
  mapDepth: 1,
  // 地图颜色 - 设置为null以启用随机渐变色模式，否则所有省份使用相同颜色
  mapColor: null,
  // 地图颜色随机渐变色 - 当mapColor为null时，每个省份会从这个数组中选择一种颜色
  mapColorGradient: ['#0063ce', '#32a2fb', '#00c8fb', '#38a0fe','#02a6b5'],
  // 地图是否透明
  mapTransparent: false, // 设为不透明，确保颜色完全显示
  // 地图透明度
  mapOpacity: 1.0, // 设为完全不透明
  // 地图边框颜色
  mapSideColor1: '#333333',
  mapSideColor2: '#000000',

  // 顶部线条颜色
  topLineColor: 0xffffff,
  // 顶部线条宽度
  topLineWidth: 0.05,
  // 顶部线条z-index
  topLineZIndex: 1,

  // 点位z-index
  spotZIndex: 1.2,
  // 标签z-index - 确保标签在点位上方
  labelZIndex: 2.0,
  // 飞线颜色
  flyLineColor: 0xffff00,
} 