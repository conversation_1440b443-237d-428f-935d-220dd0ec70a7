/**
 * 下载中国地图数据脚本
 * 
 * 用法:
 *   node scripts/download-map-data.js
 * 
 * 这个脚本会下载阿里云 DataV 的中国地图数据，并保存到项目的 public/assets/map 目录中。
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 地图代码列表，可根据需要添加或移除
const mapCodes = [
  { code: '100000', name: '中国' }
  // 如果需要添加更多省市，取消下面的注释
  // { code: '110000', name: '北京' },
  // { code: '310000', name: '上海' },
  // { code: '440000', name: '广东' }
];

// 目标目录
const targetDir = path.resolve(process.cwd(), 'public/assets/map');

// 确保目录存在
try {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`目录已创建或已存在: ${targetDir}`);
} catch (err) {
  console.error(`创建目录失败: ${err.message}`);
  process.exit(1);
}

// 下载文件函数
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(dest, () => {}); // 删除部分下载的文件
        reject(err);
      });
    }).on('error', (err) => {
      fs.unlink(dest, () => {}); // 删除部分下载的文件
      reject(err);
    });
  });
}

// 下载所有地图数据
async function downloadAllMaps() {
  console.log('开始下载地图数据...');
  
  for (const map of mapCodes) {
    const url = `https://geo.datav.aliyun.com/areas_v3/bound/${map.code}_full.json`;
    const dest = path.join(targetDir, `${map.code}_full.json`);
    
    try {
      console.log(`正在下载 ${map.name} (${map.code}) 地图数据...`);
      await downloadFile(url, dest);
      console.log(`✅ ${map.name} 地图数据下载完成: ${dest}`);
    } catch (error) {
      console.error(`❌ ${map.name} 地图数据下载失败:`, error.message);
    }
  }
  
  console.log('地图数据下载完成!');
}

// 执行下载
downloadAllMaps().catch((error) => {
  console.error('下载过程中发生错误:', error);
  process.exit(1);
}); 