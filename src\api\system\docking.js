import request from '@/utils/axios'

/**
 * 获取对接设备列表
 * @param {Object} params - 查询参数 { page, page_size }
 * @returns {Promise}
 */
export function getDockingList(params) {
  return request({
    path: '/api/system/docking/list',
    method: 'get',
    params
  })
}

/**
 * 获取对接设备详情
 * @param {Number} id - 对接设备ID
 * @returns {Promise}
 */
export function getDockingDetail(id) {
  return request({
    path: `/api/system/docking/${id}`,
    method: 'get'
  })
}

/**
 * 创建对接设备
 * @param {Object} data - 对接设备数据
 * @returns {Promise}
 */
export function createDocking(data) {
  return request({
    path: '/api/system/docking/create',
    method: 'post',
    data
  })
}

/**
 * 更新对接设备
 * @param {Object} data - 对接设备数据
 * @returns {Promise}
 */
export function updateDocking(data) {
  return request({
    path: `/api/system/docking/update/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除对接设备
 * @param {Number} id - 对接设备ID
 * @returns {Promise}
 */
export function deleteDocking(id) {
  return request({
    path: `/api/system/docking/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 获取对接设备传感器列表
 * @param {Number} id - 对接设备ID
 * @returns {Promise}
 */
export function getDockingSensorList(id) {
  return request({
    path: `/api/system/docking/detail/${id}`,
    method: 'get'
  })
}

/**
 * 导入Excel
 * @param {FormData} data - Excel文件FormData
 * @returns {Promise}
 */
export function importDockingExcel(data) {
  return request({
    path: '/api/system/docking/import/excel',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载Excel导入模板
 * @returns {Promise}
 */
export function downloadDockingTemplate() {
  return request({
    path: '/api/system/docking/download/template',
    method: 'get',
    responseType: 'blob'
  })
} 