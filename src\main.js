import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

// 引入 Font Awesome
import '@fortawesome/fontawesome-free/css/all.css'

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import i18n from './lang' // internationalization
import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log
import "@/utils/directive";
import * as filters from './filters' // global filters
import Pagination from "@/components/Pagination";
import {getDictType} from "@/api/admin";
import {downFile} from "@/utils";
Vue.config.productionTip = false;
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
Vue.prototype.$getDictType=getDictType;
Vue.prototype.$nodeMD5="nodeMD5"; //密码加盐
Vue.prototype.$downFile=downFile; //密码加盐
Vue.config.productionTip = false
Vue.component('Pagination', Pagination)
new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
