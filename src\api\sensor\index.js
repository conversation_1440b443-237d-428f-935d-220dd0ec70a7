import request from '@/utils/axios'

// 获取传感器列表
export function getSensorList(params) {
  return request({
    path: '/api/system/sensor/sensors',
    method: 'get',
    params
  })
}

// 根据网关ID获取传感器列表
export function getSensorListByGateway(gatewayId) {
  return request({
    path: '/api/system/sensor/sensors',
    method: 'get',
    params: { gateway_id: gatewayId }
  })
}

// 根据网关编码获取传感器列表 - 使用路径参数
export function getSensorListByGatewayCode(gatewayCode) {
  return request({
    path: `/api/system/sensor/gateway/code/${gatewayCode}/all-sensors`,
    method: 'get'
  })
}

// 获取传感器详情
export function getSensorDetail(id) {
  return request({
    path: `/api/system/sensor/sensors/${id}`,
    method: 'get'
  })
} 