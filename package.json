{"name": "bridge-cloud-client", "version": "1.2.1", "description": "", "author": "", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "download:map": "node scripts/download-map-data.js"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@riophae/vue-treeselect": "^0.4.0", "@tweenjs/tween.js": "^25.0.0", "@videojs/http-streaming": "^3.15.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.18.1", "chart.js": "^2.9.4", "clipboard": "2.0.4", "core-js": "3.6.5", "d3": "^7.9.0", "d3-geo": "^3.1.1", "dropzone": "5.5.1", "echarts": "^4.2.1", "element-ui": "^2.15.13", "fuse.js": "3.4.4", "gsap": "^3.12.7", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pinyin": "2.9.0", "screenfull": "4.2.0", "svg-baker-runtime": "^1.4.7", "three": "^0.174.0", "video.js": "^8.22.0", "vue": "2.6.10", "vue-i18n": "7.3.2", "vue-router": "3.0.2", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://gitee.com/MMinter/vue_node/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "repository": {"type": "git", "url": "https://gitee.com/MMinter/vue_node"}}