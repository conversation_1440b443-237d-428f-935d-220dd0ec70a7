<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎使用工业云平台</h1>
        <p class="welcome-subtitle">智能工业管理解决方案</p>
      </div>
      <div class="welcome-time">
        {{ currentTime }}
      </div>
    </div>

    <!-- 项目介绍卡片 -->
    <div class="project-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="project-card" shadow="hover">
            <div class="card-icon">
              <i class="el-icon-monitor"></i>
            </div>
            <h3>设备监控</h3>
            <p>实时监控设备运行状态，提供智能预警和故障诊断功能</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="project-card" shadow="hover">
            <div class="card-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <h3>数据分析</h3>
            <p>深度分析生产数据，助力决策优化和效率提升</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="project-card" shadow="hover">
            <div class="card-icon">
              <i class="el-icon-connection"></i>
            </div>
            <h3>智能互联</h3>
            <p>实现设备互联互通，打造智能化生产环境</p>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系统信息 -->
    <div class="system-info">
      <el-card shadow="hover">
        <div slot="header">
          <span>系统信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">系统版本：</span>
              <span class="value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="label">运行环境：</span>
              <span class="value">Node.js + Vue.js</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">最后更新：</span>
              <span class="value">{{ lastUpdate }}</span>
            </div>
            <div class="info-item">
              <span class="label">技术支持：</span>
              <span class="value">博瑞泰通技术团队</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      currentTime: '',
      lastUpdate: '2024-03-20'
    }
  },
  created() {
    this.updateTime()
    // 每秒更新时间
    setInterval(this.updateTime, 1000)
  },
  methods: {
    updateTime() {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, '0')
      const minutes = now.getMinutes().toString().padStart(2, '0')
      const seconds = now.getSeconds().toString().padStart(2, '0')
      this.currentTime = `${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.welcome-section {
  background: linear-gradient(135deg, #1975FF 0%, #1560d6 100%);
  border-radius: 16px;
  padding: 40px;
  color: white;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(25, 117, 255, 0.15);

  .welcome-content {
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 10px 0;
    }

    .welcome-subtitle {
      font-size: 18px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .welcome-time {
    font-size: 24px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 8px;
  }
}

.project-cards {
  margin-bottom: 30px;

  .project-card {
    height: 200px;
    transition: all 0.3s;
    border: none;
    border-radius: 12px;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
    }

    .card-icon {
      font-size: 40px;
      color: #1975FF;
      margin-bottom: 20px;
    }

    h3 {
      margin: 0 0 15px 0;
      font-size: 20px;
      color: #1a1a1a;
    }

    p {
      margin: 0;
      color: #666;
      line-height: 1.6;
    }
  }
}

.system-info {
  .el-card {
    border-radius: 12px;
    border: none;

    ::v-deep .el-card__header {
      padding: 20px;
      border-bottom: 1px solid #eee;
      font-size: 18px;
      font-weight: 500;
    }

    .info-item {
      margin-bottom: 15px;
      
      .label {
        color: #666;
        margin-right: 10px;
      }

      .value {
        color: #1a1a1a;
        font-weight: 500;
      }
    }
  }
}
</style>

