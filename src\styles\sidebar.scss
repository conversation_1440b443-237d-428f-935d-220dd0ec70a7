#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    box-shadow: 0 0 5px #dedede;
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHoverBg !important;
      }
    }

    .is-active>.el-submenu__title {
      color: $menuSubActiveText !important;
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $menuSubBg !important;

      &:hover {
        background-color: $menuHoverBg !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHoverBg !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

// 添加全屏模式下的侧边栏样式
body.fullscreen-mode,
html.fullscreen-mode,
body.fullscreen-active,
html.fullscreen-active {
  .sidebar-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -9999 !important;
    background-color: transparent !important;
    transform: translateX(-100%) !important;
    pointer-events: none !important;
    
    // 确保所有子元素也被隐藏
    * {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }
  }
  
  // 确保主容器占满整个屏幕
  .main-container {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
  }
}

// 添加更强力的全屏模式样式覆盖
:fullscreen,
:-webkit-full-screen,
:-moz-full-screen,
:-ms-fullscreen {
  .sidebar-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    max-width: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -9999 !important;
    background-color: transparent !important;
    transform: translateX(-100%) !important;
    pointer-events: none !important;
    border: none !important;
    box-shadow: none !important;
    
    // 确保所有子元素也被隐藏
    * {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }
  }
}

// 添加特定于全屏元素的样式
.screen-container:fullscreen,
.screen-container:-webkit-full-screen,
.screen-container:-moz-full-screen,
.screen-container:-ms-fullscreen {
  .sidebar-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
  }
}

// 确保退出全屏模式后侧边栏正确显示
body:not(.fullscreen-mode):not(.fullscreen-active),
html:not(.fullscreen-mode):not(.fullscreen-active) {
  .sidebar-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: $sideBarWidth !important;
    height: 100% !important;
    overflow: hidden !important;
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1001 !important;
    transform: none !important;
    pointer-events: auto !important;
    
    // 确保所有子元素也正确显示
    * {
      display: inherit;
      visibility: visible;
      opacity: 1;
    }
    
    // 确保菜单项正确显示
    .el-menu-item,
    .el-submenu__title {
      display: flex !important;
      align-items: center !important;
      height: 50px !important;
      line-height: 50px !important;
    }
    
    // 确保图标正确显示
    .svg-icon,
    .sub-el-icon,
    i {
      display: inline-block !important;
      vertical-align: middle !important;
    }
    
    // 确保文本正确显示
    span {
      display: inline-block !important;
      vertical-align: middle !important;
    }
  }
}
