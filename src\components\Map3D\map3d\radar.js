import * as THREE from 'three'

// 雷达选项类型
export const radarData = [
  {
    position: [-25, 0, 0],
    radius: 5,
    color: '#ff0000',
    opacity: 0.5,
    speed: 1,
  },
  {
    position: [25, 0, 0],
    radius: 5,
    color: '#ffff00',
    opacity: 0.5,
    speed: 1,
  },
  {
    position: [0, -25, 0],
    radius: 5,
    color: '#00ff00',
    opacity: 0.5,
    speed: 1,
  },
]

export function drawRadar(option, ratio) {
  const { position, radius, color, opacity, speed } = option
  
  // 创建圆形平面
  const planeGeometry = new THREE.CircleGeometry(radius, 80)
  
  // 创建着色器材质
  const planeMaterial = new THREE.ShaderMaterial({
    uniforms: {
      u_time: { value: 0 },
      u_color: { value: new THREE.Color(color) },
      u_opacity: { value: opacity },
      u_speed: { value: speed },
    },
    transparent: true,
    side: THREE.DoubleSide,
    vertexShader: `
      varying vec2 v_position;
      void main() {
        v_position = vec2(position.x, position.y);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      varying vec2 v_position;
      uniform float u_time;
      uniform vec3 u_color;
      uniform float u_opacity;
      uniform float u_speed;
      
      void main() {
        float dist = length(v_position);
        float angle = atan(v_position.y, v_position.x);
        
        // 雷达线旋转
        float line = step(0.98, cos(angle + u_time * u_speed));
        
        // 雷达圆环
        float circle = step(0.98, sin(dist * 10.0));
        
        // 波纹
        float wave = 1.0 - (abs(sin(dist * 10.0 - u_time * u_speed)) * 0.15);
        wave *= 1.0 - dist;
        
        // 合并
        float alpha = (line + circle) * u_opacity * wave;
        
        gl_FragColor = vec4(u_color, alpha);
      }
    `,
  })
  
  // 创建网格
  const plane = new THREE.Mesh(planeGeometry, planeMaterial)
  
  // 水平放置
  plane.rotation.x = -Math.PI / 2
  
  // 设置位置
  plane.position.set(position[0], position[1], position[2])
  
  // 动画
  function animate() {
    planeMaterial.uniforms.u_time.value = ratio.value
    requestAnimationFrame(animate)
  }
  animate()
  
  return plane
} 