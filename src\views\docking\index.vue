<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.gateway_name"
        placeholder="网关名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-upload2"
        @click="handleImportExcel"
      >
        导入Excel
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网关编号" min-width="110px">
        <template slot-scope="{row}">
          <span>{{ row.gateway_code || row.deviceCode || row.device_code || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网关名称" min-width="150px">
        <template slot-scope="{row}">
          <span>{{ row.gateway_name || row.deviceName || row.device_name || row.name || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="网关型号" min-width="100px">
        <template slot-scope="{row}">
          <span>{{ row.gateway_model || row.device_model || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对接网关类型" min-width="120px">
        <template slot-scope="{row}">
          <el-tag>{{ row.docking_device_type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="对接网关型号" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.docking_device_model }}</span>
        </template>
      </el-table-column>
      <el-table-column label="通信协议" min-width="120px">
        <template slot-scope="{row}">
          <el-tag type="success">{{ row.communication_protocol }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="传输格式" min-width="100px">
        <template slot-scope="{row}">
          <span>{{ row.transfer_format }}</span>
        </template>
      </el-table-column>
      <el-table-column label="接口类型" min-width="100px">
        <template slot-scope="{row}">
          <span>{{ row.interface_type }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP/端口" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.ip_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="接入分配" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.access_allocation }}</span>
        </template>
      </el-table-column>
      <el-table-column label="传感器数量" min-width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag type="info">{{ row.sensor_count }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <div class="operation-buttons">
            <el-button type="primary" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
            <el-button
              size="mini"
              type="success"
              @click="handleViewSensors(row)"
            >
              传感器
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 500px; margin-left:50px;"
      >
        <el-form-item label="网关编号" prop="gateway_code">
          <el-input v-model="temp.gateway_code" />
        </el-form-item>
        <el-form-item label="网关名称" prop="gateway_name">
          <el-input v-model="temp.gateway_name" />
        </el-form-item>
        <el-form-item label="网关型号">
          <el-input v-model="temp.gateway_model" />
        </el-form-item>
        <el-form-item label="对接网关类型" prop="docking_device_type">
          <el-select v-model="temp.docking_device_type" class="filter-item" placeholder="请选择">
            <el-option label="PLC" value="PLC" />
            <el-option label="RTU" value="RTU" />
            <el-option label="SCADA" value="SCADA" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="对接网关型号" prop="docking_device_model">
          <el-input v-model="temp.docking_device_model" />
        </el-form-item>
        <el-form-item label="施工方联系电话">
          <el-input v-model="temp.construction_contact_phone" />
        </el-form-item>
        <el-form-item label="网关开发方电话">
          <el-input v-model="temp.device_developer_phone" />
        </el-form-item>
        <el-form-item label="通信协议" prop="communication_protocol">
          <el-select v-model="temp.communication_protocol" class="filter-item" placeholder="请选择">
            <el-option label="MODBUS_TCP" value="MODBUS_TCP" />
            <el-option label="MODBUS_RTU" value="MODBUS_RTU" />
            <el-option label="MQTT" value="MQTT" />
            <el-option label="OPC_UA" value="OPC_UA" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="传输格式" prop="transfer_format">
          <el-input v-model="temp.transfer_format" />
        </el-form-item>
        <el-form-item label="接口类型" prop="interface_type">
          <el-input v-model="temp.interface_type" />
        </el-form-item>
        <el-form-item label="IP/端口" prop="ip_port">
          <el-input v-model="temp.ip_port" />
        </el-form-item>
        <el-form-item label="接入分配" prop="access_allocation">
          <el-input v-model="temp.access_allocation" />
        </el-form-item>
        <el-form-item label="安全要求">
          <el-input v-model="temp.security_requirements" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog title="导入Excel" :visible.sync="importDialogVisible" width="500px">
      <el-form label-position="right" label-width="100px" style="margin-bottom: 20px;">
        <el-form-item label="网关编号">
          <div style="display: flex;">
            <el-select
              v-model="importGatewayCode"
              filterable
              remote
              reserve-keyword
              placeholder="请输入网关编号/名称进行搜索"
              :remote-method="searchGateway"
              :loading="gatewayLoading"
              style="flex: 1;"
            >
              <el-option
                v-for="item in gatewayList"
                :key="item.id"
                :label="(item.gateway_name || '未命名网关') + ' (' + (item.gateway_code || '无编号') + ')'"
                :value="item.gateway_code">
              </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :http-request="handleExcelUpload"
        :before-upload="beforeExcelUpload"
        accept=".xlsx, .xls"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且文件大小不超过10MB</div>
      </el-upload>
      <div class="import-template" style="margin-top: 20px; text-align: center;">
        <a href="#" @click.prevent="downloadTemplate">下载导入模板</a>
      </div>
    </el-dialog>

    <!-- 传感器列表对话框 -->
    <el-dialog
      title="对接网关传感器列表"
      :visible.sync="sensorDialogVisible"
      width="1200px"
      class="sensor-list-dialog"
    >
      <div v-loading="sensorLoading">
        <!-- 对接网关信息 -->
        <div class="docking-device-info">
          <div class="info-item">
            <span class="label">网关编号:</span>
            <span class="value">{{ currentDocking.gateway_code || currentDocking.deviceCode || currentDocking.device_code || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">网关名称:</span>
            <span class="value">{{ currentDocking.gateway_name || currentDocking.deviceName || currentDocking.device_name || currentDocking.name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">对接网关类型:</span>
            <el-tag>{{ currentDocking.docking_device_type }}</el-tag>
          </div>
          <div class="info-item">
            <span class="label">对接网关型号:</span>
            <span class="value">{{ currentDocking.docking_device_model }}</span>
          </div>
          <div class="info-item">
            <span class="label">通信协议:</span>
            <el-tag type="success">{{ currentDocking.communication_protocol }}</el-tag>
          </div>
          <div class="info-item">
            <span class="label">传输格式:</span>
            <span class="value">{{ currentDocking.transfer_format }}</span>
          </div>
          <div class="info-item">
            <span class="label">IP/端口:</span>
            <span class="value">{{ currentDocking.ip_port }}</span>
          </div>
          <div class="info-item">
            <span class="label">接入分配:</span>
            <span class="value">{{ currentDocking.access_allocation }}</span>
          </div>
          <div class="info-item">
            <span class="label">传感器数量:</span>
            <el-tag type="info">{{ sensorList.length }}</el-tag>
          </div>
        </div>
        
        <div class="sensor-table-container">
          <!-- 传感器列表表格 -->
          <el-table
            :data="sensorList"
            border
            style="width: 100%"
            v-if="sensorList.length > 0"
            height="500"
          >
            <el-table-column prop="id" label="传感器ID" width="70" />
            <el-table-column prop="sequence_no" label="序号" width="50" align="center" />
            <el-table-column prop="variable_name" label="变量名称" width="120" />
            <el-table-column prop="variable_description" label="变量描述" width="120" />
            <el-table-column prop="register_address" label="寄存器地址" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.address_type }}{{ scope.row.register_address }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="data_type" label="数据类型" width="80" align="center" />
            <el-table-column prop="byte_length" label="字节长度" width="70" align="center" />
            <el-table-column prop="unit" label="单位" width="60" align="center" />
            <el-table-column prop="data_range" label="数据范围" width="100" />
            <el-table-column prop="data_conversion_formula" label="转换公式" width="100" />
            <el-table-column prop="read_write_permission" label="读写权限" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="small" :type="scope.row.read_write_permission === '只读' ? 'info' : 'success'">
                  {{ scope.row.read_write_permission }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="update_frequency" label="更新频率" width="70" align="center" />
            <el-table-column prop="sensor_description" label="传感器描述" min-width="150" />
            <el-table-column prop="created_at" label="创建时间" width="150">
              <template slot-scope="scope">
                <span>{{ formatDate(scope.row.created_at) }}</span>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 无数据提示 -->
          <el-empty
            v-else
            description="暂无传感器数据"
            :image-size="200"
          ></el-empty>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDockingList, createDocking, updateDocking, deleteDocking, getDockingSensorList, importDockingExcel, downloadDockingTemplate } from '@/api/system/docking'
import { getDeviceList } from '@/api/device'
import { getGatewayList } from '@/api/gateway'
import Pagination from '@/components/Pagination'
import { parseTime, formatDate } from '@/utils'
import { getToken } from '@/utils/auth'

export default {
  name: 'DockingList',
  components: { Pagination },
  filters: {
    parseTime
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        gateway_name: undefined
      },
      temp: {
        id: undefined,
        gateway_code: '',
        gateway_name: '',
        gateway_model: '',
        docking_device_type: '',
        docking_device_model: '',
        construction_contact_phone: '',
        device_developer_phone: '',
        communication_protocol: '',
        transfer_format: '',
        interface_type: '',
        ip_port: '',
        access_allocation: '',
        security_requirements: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑对接网关',
        create: '添加对接网关'
      },
      rules: {
        gateway_code: [{ required: true, message: '网关编号不能为空', trigger: 'blur' }],
        gateway_name: [{ required: true, message: '网关名称不能为空', trigger: 'blur' }],
        docking_device_type: [{ required: true, message: '请选择对接网关类型', trigger: 'change' }],
        docking_device_model: [{ required: true, message: '对接网关型号不能为空', trigger: 'blur' }],
        communication_protocol: [{ required: true, message: '请选择通信协议', trigger: 'change' }],
        transfer_format: [{ required: true, message: '传输格式不能为空', trigger: 'blur' }],
        interface_type: [{ required: true, message: '接口类型不能为空', trigger: 'blur' }],
        ip_port: [{ required: true, message: 'IP/端口不能为空', trigger: 'blur' }],
        access_allocation: [{ required: true, message: '接入分配不能为空', trigger: 'blur' }],
      },
      // 新增 - 传感器相关数据
      sensorDialogVisible: false,
      sensorLoading: false,
      currentDocking: {},
      sensorList: [],
      // 新增 - Excel导入相关数据
      importDialogVisible: false,
      importGatewayCode: '',
      // 网关列表
      gatewayList: [],
      // 网关搜索关键字
      gatewaySearchKey: '',
      gatewayLoading: false
    }
  },
  created() {
    this.getList()
    this.fetchGatewayList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getDockingList(this.listQuery).then(response => {
        if (response.code === 0) {
          // 处理数据字段映射
          this.list = (response.data.list || []).map(item => {
            // 确保网关编号和网关名称字段标准化
            return {
              ...item,
              gateway_code: item.gateway_code || item.device_code || item.deviceCode,
              gateway_name: item.gateway_name || item.device_name || item.deviceName || item.name
            }
          })
          this.total = response.data.total
        } else {
          this.$message.error(response.message || '获取对接网关列表失败')
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    formatDate(time) {
      if (!time) return '-'
      try {
        // 尝试直接创建Date对象
        let date = new Date(time)
        
        // 如果date无效，尝试替换'-'为'/'再解析
        if (isNaN(date.getTime())) {
          date = new Date(time.replace(/-/g, '/'))
        }
        
        // 如果依然无效，返回原始时间数据
        if (isNaN(date.getTime())) {
          return time.toString()
        }
        
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      } catch (error) {
        console.error('Date formatting error:', error)
        return time.toString()
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        gateway_code: '',
        gateway_name: '',
        gateway_model: '',
        docking_device_type: '',
        docking_device_model: '',
        construction_contact_phone: '',
        device_developer_phone: '',
        communication_protocol: '',
        transfer_format: '',
        interface_type: '',
        ip_port: '',
        access_allocation: '',
        security_requirements: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createDocking(this.temp).then(response => {
            if (response.code === 0) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '对接网关创建成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$message.error(response.message || '创建失败')
            }
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateDocking(tempData).then(response => {
            if (response.code === 0) {
              this.dialogFormVisible = false
              this.$notify({
                title: '成功',
                message: '对接网关更新成功',
                type: 'success',
                duration: 2000
              })
              this.getList()
            } else {
              this.$message.error(response.message || '更新失败')
            }
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该对接网关?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDocking(row.id).then(response => {
          if (response.code === 0) {
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    
    // 新增 - 查看传感器方法
    async handleViewSensors(row) {
      // 标准化当前网关信息字段
      this.currentDocking = {
        ...row,
        gateway_code: row.gateway_code || row.device_code || row.deviceCode,
        gateway_name: row.gateway_name || row.device_name || row.deviceName || row.name
      }
      this.sensorDialogVisible = true
      this.sensorLoading = true
      
      try {
        const response = await getDockingSensorList(row.id)
        
        if (response && response.code === 0 && response.data) {
          this.sensorList = response.data.sensors || []
          // 更新当前网关信息为API返回的完整信息，并确保字段标准化
          const deviceData = response.data || {}
          this.currentDocking = {
            ...deviceData,
            gateway_code: deviceData.gateway_code || deviceData.device_code || deviceData.deviceCode || row.gateway_code,
            gateway_name: deviceData.gateway_name || deviceData.device_name || deviceData.deviceName || deviceData.name || row.gateway_name
          }
        } else {
          this.$message.warning(response?.message || '获取传感器列表失败')
          this.sensorList = []
        }
      } catch (error) {
        console.error('获取传感器列表失败:', error)
        this.sensorList = []
        this.$message.error('获取传感器列表失败，请稍后重试')
      } finally {
        this.sensorLoading = false
      }
    },
    
    // 新增 - 获取传感器状态类型
    getSensorStatusType(status) {
      if (status === 'normal' || status === 1) return 'success'
      if (status === 'warning' || status === 2) return 'warning'
      if (status === 'error' || status === 'danger' || status === 3) return 'danger'
      if (status === 'offline' || status === 0) return 'info'
      return 'info'
    },
    
    // 新增 - 获取传感器状态文本
    getSensorStatusText(status) {
      if (status === 'normal' || status === 1) return '正常'
      if (status === 'warning' || status === 2) return '警告'
      if (status === 'error' || status === 'danger' || status === 3) return '异常'
      if (status === 'offline' || status === 0) return '离线'
      return '未知'
    },
    
    // 新增 - 获取传感器值样式
    getSensorValueClass(sensor) {
      // 检查是否有状态
      if (sensor.status) {
        if (sensor.status === 'normal' || sensor.status === 1) return 'sensor-value normal'
        if (sensor.status === 'warning' || sensor.status === 2) return 'sensor-value warning'
        if (sensor.status === 'error' || sensor.status === 'danger' || sensor.status === 3) return 'sensor-value danger'
        if (sensor.status === 'offline' || sensor.status === 0) return 'sensor-value offline'
      }
      
      // 如果没有状态，但有阈值，则根据阈值判断
      if (sensor.min_threshold !== undefined && sensor.max_threshold !== undefined) {
        const value = parseFloat(sensor.sensor_value)
        if (value < sensor.min_threshold) return 'sensor-value warning'
        if (value > sensor.max_threshold) return 'sensor-value danger'
      }
      
      return 'sensor-value normal'
    },
    handleImportExcel() {
      this.importGatewayCode = ''
      // 获取最新网关列表
      this.fetchGatewayList()
      this.importDialogVisible = true
    },
    beforeExcelUpload(file) {
      const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase())
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('上传文件只能是 Excel 格式!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return isExcel && isLt10M
    },
    downloadTemplate() {
      downloadDockingTemplate().then(response => {
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const fileName = '对接网关导入模板.xlsx'
        
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          // For IE
          window.navigator.msSaveOrOpenBlob(blob, fileName)
        } else {
          const link = document.createElement('a')
          link.href = window.URL.createObjectURL(blob)
          link.download = fileName
          link.click()
          window.URL.revokeObjectURL(link.href)
        }
      }).catch(error => {
        console.error('下载模板失败:', error)
        this.$message.error('下载模板失败，请稍后重试')
      })
    },
    handleExcelUpload(event) {
      const file = event.file
      const formData = new FormData()
      formData.append('file', file)
      
      // 添加网关编号到formData
      if (this.importGatewayCode) {
        formData.append('gateway_code', this.importGatewayCode)
      }
      
      console.log('上传参数:', {file: file.name, gateway_code: this.importGatewayCode})
      
      importDockingExcel(formData)
        .then(response => {
          if (response.code === 0) {
            this.$notify({
              title: '成功',
              message: '导入Excel成功',
              type: 'success',
              duration: 2000
            })
            this.importDialogVisible = false
            this.getList()
          } else {
            this.$message.error(response.message || '导入失败')
          }
          // 触发上传成功回调
          event.onSuccess && event.onSuccess(response)
        })
        .catch(error => {
          console.error('导入失败:', error)
          this.$message.error('导入失败：' + (error.message || '未知错误'))
          // 触发上传失败回调
          event.onError && event.onError(error)
        })
    },
    fetchGatewayList() {
      getGatewayList({ page: 1, page_size: 100 }).then(response => {
        if (response.code === 0) {
          // 标准化网关列表数据
          this.gatewayList = (response.data.list || []).map(item => ({
            ...item,
            gateway_code: item.gateway_code || item.device_code || item.deviceCode,
            gateway_name: item.gateway_name || item.device_name || item.deviceName || item.name
          }))
        } else {
          this.$message.error(response.message || '获取网关列表失败')
        }
      }).catch(() => {
        this.$message.error('获取网关列表失败，请稍后重试')
      })
    },
    searchGateway(query) {
      this.gatewayLoading = true
      getGatewayList({ page: 1, page_size: 10, name: query }).then(response => {
        if (response.code === 0) {
          // 标准化网关列表数据
          this.gatewayList = (response.data.list || []).map(item => ({
            ...item,
            gateway_code: item.gateway_code || item.device_code || item.deviceCode,
            gateway_name: item.gateway_name || item.device_name || item.deviceName || item.name
          }))
        } else {
          this.$message.error(response.message || '获取网关列表失败')
        }
        this.gatewayLoading = false
      }).catch(() => {
        this.gatewayLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 10px;
  }
}

// 操作按钮垂直排列样式
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .el-button {
    margin-left: 0;
    margin-bottom: 5px;
    width: 80px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 新增 - 传感器相关样式
.docking-device-info {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  margin-bottom: 20px;
  
  .info-item {
    flex: 1;
    min-width: 200px;
    margin: 5px 10px;
    
    .label {
      font-weight: bold;
      margin-right: 8px;
      color: #606266;
    }
    
    .value {
      color: #303133;
    }
  }
}

.sensor-table-container {
  margin-top: 20px;
}

.sensor-value {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.normal {
    background-color: #f0f9eb;
    color: #67c23a;
  }
  
  &.warning {
    background-color: #fdf6ec;
    color: #e6a23c;
  }
  
  &.danger {
    background-color: #fef0f0;
    color: #f56c6c;
  }
  
  &.offline {
    background-color: #f4f4f5;
    color: #909399;
  }
}
</style> 