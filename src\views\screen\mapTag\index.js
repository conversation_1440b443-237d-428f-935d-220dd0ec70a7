import * as THREE from 'three';
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { projection } from '../initChinaMap/index.js';

// 标牌组
const tagGroup = new THREE.Group();
tagGroup.name = '标牌组';

// 创建标牌
function createMapTag(cityData, waterObj) {
  // 创建标牌DOM元素
  cityData.forEach(city => {
    const [x, y] = projection([city.longitude, city.latitude]);
    
    // 创建标牌容器
    const tagElement = document.createElement('div');
    tagElement.className = 'map-tag';
    
    // 创建标牌内容
    const contentDiv = document.createElement('div');
    contentDiv.className = 'content';
    
    // 添加标牌文本
    const nameText = document.createElement('div');
    nameText.textContent = city.name + ':';
    contentDiv.appendChild(nameText);
    
    // 添加标牌值
    const valueText = document.createElement('div');
    valueText.id = `tag_value_${city.name}`;
    valueText.textContent = city.value + '人次';
    valueText.style.color = '#ffd700';
    contentDiv.appendChild(valueText);
    
    // 添加标牌箭头
    const arrowDiv = document.createElement('div');
    arrowDiv.className = 'arrow';
    
    // 组装标牌
    tagElement.appendChild(contentDiv);
    tagElement.appendChild(arrowDiv);
    
    // 创建CSS2D对象
    const tagObject = new CSS2DObject(tagElement);
    tagObject.position.set(x, 10, -y);
    tagObject.userData = city;
    
    // 添加到标牌组
    tagGroup.add(tagObject);
  });
  
  // 添加水面特殊标记
  if (waterObj && waterObj.length > 0) {
    waterObj.forEach(water => {
      const [x, y] = projection([water.longitude, water.latitude]);
      
      // 创建水面标记
      const waterTag = document.createElement('div');
      waterTag.className = 'map-tag water-tag';
      
      const contentDiv = document.createElement('div');
      contentDiv.className = 'content';
      
      const nameText = document.createElement('div');
      nameText.textContent = water.name;
      contentDiv.appendChild(nameText);
      
      const arrowDiv = document.createElement('div');
      arrowDiv.className = 'arrow';
      
      waterTag.appendChild(contentDiv);
      waterTag.appendChild(arrowDiv);
      
      // 调整水面标记样式
      contentDiv.style.backgroundColor = 'rgba(0, 149, 255, 0.8)';
      contentDiv.style.borderColor = '#4fc3f7';
      
      // 创建CSS2D对象
      const waterTagObject = new CSS2DObject(waterTag);
      waterTagObject.position.set(x, 5, -y);
      
      // 添加到标牌组
      tagGroup.add(waterTagObject);
    });
  }
  
  return tagGroup;
}

export { createMapTag, tagGroup }; 