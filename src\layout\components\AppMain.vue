<template>
  <section class="app-main" :class="{ 'fullscreen-mode': isFullscreen }">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key"  />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
  },
  data() {
    return {
      isFullscreen: false
    }
  },
  watch: {
    $route: {
      handler(to, from) {
        // 确保 from 存在，避免 from.path 访问错误
        if (from && to.path !== from.path) {
          // 路由变化时重置全局样式
          document.body.style.overflow = '';
          document.body.style.background = '';
          document.documentElement.style.overflow = '';

          // 移除可能的全局样式类
          document.body.classList.remove('screen-mode');
          document.documentElement.classList.remove('screen-mode');
          document.body.classList.remove('data-platform-mode');
          document.documentElement.classList.remove('data-platform-mode');

          // 添加默认主题类
          document.body.classList.add('default-theme');

          // 强制重新渲染
          this.$nextTick(() => {
            window.dispatchEvent(new Event('resize'));
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 监听全屏变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  beforeDestroy() {
    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  methods: {
    handleFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement ||
        !!document.webkitFullscreenElement ||
        !!document.mozFullScreenElement ||
        !!document.msFullscreenElement
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px;
  transition: all 0.3s;

  &.fullscreen-mode {
    padding: 0;
    margin: 0;
    min-height: 100vh;
  }
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;

    &.fullscreen-mode {
      padding-top: 0;
    }
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
