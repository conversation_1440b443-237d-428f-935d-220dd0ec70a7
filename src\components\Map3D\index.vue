<template>
  <div class="app">
    <Map3D 
      v-if="geoJson" 
      :geoJson="geoJson" 
      :dblClickFn="dblClickFn" 
      :projectionFnParam="projectionFnParam"
      :projectList="projectList"
      @project-click="handleProjectClick"
      @webgl-error="handleWebGLError"
    />
    <div v-if="showGuide" class="map-guide">
      <el-alert
        title="地图数据加载失败"
        type="warning"
        description="由于CORS策略限制，无法从远程服务器加载地图数据。请尝试以下解决方案之一："
        show-icon
        :closable="false"
      >
      </el-alert>
      <div class="guide-steps">
        <h4>方案一：本地文件</h4>
        <ol>
          <li>从 <a href="https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json" target="_blank">此链接</a> 下载中国地图数据</li>
          <li>将下载的文件保存为 <code>public/assets/map/100000_full.json</code></li>
          <li>重新加载页面</li>
        </ol>
        
        <h4>方案二：使用代理服务器</h4>
        <ol>
          <li>应用已配置好的代理服务器 <code>/api/map</code> → <code>https://geo.datav.aliyun.com/areas_v3/bound</code></li>
          <li>如果代理服务器仍无法访问，可能是因为您的开发服务器未启用代理配置</li>
          <li>确保运行 <code>npm run dev</code> 启动包含代理配置的开发服务器</li>
        </ol>
        
        <p>更多详细信息，请查看 <code>public/assets/map/README.md</code> 文件。</p>
      </div>
    </div>
    
    <!-- WebGL错误提示 -->
    <div v-if="webglError" class="webgl-error-container">
      <el-alert
        :title="webglError.message"
        type="error"
        description="3D地图功能需要WebGL支持，请确保您的浏览器支持WebGL并已启用。"
        show-icon
        :closable="true"
        @close="webglError = null"
      >
      </el-alert>
      <div class="error-details">
        <p>{{ webglError.details }}</p>
        <h4>可能的解决方案：</h4>
        <ol>
          <li>使用最新版Chrome、Firefox或Edge浏览器</li>
          <li>确保浏览器已启用硬件加速</li>
          <li>更新显卡驱动程序</li>
          <li>如果您使用的是远程桌面或虚拟机，请尝试在本地物理机器上访问</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Map3D from './map3d'
// Import THREE properly
import * as THREE from 'three'
// We'll use dynamic import for CSS2DRenderer to avoid issues

// 检查WebGL是否可用的工具函数
function isWebGLAvailable() {
  try {
    const canvas = document.createElement('canvas');
    return !!(window.WebGLRenderingContext && 
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
  } catch (e) {
    console.error('WebGL检测失败:', e);
    return false;
  }
}

// 地图放大倍率
const MapScale = {
  province: 100,
  city: 100,
  district: 300,
}

export default {
  name: 'App',
  components: {
    Map3D
  },
  props: {
    projectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      geoJson: null,
      mapAdCode: 100000,
      projectionFnParam: {
        center: [104.0, 37.5],
        scale: 40,
      },
      showGuide: false,
      webglError: null,
    }
  },
  watch: {
    mapAdCode() {
      this.queryMapData(this.mapAdCode)
    },
    projectList: {
      handler(newVal) {
        console.log('Map3D组件接收到新的projectList：', newVal)
        // 确保在项目列表更新时，地图数据也会更新
        if (this.geoJson) {
          // 强制更新地图以反映新的项目列表
          this.$nextTick(() => {
            this.$children.forEach(child => {
              if (child.$options.name === 'Map3D') {
                // 如果Map3D子组件已挂载，则调用其initMap方法重新初始化
                if (child.initMap) {
                  child.initMap()
                }
              }
            })
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 请求地图数据
    async queryMapData(code) {
      try {
        let response
        try {
          // 尝试从本地加载地图数据
          response = await axios.get(`/assets/map/${code}_full.json`)
          this.showGuide = false
        } catch (localError) {
          console.warn(`未能从本地加载地图数据: ${localError.message}`)
          
          // 尝试通过代理服务器获取数据
          try {
            console.log('尝试通过代理服务器获取地图数据')
            response = await axios.get(`/api/map/${code}_full.json`)
            this.showGuide = false
          } catch (proxyError) {
            console.warn(`通过代理获取地图数据失败: ${proxyError.message}`)
            this.showGuide = true
            this.$message({
              message: '地图数据加载失败，请确保地图文件已下载到本地',
              type: 'warning'
            })
            return false
          }
        }
        // this.geoJson = response.data
        this.geoJson = response.data
        console.log('projectList', this.projectList)  
        return true
      } catch (error) {
        console.error('Failed to fetch map data:', error)
        this.$message.error('地图数据加载失败')
        this.showGuide = true
        return false
      }
    },
    
    // 双击事件
    dblClickFn(customProperties) {
      console.log('dblClickFn被调用，参数:', customProperties);
      
      // 检查是否为项目点击 (通过判断customProperties是否有id属性)
      const isProjectClick = customProperties && customProperties.id;
      
      // 如果是项目点击，则向上触发事件而不改变地图状态
      if (isProjectClick) {
        console.log('检测到项目点击，触发project-click事件');
        // 向上传递项目点击事件
        this.$emit('project-click', customProperties);
        
        // 手动触发全局事件作为备份
        window.dispatchEvent(new CustomEvent('bridge-project-click', { 
          detail: { project: customProperties }
        }));
        
        return; // 不执行下面的地图数据加载
      }
      
      // 只有当有adcode时才更新地图
      if (customProperties && customProperties.adcode) {
        this.mapAdCode = customProperties.adcode;
        
        // 只有当有centroid和level时才更新投影参数
        if (customProperties.centroid && customProperties.level) {
          this.projectionFnParam = {
            center: customProperties.centroid,
            scale: MapScale[customProperties.level] || 40,
          };
        }
      }
    },
    
    // 设置项目列表
    setProjectList(projects) {
      // 确保项目列表中的每个项目都有adcode属性
      this.projectList = projects.map(project => {
        return {
          ...project,
          adcode: project.adcode || '000000',
          level: project.level || 'province'
        };
      });
    },
    
    // 将经纬度转换为地图坐标
    projectLatLongToPosition(longitude, latitude, projectionParam) {
      const center = projectionParam.center;
      const scale = projectionParam.scale;
      
      return {
        x: (longitude - center[0]) * scale,
        y: (latitude - center[1]) * scale
      };
    },

    // 处理项目点击事件
    handleProjectClick(project) {
      console.log('Map3D组件中接收到项目点击:', project);
      
      // 如果是对象，使用项目数据
      const projectData = typeof project === 'object' ? project : project.detail?.project;
      
      if (!projectData) {
        console.error('无效的项目数据:', project);
        return;
      }
      
      console.log('处理项目点击事件:', projectData.id, projectData.name);
      
      // 向上传递项目点击事件，不再调用dblClickFn方法
      this.$emit('project-click', projectData);
      
      // 手动触发全局事件作为备份
      window.dispatchEvent(new CustomEvent('bridge-project-click', { 
        detail: { project: projectData }
      }));
    },

    // 处理WebGL错误
    handleWebGLError(error) {
      console.warn('WebGL错误:', error);
      this.webglError = error;
      
      // 通知父组件WebGL错误
      this.$emit('webgl-error', error);
      
      // 显示错误消息
      this.$message({
        message: error.message,
        type: 'error',
        duration: 5000,
        showClose: true
      });
    },
  },
  mounted() {
    this.queryMapData(this.mapAdCode) // 默认的中国adcode码
    
    // 创建右侧标签容器
    const rightLabelContainer = document.createElement('div');
    rightLabelContainer.className = 'label-right-container';
    rightLabelContainer.style.position = 'absolute';
    rightLabelContainer.style.top = '0';
    rightLabelContainer.style.right = '20px';
    rightLabelContainer.style.height = '100%';
    rightLabelContainer.style.width = '250px';
    rightLabelContainer.style.zIndex = '150';
    rightLabelContainer.style.pointerEvents = 'none';
    document.querySelector('.map-container')?.appendChild(rightLabelContainer);
    
    // 添加标签样式
    const style = document.createElement('style')
    style.textContent = `
      /* 右侧标签容器 */
      .label-right-container {
        position: absolute;
        top: 0;
        right: 20px;
        height: 100%;
        width: 250px;
        z-index: 150;
        pointer-events: none;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 20px;
      }
      
      /* 引线容器 */
      .map-label-container {
        position: absolute;
        pointer-events: none;
        z-index: 100;
        transform: translateY(-50%);
        transition: all 0.3s ease;
      }
      
      /* 引线 */
      .map-label-line {
        position: absolute;
        height: 1px;
        background-color: rgba(0, 180, 255, 0.7);
        transition: all 0.3s ease;
      }
      
      .map-label-line:hover {
        background-color: rgba(0, 200, 255, 0.9);
        box-shadow: 0 0 8px rgba(0, 200, 255, 0.7);
      }
      
      /* 点位指示器 */
      .map-label-dot {
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: rgba(0, 180, 255, 0.9);
        box-shadow: 0 0 6px rgba(0, 180, 255, 0.7);
        transition: all 0.3s ease;
      }
      
      .map-label-dot:hover {
        transform: translate(-50%, -50%) scale(1.3);
        box-shadow: 0 0 10px rgba(0, 200, 255, 0.9);
      }
      
      .map-label {
        position: absolute;
        color: white;
        background-color: rgba(5, 30, 60, 0.85);
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 13px;
        font-weight: 600;
        transform: translateY(-50%);
        pointer-events: auto;
        cursor: pointer;
        z-index: 100;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(0, 200, 255, 0.4);
        white-space: nowrap;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
      }
      
      .map-label:hover {
        background-color: rgba(0, 100, 180, 0.9);
        transform: translateY(-50%) scale(1.1);
        border-color: rgba(0, 200, 255, 0.8);
        box-shadow: 0 0 15px rgba(0, 200, 255, 0.5);
        z-index: 999;
      }
      
      .map-label:hover + .map-label-line {
        box-shadow: 0 0 8px rgba(0, 200, 255, 0.7);
      }
      
      .map-label.clicked {
        animation: labelClick 0.7s ease-in-out;
        background-color: rgba(0, 200, 255, 0.3);
        border-color: rgba(0, 200, 255, 1);
        box-shadow: 0 0 20px rgba(0, 200, 255, 0.8);
      }
      
      .map-label.status-in-progress .label-icon { 
        background-color: #1E88E5; 
      }
      .map-label.status-completed .label-icon { 
        background-color: #4CAF50; 
      }
      .map-label.status-paused .label-icon { 
        background-color: #FF9800; 
      }
      .map-label.status-preparing .label-icon { 
        background-color: #9C27B0; 
      }
      
      @keyframes labelClick {
        0% { transform: translateY(-50%) scale(1); }
        50% { transform: translateY(-50%) scale(1.2); }
        100% { transform: translateY(-50%) scale(1); }
      }
      
      .label-icon {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
        vertical-align: middle;
        box-shadow: 0 0 4px rgba(255, 255, 255, 0.6);
      }
      
      /* 添加位于最右侧的固定容器 */
      .label-group-container {
        position: absolute;
        top: 0;
        right: 20px;
        height: 100%;
        width: 250px;
        z-index: 150;
        pointer-events: none;
      }

      /* 增强的地图点标记样式 */
      .map-point-marker {
        position: absolute;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background-color: rgba(0, 200, 255, 0.9);
        box-shadow: 0 0 12px rgba(0, 200, 255, 0.8);
        transform: translate(-50%, -50%);
        z-index: 1000;
        cursor: pointer;
        pointer-events: auto;
      }

      /* 内部光晕 */
      .point-inner-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.9);
        animation: innerGlow 1.5s infinite;
      }

      /* 外部光晕 */
      .point-outer-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid rgba(0, 200, 255, 0.6);
        transform: translate(-50%, -50%) scale(1.5);
        animation: outerGlow 3s infinite;
      }

      /* 闪烁效果 */
      .point-flash {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(0, 200, 255, 0.5);
        transform: translate(-50%, -50%) scale(1);
        opacity: 0;
        animation: flashEffect 2s infinite;
      }

      /* 内部光晕动画 */
      @keyframes innerGlow {
        0% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
        50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
        100% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
      }

      /* 外部光晕动画 */
      @keyframes outerGlow {
        0% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.6; }
        50% { transform: translate(-50%, -50%) scale(1.8); opacity: 0.3; }
        100% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.6; }
      }

      /* 闪烁效果动画 */
      @keyframes flashEffect {
        0% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
        10% { transform: translate(-50%, -50%) scale(2); opacity: 0.8; }
        100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
      }

      /* 亮闪烁状态 - 通过JS动态切换 */
      .map-point-marker.flash-bright .point-flash {
        animation-duration: 1s;
        animation-delay: 0s;
      }

      /* 状态颜色样式 */
      .map-point-marker.status-in-progress {
        background-color: rgba(30, 136, 229, 0.9);
        box-shadow: 0 0 12px rgba(30, 136, 229, 0.8);
      }
      .map-point-marker.status-in-progress .point-outer-glow {
        border-color: rgba(30, 136, 229, 0.6);
      }
      .map-point-marker.status-in-progress .point-flash {
        background-color: rgba(30, 136, 229, 0.5);
      }

      .map-point-marker.status-completed {
        background-color: rgba(76, 175, 80, 0.9);
        box-shadow: 0 0 12px rgba(76, 175, 80, 0.8);
      }
      .map-point-marker.status-completed .point-outer-glow {
        border-color: rgba(76, 175, 80, 0.6);
      }
      .map-point-marker.status-completed .point-flash {
        background-color: rgba(76, 175, 80, 0.5);
      }

      .map-point-marker.status-paused {
        background-color: rgba(255, 152, 0, 0.9);
        box-shadow: 0 0 12px rgba(255, 152, 0, 0.8);
      }
      .map-point-marker.status-paused .point-outer-glow {
        border-color: rgba(255, 152, 0, 0.6);
      }
      .map-point-marker.status-paused .point-flash {
        background-color: rgba(255, 152, 0, 0.5);
      }

      .map-point-marker.status-preparing {
        background-color: rgba(156, 39, 176, 0.9);
        box-shadow: 0 0 12px rgba(156, 39, 176, 0.8);
      }
      .map-point-marker.status-preparing .point-outer-glow {
        border-color: rgba(156, 39, 176, 0.6);
      }
      .map-point-marker.status-preparing .point-flash {
        background-color: rgba(156, 39, 176, 0.5);
      }

      /* 悬停效果 */
      .map-point-marker:hover {
        transform: translate(-50%, -50%) scale(1.2);
      }
      .map-point-marker:hover .point-inner-glow {
        animation-duration: 0.8s;
      }
      .map-point-marker:hover .point-outer-glow {
        animation-duration: 1.5s;
      }
      .map-point-marker:hover .point-flash {
        animation-duration: 1s;
      }

      /* 恢复连接线样式 */
      .map-connection-line {
        position: absolute;
        height: 2px;
        background-color: rgba(0, 180, 255, 0.6);
        transform-origin: 0 0;
        pointer-events: none;
        z-index: 999;
        animation: lineFade 2s infinite;
      }

      /* 连接线动画 */
      @keyframes lineFade {
        0% { opacity: 0.3; }
        50% { opacity: 0.8; }
        100% { opacity: 0.3; }
      }

      /* 状态连接线样式 */
      .map-connection-line.status-in-progress {
        background-color: rgba(30, 136, 229, 0.6);
      }

      .map-connection-line.status-completed {
        background-color: rgba(76, 175, 80, 0.6);
      }

      .map-connection-line.status-paused {
        background-color: rgba(255, 152, 0, 0.6);
      }

      .map-connection-line.status-preparing {
        background-color: rgba(156, 39, 176, 0.6);
      }

      /* 添加悬停效果 */
      .map-label-container:hover + .map-connection-line,
      .map-label-container:hover ~ .map-point-marker {
        opacity: 1;
        filter: brightness(1.5);
      }
    `
    document.head.appendChild(style)

    // 声明渲染相关变量
    let renderer = null;

    // 使用正确的DOM元素
    const currentDom = document.querySelector('.map-container');
    if (!currentDom) {
      console.error('未找到地图容器元素');
      return;
    }

    try {
      // 只在WebGL可用时创建标签渲染器
      if (isWebGLAvailable()) {
        // 使用动态导入CSS2DRenderer
        import('three/examples/jsm/renderers/CSS2DRenderer').then(({ CSS2DRenderer }) => {
          try {
            let labelRenderer = new CSS2DRenderer();
            labelRenderer.setSize(currentDom.clientWidth, currentDom.clientHeight);
            labelRenderer.domElement.style.position = 'absolute';
            labelRenderer.domElement.style.top = '0px';
            labelRenderer.domElement.style.left = '0px';
            labelRenderer.domElement.style.pointerEvents = 'none';
            labelRenderer.domElement.style.zIndex = '10';
            
            // 确保CSS2D渲染器的子元素能接收事件
            labelRenderer.domElement.addEventListener('click', function(event) {
              // 只允许标签接收事件
              if (event.target.classList.contains('map-label') || 
                  event.target.parentElement?.classList.contains('map-label')) {
                event.stopPropagation();
              } else if (renderer && renderer.domElement) {
                // 其他元素的事件应该穿透
                const mouseEvent = new MouseEvent('click', {
                  clientX: event.clientX,
                  clientY: event.clientY,
                  bubbles: true,
                  cancelable: true
                });
                renderer.domElement.dispatchEvent(mouseEvent);
              }
            });
            
            // 保存标签渲染器引用以便后续清理
            this._labelRenderer = labelRenderer;
            
            const labelRendererDom = this.$refs.map2dContainer;
            if (labelRendererDom?.childNodes[0]) {
              labelRendererDom.removeChild(labelRendererDom.childNodes[0]);
            }
            
            if (labelRendererDom) {
              labelRendererDom.appendChild(labelRenderer.domElement);
            }
          } catch (error) {
            console.error('CSS2DRenderer初始化失败:', error);
            // 创建一个虚拟标签渲染器
            this._labelRenderer = {
              setSize: () => {},
              render: () => {},
              domElement: document.createElement('div')
            };
          }
        }).catch(error => {
          console.error('CSS2DRenderer导入失败:', error);
          this.handleWebGLError({
            message: 'CSS2DRenderer加载失败',
            details: error.message || '无法加载必要的THREE.js组件'
          });
        });
      }
    } catch (e) {
      console.error('WebGL相关初始化失败:', e);
      this.handleWebGLError({
        message: 'WebGL初始化过程中发生错误',
        details: e.message || '未知错误'
      });
    }
  }
}
</script>

<style>
.app {
  width: 100%;
  height: 100vh;
  background: transparent; /* 确保App容器背景透明 */
  position: relative;
  overflow: hidden; /* 防止内容溢出 */
}

/* 移除科技动态背景元素 */
.app::before {
  display: none;
}

/* 移除数字雨效果 */
.app::after {
  display: none;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent; /* 确保地图容器背景透明 */
  z-index: 5;
}

.map-canvas {
  width: 100%;
  height: 100%;
  background: transparent !important; /* 强制Canvas背景透明 */
}

/* 扫描线动画 */
@keyframes scanlines {
  0% {
    background-position: 0 -100vh;
  }
  100% {
    background-position: 0 100vh;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

@keyframes digitalRain {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 1000px;
  }
}

.map-guide {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600px;
  background-color: rgba(5, 30, 60, 0.8); /* 更新为与新背景匹配的颜色 */
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  z-index: 10;
  color: white;
  border: 1px solid rgba(0, 200, 255, 0.4); /* 添加与主页一致的边框 */
}

/* 为弹窗添加发光边框效果 */
.map-guide::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 9px;
  background: linear-gradient(135deg, 
    rgba(0, 200, 255, 0.6) 0%,
    rgba(0, 200, 255, 0.2) 25%, 
    transparent 50%, 
    rgba(0, 200, 255, 0.2) 75%, 
    rgba(0, 200, 255, 0.6) 100%);
  z-index: -1;
  animation: borderGlow 5s linear infinite;
}

@keyframes borderGlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

.guide-steps {
  margin-top: 15px;
  
  ol {
    padding-left: 20px;
    
    li {
      margin-bottom: 10px;
      color: rgba(255, 255, 255, 0.9);
    }
  }
  
  code {
    background-color: rgba(0, 200, 255, 0.2);
    padding: 2px 4px;
    border-radius: 4px;
    color: #ffffff;
  }
}

.webgl-error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600px;
  background-color: rgba(5, 30, 60, 0.8); /* 更新为与新背景匹配的颜色 */
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  z-index: 10;
  color: white;
  border: 1px solid rgba(0, 200, 255, 0.4); /* 添加与主页一致的边框 */
}

.webgl-error-container::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 9px;
  background: linear-gradient(135deg, 
    rgba(255, 71, 87, 0.6) 0%,
    rgba(255, 71, 87, 0.2) 25%, 
    transparent 50%, 
    rgba(255, 71, 87, 0.2) 75%, 
    rgba(255, 71, 87, 0.6) 100%);
  z-index: -1;
  animation: errorGlow 5s linear infinite;
}

@keyframes errorGlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

.error-details {
  margin-top: 15px;
}

.error-details h4 {
  margin-bottom: 10px;
}

.error-details ol {
  padding-left: 20px;
}

.error-details li {
  margin-bottom: 5px;
}
</style> 