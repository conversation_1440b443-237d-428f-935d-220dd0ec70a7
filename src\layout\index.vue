<template>
  <div :class="[classObj, {'fullscreen-active': isFullscreen}]" class="app-wrapper">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <div class="sidebar-container" :class="{'hide-in-fullscreen': isFullscreen}" v-if="!isFullscreen">
      <Sidebar />
    </div>
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }" v-show="!isFullscreen">
        <Navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <AppMain />
      <right-panel v-if="showSettings && !isFullscreen">
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar, Settings, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    // 监听全屏变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
    
    // 初始检查全屏状态
    this.checkFullscreenState();
  },
  beforeDestroy() {
    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    
    // 检查当前全屏状态
    checkFullscreenState() {
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      
      console.log('Layout: 检查全屏状态:', this.isFullscreen ? '全屏' : '非全屏');
    },
    
    handleFullscreenChange() {
      // 更新全屏状态
      const wasFullscreen = this.isFullscreen;
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );

      console.log('Layout: 全屏状态变化为', this.isFullscreen ? '全屏' : '非全屏');

      // 根据全屏状态更新类和样式
      if (this.isFullscreen) {
        // 添加全屏类
        document.body.classList.add('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.add('fullscreen-mode', 'fullscreen-active');
        
        // 确保侧边栏完全隐藏 - 更强力的隐藏方式
        const sidebarEl = document.querySelector('.sidebar-container');
        if (sidebarEl) {
          // 添加类
          sidebarEl.classList.add('hide-in-fullscreen');
          
          // 直接设置样式
          sidebarEl.style.display = 'none';
          sidebarEl.style.visibility = 'hidden';
          sidebarEl.style.opacity = '0';
          sidebarEl.style.width = '0';
          sidebarEl.style.height = '0';
          sidebarEl.style.overflow = 'hidden';
          sidebarEl.style.position = 'absolute';
          sidebarEl.style.left = '-9999px';
          sidebarEl.style.top = '-9999px';
          sidebarEl.style.zIndex = '-9999';
          sidebarEl.style.transform = 'translateX(-100%)';
          sidebarEl.style.transition = 'none';
          sidebarEl.style.pointerEvents = 'none';
          sidebarEl.style.backgroundColor = 'transparent';
          
          // 隐藏所有子元素
          const sidebarChildren = sidebarEl.querySelectorAll('*');
          sidebarChildren.forEach(child => {
            if (child.style.display !== 'none') {
              child.dataset.originalDisplay = child.style.display;
              child.style.display = 'none';
              child.style.visibility = 'hidden';
              child.style.opacity = '0';
            }
          });
        }
        
        // 确保主容器占满整个屏幕
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
          mainContainer.style.marginLeft = '0';
          mainContainer.style.width = '100%';
          mainContainer.style.maxWidth = '100vw';
          mainContainer.style.left = '0';
        }
        
        // 如果是全屏状态，确保设置面板关闭
        const rightPanelEl = document.querySelector('.rightPanel-container');
        if (rightPanelEl && rightPanelEl.__vue__) {
          rightPanelEl.__vue__.show = false;
        }
      } else {
        // 移除全屏类
        document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');
        
        // 恢复侧边栏显示
        const sidebarEl = document.querySelector('.sidebar-container');
        if (sidebarEl) {
          // 移除类
          sidebarEl.classList.remove('hide-in-fullscreen');
          
          // 重置样式
          sidebarEl.style.display = '';
          sidebarEl.style.visibility = '';
          sidebarEl.style.opacity = '';
          sidebarEl.style.width = '';
          sidebarEl.style.height = '';
          sidebarEl.style.overflow = '';
          sidebarEl.style.position = '';
          sidebarEl.style.left = '';
          sidebarEl.style.top = '';
          sidebarEl.style.zIndex = '';
          sidebarEl.style.transform = '';
          sidebarEl.style.transition = '';
          sidebarEl.style.pointerEvents = '';
          sidebarEl.style.backgroundColor = '';
          
          // 恢复子元素的显示
          const sidebarChildren = sidebarEl.querySelectorAll('*');
          sidebarChildren.forEach(child => {
            if (child.dataset.originalDisplay) {
              child.style.display = child.dataset.originalDisplay;
              child.style.visibility = '';
              child.style.opacity = '';
              delete child.dataset.originalDisplay;
            } else {
              child.style.display = '';
              child.style.visibility = '';
              child.style.opacity = '';
            }
          });
        }
        
        // 恢复主容器样式
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
          mainContainer.style.marginLeft = '';
          mainContainer.style.width = '';
          mainContainer.style.maxWidth = '';
          mainContainer.style.left = '';
        }
        
        // 如果是从全屏状态退出，强制重新渲染侧边栏
        if (wasFullscreen) {
          this.$nextTick(() => {
            // 确保侧边栏内容正确渲染
            const sidebarComponent = this.$children.find(child => child.$options.name === 'Sidebar');
            if (sidebarComponent) {
              sidebarComponent.$forceUpdate();
            }
            
            // 重新初始化侧边栏状态
            this.$store.dispatch('app/toggleSideBar', false);
            this.$nextTick(() => {
              this.$store.dispatch('app/toggleSideBar', true);
            });
          });
        }
      }
      
      // 触发窗口大小变化事件，确保组件正确渲染
      this.$nextTick(() => {
        window.dispatchEvent(new Event('resize'));
      });
    }
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }

  &.fullscreen-active {
    background-color: #001a33;
    
    .sidebar-container {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      width: 0 !important;
      overflow: hidden !important;
    }
  }
}

.hide-in-fullscreen {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
