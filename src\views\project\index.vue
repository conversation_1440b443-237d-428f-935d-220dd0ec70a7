<template>
  <div class="project-container">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增项目
      </el-button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索项目名称"
        style="width: 200px; margin-left: 16px"
        clearable
        @clear="handleSearch"
        @keyup.enter.native.passive="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
      <el-select v-model="statusFilter" placeholder="项目状态" clearable style="width: 120px; margin-left: 16px;">
        <el-option label="全部" value=""></el-option>
        <el-option label="筹备中" :value="0"></el-option>
        <el-option label="进行中" :value="1"></el-option>
        <el-option label="已完成" :value="2"></el-option>
        <el-option label="已暂停" :value="3"></el-option>
      </el-select>
    </div>

    <!-- 项目列表 -->
    <el-table
      v-loading="loading"
      :data="projectList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="ID" width="80" fixed="left" />
      <el-table-column prop="number" label="项目号" width="150" fixed="left" />
      <el-table-column prop="name" label="项目名称" min-width="150" />
      <el-table-column prop="account_name" label="所属单位" min-width="120" />
      <el-table-column label="项目位置" min-width="150">
        <template slot-scope="scope">
          {{ scope.row.location || '未设置' }}
          <el-tag v-if="scope.row.longitude && scope.row.latitude" size="mini" type="info">
            {{ getFormattedCoordinate(scope.row.longitude) }}, {{ getFormattedCoordinate(scope.row.latitude) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="造桥机型号" min-width="150">
        <template slot-scope="scope">
          <el-tag 
            v-for="model in getBridgeMachineModels(scope.row)" 
            :key="model" 
            size="small" 
            style="margin-right: 5px; margin-bottom: 5px;"
          >
            {{ model }}
          </el-tag>
          <span v-if="!getBridgeMachineModels(scope.row).length">
            暂无型号
          </span>
        </template>
      </el-table-column>
      <el-table-column label="安装日期" min-width="120" sortable>
        <template slot-scope="scope">
          {{ formatDate(scope.row.installation_date || scope.row.installationDate || scope.row.installDate) || '未设置' }}
        </template>
      </el-table-column>
      <el-table-column label="工程进度" min-width="150">
        <template slot-scope="scope">
          <el-progress
            :percentage="getProgressPercentage(scope.row)"
            :status="getProgressStatus(getProgressPercentage(scope.row))"
            :stroke-width="15"
          ></el-progress>
          <div class="progress-info">
            <span>已完成: {{ getProgressPercentage(scope.row).toFixed(2) }}%</span>
            <span>剩余: {{ (100 - getProgressPercentage(scope.row)).toFixed(2) }}%</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="项目描述" min-width="200" />
      <el-table-column label="项目图片" min-width="120">
        <template slot-scope="scope">
          <el-image 
            :src="scope.row.image" 
            :preview-src-list="[scope.row.image]"
            fit="cover"
            style="width: 80px; height: 80px;"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTypeFromCode(scope.row.status)">
            {{ getStatusTextFromCode(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预计完成时间" min-width="180">
        <template slot-scope="scope">
          {{ formatRelativeDate(scope.row.estimated_completion) }}
        </template>
      </el-table-column>
      <el-table-column label="实际完成时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.actual_completion ? formatDateTime(scope.row.actual_completion) : '未完成' }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="150">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change.native.passive="handleSizeChange"
        @current-change.native.passive="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 项目表单抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="50%"
      @close="handleDrawerClose"
    >
      <el-form
        ref="projectForm"
        :model="projectForm"
        :rules="projectRules"
        label-width="100px"
      >
        <el-form-item label="项目号" prop="number">
          <el-input 
            v-model="projectForm.number" 
            readonly
            disabled
            placeholder="项目号不可修改"
          ></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称"></el-input>
        </el-form-item>
        <el-form-item label="项目位置" prop="location">
          <div class="location-input-group">
            <el-input 
              v-model="projectForm.location" 
              placeholder="请输入项目位置"
              style="width: 100%;"
            ></el-input>
            <el-button 
              type="primary" 
              icon="el-icon-map-location" 
              @click="showMapDialog"
              style="margin-left: 10px;"
            >地图选点</el-button>
          </div>
          <div class="coordinates-input">
            <el-input
              v-model.number="projectForm.longitude"
              placeholder="经度"
              size="small"
              style="width: 180px;"
              @change="validateCoordinate('longitude')"
            >
              <template slot="append">°E</template>
            </el-input>
            <el-input
              v-model.number="projectForm.latitude"
              placeholder="纬度"
              size="small"
              style="width: 180px;"
              @change="validateCoordinate('latitude')"
            >
              <template slot="append">°N</template>
            </el-input>
            <el-button 
              type="primary" 
              size="small"
              @click="handleCoordinatesSearch"
            >定位</el-button>
          </div>
        </el-form-item>
        <el-form-item label="造桥机型号" prop="bridge_machine_models">
          <el-select
            v-model="projectForm.bridge_machine_models"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入造桥机型号"
            style="width: 100%;"
          >
            <el-option
              v-for="item in bridgeMachineOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安装日期" prop="installation_date">
          <el-date-picker
            v-model="projectForm.installation_date"
            type="date"
            placeholder="选择安装日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="工程进度" prop="progress_percentage">
          <el-slider
            v-model="projectForm.progress_percentage"
            :step="0.1"
            :min="0"
            :max="100"
            show-input
            :format-tooltip="value => `${value}%`"
          ></el-slider>
        </el-form-item>
        <el-form-item label="预计完成时间" prop="estimated_completion">
          <el-date-picker
            v-model="projectForm.estimated_completion"
            type="datetime"
            placeholder="选择预计完成时间"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="实际完成时间" prop="actual_completion" v-if="projectForm.status === 2">
          <el-date-picker
            v-model="projectForm.actual_completion"
            type="datetime"
            placeholder="选择实际完成时间"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目状态" prop="status">
          <el-select v-model="projectForm.status" placeholder="请选择项目状态">
            <el-option label="筹备中" :value="0"></el-option>
            <el-option label="进行中" :value="1"></el-option>
            <el-option label="已完成" :value="2"></el-option>
            <el-option label="已暂停" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述" prop="description">
          <el-input
            type="textarea"
            :rows="3"
            v-model="projectForm.description"
            placeholder="请输入项目描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="项目图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="projectForm.image" :src="projectForm.image" class="image">
            <i v-else class="el-icon-plus image-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-drawer>

    <!-- 地图选择对话框 -->
    <el-dialog
      title="选择项目位置"
      :visible.sync="mapDialogVisible"
      width="80%"
      class="map-dialog"
      @closed="handleMapDialogClosed"
      :before-close="handleMapDialogBeforeClose"
    >
      <div class="map-container">
        <div class="map-search">
          <el-input
            v-model="searchAddress"
            placeholder="搜索地址"
            clearable
            @keyup.enter.native.passive="handleAddressSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleAddressSearch"></el-button>
          </el-input>
        </div>
        <div id="mapContainer" class="map"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="mapDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleLocationConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getProjectList, 
  getProjectDetail, 
  createProject, 
  updateProject, 
  deleteProject 
} from '@/api/project'
import { getAllModels } from '@/api/system/model'

export default {
  name: 'ProjectManagement',
  data() {
    return {
      loading: false,
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      projectList: [],
      drawerVisible: false,
      drawerTitle: '新增项目',
      isEdit: false,
      debounceTimer: null,
      projectForm: {
        id: null,
        number: '',
        name: '',
        description: '',
        status: 0,
        estimated_completion: '',
        actual_completion: null,
        location: '',
        longitude: null,
        latitude: null,
        progress: '0.00',
        progress_percentage: 0
      },
      projectRules: {
        number: [
          { required: false, message: '项目号可以为空，系统将自动生成', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9\-_]{3,30}$/, message: '项目号只能包含字母、数字、下划线和连字符，长度3-30', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入项目位置', trigger: 'blur' }
        ],
        longitude: [
          { required: true, message: '请输入经度', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' }
        ],
        installation_date: [
          { required: true, message: '请选择安装日期', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择项目状态', trigger: 'change' }
        ],
        bridge_machine_models: [
          { type: 'array', required: false, message: '请选择造桥机型号', trigger: 'change' }
        ],
        progress_percentage: [
          { type: 'number', message: '进度必须是数字', trigger: 'change' }
        ]
      },
      mapDialogVisible: false,
      map: null,
      marker: null,
      searchAddress: '',
      bridgeMachineOptions: []
    }
  },
  created() {
    console.log('项目管理组件已创建');
    this.fetchProjects();
    this.fetchBridgeMachineModels();
  },
  mounted() {
    this.fetchProjects();
    
    // 调试输入框样式
    setTimeout(() => {
      const inputEl = document.querySelector('.el-form-item__content .el-input__inner');
      if (inputEl) {
        console.log('Input styles:', window.getComputedStyle(inputEl));
        
        // 强制移除可能的只读或禁用属性
        inputEl.removeAttribute('readonly');
        inputEl.removeAttribute('disabled');
      }
    }, 1000);
    
    // 添加被动事件监听器，解决性能警告
    this.$nextTick(() => {
      // 处理地图容器的触摸和滚轮事件
      const mapEl = document.getElementById('mapContainer');
      if (mapEl) {
        // 使用选项 { passive: true } 添加监听器
        const wheelOpts = { passive: true };
        mapEl.addEventListener('touchstart', () => {}, wheelOpts);
        mapEl.addEventListener('touchmove', () => {}, wheelOpts);
        mapEl.addEventListener('touchend', () => {}, wheelOpts);
        mapEl.addEventListener('wheel', () => {}, wheelOpts);
      }
      
      // 处理整个文档的滚轮事件
      document.addEventListener('wheel', () => {}, { passive: true });
      document.addEventListener('touchstart', () => {}, { passive: true });
      document.addEventListener('touchmove', () => {}, { passive: true });
    });
  },
  methods: {
    // 添加防抖动方法
    debounce(fn, delay = 300) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        fn();
      }, delay);
    },
    
    // 获取项目列表
    async fetchProjects() {
      try {
        this.loading = true;
        console.log('开始获取项目列表...');
        
        // 构建查询参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          query: this.searchQuery,
          status: this.statusFilter
        };
        
        console.log('查询参数:', params);
        const response = await getProjectList(params);
        console.log('获取项目列表响应:', response);
        
        // 使用 code 而不是 statusCode
        if (response && response.code === 0 && response.data) {
          // 处理返回的数据
          if (Array.isArray(response.data.list)) {
            this.projectList = response.data.list.map(project => {
              // 确保经纬度是数字类型
              if (project.longitude) {
                project.longitude = parseFloat(project.longitude);
              }
              if (project.latitude) {
                project.latitude = parseFloat(project.latitude);
              }
              
              // 确保进度是数字类型
              if (project.progress_percentage !== undefined) {
                project.progress_percentage = parseFloat(project.progress_percentage);
              } else if (project.progress !== undefined) {
                project.progress = parseFloat(project.progress);
              }
              
              return project;
            });
            
            this.total = response.data.total || 0;
            console.log('项目列表更新成功，共', this.projectList.length, '条数据');
          } else {
            console.warn('响应数据中list不是数组:', response.data);
            this.projectList = [];
            this.total = 0;
          }
        } else {
          console.warn('获取项目列表失败:', response);
          this.$message.error(response?.message || '获取项目列表失败');
          this.projectList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取项目列表出错:', error);
        this.$message.error('获取项目列表失败，请稍后重试');
        this.projectList = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    
    // 搜索项目
    handleSearch() {
      this.debounce(() => {
        this.currentPage = 1;
        this.fetchProjects();
      });
    },
    
    // 添加项目
    async handleAdd() {
      console.log('添加新项目');
      this.isEdit = false;
      this.drawerTitle = '新增项目';
      
      // 确保造桥机型号已加载
      if (this.bridgeMachineOptions.length === 0) {
        await this.fetchBridgeMachineModels();
      }
      
      // 生成临时项目号
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const tempProjectNo = `PRJ${year}${month}${day}${random}`;
      
      // 格式化当前日期为 yyyy-MM-dd
      const today = `${year}-${month}-${day}`;
      
      this.projectForm = {
        id: null,
        number: tempProjectNo,
        name: '',
        description: '',
        status: 0, // 默认为筹备中
        estimated_completion: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 默认30天后
        actual_completion: null, // 实际完成日期，初始为空
        // 使用正确的字段名
        installation_date: today,
        bridge_machine_models: [], // 初始化为空数组
        // 位置和进度字段
        location: '',
        longitude: null,
        latitude: null,
        progress_percentage: 0 // 使用正确的进度字段名
      };
      
      this.drawerVisible = true;
    },
    
    // 编辑项目
    async handleEdit(row) {
      this.isEdit = true;
      this.drawerTitle = '编辑项目';
      
      try {
        // 确保造桥机型号已加载
        if (this.bridgeMachineOptions.length === 0) {
          await this.fetchBridgeMachineModels();
        }
        
        // 获取项目详情
        const response = await getProjectDetail(row.id);
        
        // 使用 code 而不是 statusCode
        if (response && response.code === 0) {
          // 直接使用后端返回的数据
          const projectData = response.data;
          
          // 处理安装日期
          const installDate = projectData.installation_date || projectData.installationDate || projectData.installDate || '';
          
          // 处理桥机型号
          let bridgeMachineModels = [];
          if (projectData.bridge_machine_models) {
            if (Array.isArray(projectData.bridge_machine_models)) {
              bridgeMachineModels = projectData.bridge_machine_models;
            } else if (typeof projectData.bridge_machine_models === 'string') {
              try {
                const parsed = JSON.parse(projectData.bridge_machine_models);
                bridgeMachineModels = Array.isArray(parsed) ? parsed : [parsed];
              } catch (e) {
                bridgeMachineModels = [projectData.bridge_machine_models];
              }
            }
          } else if (projectData.bridgeMachineModels) {
            if (Array.isArray(projectData.bridgeMachineModels)) {
              bridgeMachineModels = projectData.bridgeMachineModels;
            } else if (typeof projectData.bridgeMachineModels === 'string') {
              try {
                const parsed = JSON.parse(projectData.bridgeMachineModels);
                bridgeMachineModels = Array.isArray(parsed) ? parsed : [parsed];
              } catch (e) {
                bridgeMachineModels = [projectData.bridgeMachineModels];
              }
            }
          } else if (projectData.bbm_type) {
            if (Array.isArray(projectData.bbm_type)) {
              bridgeMachineModels = projectData.bbm_type;
            } else if (typeof projectData.bbm_type === 'string') {
              try {
                const parsed = JSON.parse(projectData.bbm_type);
                bridgeMachineModels = Array.isArray(parsed) ? parsed : [parsed];
              } catch (e) {
                bridgeMachineModels = [projectData.bbm_type];
              }
            } else {
              bridgeMachineModels = [projectData.bbm_type];
            }
          }
          
          // 处理进度
          let progressPercentage = 0;
          if (projectData.progress_percentage !== undefined) {
            progressPercentage = parseFloat(projectData.progress_percentage);
          } else if (projectData.progress !== undefined) {
            progressPercentage = parseFloat(projectData.progress) * 100;
          }
          
          // 设置表单数据
          this.projectForm = {
            id: projectData.id,
            number: projectData.number,
            name: projectData.name,
            description: projectData.description || '',
            status: parseInt(projectData.status) || 0,
            estimated_completion: projectData.estimated_completion || '',
            actual_completion: projectData.actual_completion || '',
            installation_date: installDate,
            bridge_machine_models: bridgeMachineModels,
            progress_percentage: progressPercentage,
            location: projectData.location || '',
            longitude: projectData.longitude ? parseFloat(projectData.longitude) : null,
            latitude: projectData.latitude ? parseFloat(projectData.latitude) : null,
            image: projectData.image || ''
          };
          
          console.log('编辑表单数据:', this.projectForm);
        } else {
          this.$message.error(response.message || '获取项目详情失败');
          return;
        }
      } catch (error) {
        console.error('获取项目详情出错:', error);
        this.$message.error('获取项目详情失败，请稍后重试');
        return;
      }
      
      this.drawerVisible = true;
    },
    
    // 删除项目
    async handleDelete(id) {
      try {
        await this.$confirm('确定要删除该项目吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const response = await deleteProject(id);
        
        // 使用 code 而不是 statusCode
        if (response && response.code === 0) {
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.fetchProjects();
        } else {
          this.$message.error(response?.message || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除项目出错:', error);
          this.$message.error('删除失败，请稍后重试');
        }
      }
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.projectForm.validate().catch(() => false);
        if (!valid) {
          this.$message.warning('请完善表单信息');
          return;
        }
        
        this.loading = true;
        console.log('开始提交表单...');
        
        // 准备提交的数据
        const submitData = {
          ...this.projectForm,
          // 确保经纬度是字符串
          longitude: this.projectForm.longitude ? this.projectForm.longitude.toString() : '0.000000',
          latitude: this.projectForm.latitude ? this.projectForm.latitude.toString() : '0.000000',
          // 确保进度是正确的格式
          progress_percentage: this.projectForm.progress_percentage !== undefined ? 
            this.projectForm.progress_percentage.toString() : '0'
        };
        
        console.log('提交表单数据:', submitData);
        
        // 发送请求
        let response;
        if (this.isEdit) {
          // 更新项目
          response = await updateProject(submitData);
        } else {
          // 创建项目
          response = await createProject(submitData);
        }
        
        console.log('API响应:', response);
        
        // 处理响应 - 使用 code 而不是 statusCode
        if (response && response.code === 0) {
          console.log('操作成功');
          // 使用Element UI的成功消息类型
          this.$message({
            type: 'success',
            message: this.isEdit ? '更新项目成功' : '创建项目成功',
            duration: 2000
          });
          
          // 先关闭抽屉
          this.drawerVisible = false;
          
          // 然后重新获取项目列表
          setTimeout(() => {
            this.fetchProjects();
          }, 300);
        } else {
          this.$message.error(response?.message || (this.isEdit ? '更新项目失败' : '创建项目失败'));
        }
      } catch (error) {
        console.error('提交项目数据出错:', error);
        this.$message.error('操作失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.projectForm) {
        this.$refs.projectForm.resetFields()
      }
      
      this.projectForm = {
        id: null,
        number: '',
        name: '',
        location: '',
        longitude: null,
        latitude: null,
        description: '',
        installDate: '',
        status: 0,
        progress: 0,
        completedWork: 0,
        estimated_completion: '',
        image: '',
        bridgeMachineModels: []
      }
    },
    
    // 关闭抽屉
    handleDrawerClose() {
      this.resetForm()
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchProjects()
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchProjects()
    },
    
    // 获取项目状态类型
    getStatusType(status) {
      const statusTypeMap = {
        0: 'info',
        1: 'primary',
        2: 'success',
        3: 'warning'
      };
      return statusTypeMap[status] || 'info';
    },
    
    // 获取项目状态文本
    getStatusText(status) {
      const statusMap = {
        0: '筹备中',
        1: '进行中',
        2: '已完成',
        3: '已暂停'
      };
      return statusMap[status] || '未知状态';
    },
    
    // 获取进度状态
    getProgressStatus(percentage) {
      // Element UI 只接受这几个状态值: 'success', 'exception', 'warning' 或不设置
      if (percentage >= 100) {
        return 'success';
      } else if (percentage < 0) {
        return 'exception';
      }
      // 不返回任何值，使用默认状态
      return undefined;
    },
    
    // 显示地图对话框
    showMapDialog() {
      this.mapDialogVisible = true;
      this.$nextTick(() => {
        this.initMap();
        // 如果已有经纬度，则设置中心点和标记
        if (this.projectForm.longitude && this.projectForm.latitude) {
          const lnglat = [this.projectForm.longitude, this.projectForm.latitude];
          this.map.setCenter(lnglat);
          this.updateMarker(lnglat);
        }
      });
    },
    
    // 初始化地图
    initMap() {
      try {
        console.log('初始化地图...');
        if (this.map) {
          console.log('销毁旧地图实例');
          this.map.destroy();
        }
        
        // 确保DOM元素存在
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) {
          console.error('找不到地图容器元素！');
          this.$message.error('地图初始化失败：找不到地图容器');
          return;
        }
        
        console.log('创建新的地图实例');
        this.map = new AMap.Map('mapContainer', {
          zoom: 10,
          center: [116.397428, 39.90923],
          resizeEnable: true,
          touchZoom: {
            passive: true
          }
        });
        
        // 检查地图是否成功创建
        if (!this.map) {
          console.error('地图实例创建失败');
          this.$message.error('地图初始化失败');
          return;
        }
        
        console.log('地图初始化成功，添加点击事件');
        // 添加点击事件
        this.map.on('click', (e) => {
          try {
            console.log('地图点击事件触发，坐标:', e.lnglat);
            const lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
            this.updateMarker(lnglat);
            this.getAddress(lnglat);
          } catch (error) {
            console.error('地图点击事件处理失败:', error);
            this.$message.error('操作失败：' + error.message);
          }
        }, { passive: true });
        
        // 添加工具条和比例尺
        console.log('添加地图控件');
        AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
          this.map.addControl(new AMap.ToolBar());
          this.map.addControl(new AMap.Scale());
        });
        
        console.log('地图及控件初始化完成');
      } catch (error) {
        console.error('地图初始化失败:', error);
        this.$message.error('地图初始化失败：' + error.message);
      }
    },
    
    // 更新标记
    updateMarker(lnglat) {
      try {
        console.log('更新地图标记，坐标:', lnglat);
        if (this.marker) {
          console.log('更新现有标记位置');
          this.marker.setPosition(lnglat);
        } else {
          console.log('创建新标记');
          this.marker = new AMap.Marker({
            position: lnglat,
            draggable: true,
            map: this.map
          });
          
          // 拖拽结束后获取地址
          this.marker.on('dragend', () => {
            try {
              const position = this.marker.getPosition();
              console.log('标记拖拽结束，新位置:', position);
              this.getAddress([position.lng, position.lat]);
            } catch (error) {
              console.error('标记拖拽处理失败:', error);
              this.$message.error('获取位置信息失败');
            }
          }, { passive: true });
        }
        
        // 更新表单中的经纬度，确保保留6位小数精度
        const lng = parseFloat(lnglat[0]);
        const lat = parseFloat(lnglat[1]);
        this.projectForm.longitude = lng.toFixed(6);
        this.projectForm.latitude = lat.toFixed(6);
        console.log('表单经纬度已更新:', this.projectForm.longitude, this.projectForm.latitude);
      } catch (error) {
        console.error('更新标记失败:', error);
        this.$message.error('更新位置标记失败：' + error.message);
      }
    },
    
    // 获取地址
    getAddress(lnglat) {
      try {
        console.log('开始根据坐标获取地址信息:', lnglat);
        
        // 更新经纬度，确保无论如何都会有值
        this.projectForm.longitude = parseFloat(lnglat[0]).toFixed(6);
        this.projectForm.latitude = parseFloat(lnglat[1]).toFixed(6);
        
        // 使用1.4.15版本兼容写法
        const geocoder = new AMap.Geocoder({
          radius: 1000,
          extensions: 'all'
        });
        
        console.log('发送地理编码请求...');
        // 转换为符合1.4.15版本的经纬度对象
        const lnglatObj = new AMap.LngLat(lnglat[0], lnglat[1]);
        
        geocoder.getAddress(lnglatObj, (status, result) => {
          console.log('地理编码响应:', status, result);
          if (status === 'complete' && result.info === 'OK') {
            this.projectForm.location = result.regeocode.formattedAddress;
            console.log('获取地址成功:', this.projectForm.location);
            this.$message.success('地址获取成功');
          } else {
            console.error('获取地址失败:', status, result);
            // 如果自动获取失败，尝试填入省市区信息
            try {
              if (result && result.regeocode && result.regeocode.addressComponent) {
                const addr = result.regeocode.addressComponent;
                const location = [
                  addr.province,
                  addr.city,
                  addr.district,
                  addr.township
                ].filter(item => item).join('');
                if (location) {
                  this.projectForm.location = location;
                  console.log('获取简化地址:', location);
                  this.$message.success('获取简化地址成功');
                } else {
                  this.$message.warning('获取地址失败，请手动输入');
                }
              } else {
                this.$message.warning('获取地址失败，请手动输入');
              }
            } catch (err) {
              console.error('处理地址组件出错:', err);
              this.$message.warning('获取地址失败，请手动输入');
            }
          }
        });
      } catch (error) {
        console.error('地址获取过程出错:', error);
        this.$message.error('地址获取失败：' + error.message);
      }
    },
    
    // 处理坐标搜索
    handleCoordinatesSearch() {
      this.debounce(() => {
        try {
          if (this.projectForm.longitude === null || this.projectForm.longitude === undefined || 
              this.projectForm.latitude === null || this.projectForm.latitude === undefined) {
            this.$message.warning('请输入经纬度');
            return;
          }
          
          // 确保经纬度是有效数字
          const longitude = parseFloat(this.projectForm.longitude);
          const latitude = parseFloat(this.projectForm.latitude);
          
          if (isNaN(longitude) || isNaN(latitude)) {
            this.$message.warning('请输入有效的经纬度数值');
            return;
          }
          
          // 确保经纬度在有效范围内
          if (longitude < -180 || longitude > 180) {
            this.$message.warning('经度应在 -180 到 180 之间');
            return;
          }
          
          if (latitude < -90 || latitude > 90) {
            this.$message.warning('纬度应在 -90 到 90 之间');
            return;
          }
          
          console.log('使用经纬度搜索位置:', longitude, latitude);
          
          // 更新表单中的经纬度为格式化后的值
          this.projectForm.longitude = longitude;
          this.projectForm.latitude = latitude;
          
          const lnglat = [longitude, latitude];
          
          if (!this.mapDialogVisible) {
            this.showMapDialog();
            // 在地图加载完成后设置中心点和标记
            this.$nextTick(() => {
              if (this.map) {
                this.map.setCenter(lnglat);
                this.updateMarker(lnglat);
                this.getAddress(lnglat);
              }
            });
          } else {
            if (this.map) {
              this.map.setCenter(lnglat);
              this.updateMarker(lnglat);
              this.getAddress(lnglat);
            }
          }
        } catch (error) {
          console.error('坐标搜索出错:', error);
          this.$message.error('坐标搜索失败: ' + error.message);
        }
      });
    },
    
    // 处理地址搜索
    handleAddressSearch() {
      this.debounce(() => {
        try {
          if (!this.searchAddress) {
            this.$message.warning('请输入地址');
            return;
          }
          
          console.log('搜索地址:', this.searchAddress);
          
          // 修改为兼容1.4.15版本
          const geocoder = new AMap.Geocoder({
            city: '全国',
            radius: 1000
          });
          
          console.log('发送地址搜索请求...');
          geocoder.getLocation(this.searchAddress, (status, result) => {
            console.log('地址搜索响应:', status, result);
            if (status === 'complete' && result.info === 'OK' && result.geocodes && result.geocodes.length > 0) {
              const location = result.geocodes[0].location;
              console.log('搜索到的位置:', location);
              
              // 格式化经纬度为6位小数
              const lng = parseFloat(location.lng).toFixed(6);
              const lat = parseFloat(location.lat).toFixed(6);
              
              this.map.setCenter([location.lng, location.lat]);
              this.updateMarker([location.lng, location.lat]);
              this.projectForm.location = result.geocodes[0].formattedAddress || this.searchAddress;
              
              // 确保经纬度直接更新到表单
              this.projectForm.longitude = lng;
              this.projectForm.latitude = lat;
              
              this.$message.success(`地址搜索成功: ${this.projectForm.location} (${lng}, ${lat})`);
            } else {
              console.error('地址搜索失败:', status, result);
              this.$message.warning('地址搜索失败，请尝试更精确的地址');
            }
          });
        } catch (error) {
          console.error('地址搜索过程出错:', error);
          this.$message.error('地址搜索失败：' + error.message);
        }
      });
    },
    
    // 确认位置选择
    handleLocationConfirm() {
      if (!this.projectForm.location || !this.projectForm.longitude || !this.projectForm.latitude) {
        this.$message.warning('请选择项目位置');
        return;
      }
      
      // 确保表单数据正确更新
      console.log('确认选择位置:', this.projectForm.location, this.projectForm.longitude, this.projectForm.latitude);
      this.$message.success(`已选择位置: ${this.projectForm.location} (${this.projectForm.longitude}, ${this.projectForm.latitude})`);
      
      // 关闭对话框
      this.mapDialogVisible = false;
    },
    
    // 处理地图对话框关闭前的逻辑
    handleMapDialogBeforeClose(done) {
      // 如果用户点击取消按钮关闭，可以在这里处理
      // 但我们不需要重置经纬度，因为用户可能是确认了选择
      done();
    },
    
    // 进度变更
    handleProgressChange(value) {
      // 将百分比转换为小数
      this.projectForm.progress = (value / 100).toFixed(2);
    },

    // 上传图片成功
    handleImageSuccess(res, file) {
      this.projectForm.image = URL.createObjectURL(file.raw)
    },

    // 上传图片前的校验
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },

    // 添加状态映射方法
    mapStatusFromBackend(backendStatus) {
      // 后端状态映射到前端状态
      const statusMap = {
        0: 'preparing',
        1: 'in_progress',
        2: 'completed',
        3: 'paused'
      };
      return statusMap[backendStatus] || 'preparing';
    },

    // 添加状态映射方法
    mapStatusToBackend(frontendStatus) {
      // 前端状态映射到后端状态
      const statusMap = {
        'preparing': 0,
        'in_progress': 1,
        'completed': 2,
        'paused': 3
      };
      return statusMap[frontendStatus] || 0;
    },

    // 获取项目状态类型
    getStatusTypeFromCode(statusCode) {
      const statusTypeMap = {
        0: 'info',
        1: 'primary',
        2: 'success',
        3: 'warning'
      };
      return statusTypeMap[statusCode] || 'info';
    },

    // 获取项目状态文本
    getStatusTextFromCode(statusCode) {
      const statusMap = {
        0: '筹备中',
        1: '进行中',
        2: '已完成',
        3: '已暂停'
      };
      return statusMap[statusCode] || '未知状态';
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      
      const date = new Date(dateTime);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 格式化日期（只有年月日）
    formatDate(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },
    
    // 格式化相对日期（与当前时间比较）
    formatRelativeDate(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      const now = new Date();
      const diffDays = Math.floor((date - now) / (1000 * 60 * 60 * 24));
      
      // 格式化基础日期
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const baseDate = `${year}-${month}-${day}`;
      
      // 附加相对时间信息
      if (diffDays > 0) {
        return `${baseDate} (还有${diffDays}天)`;
      } else if (diffDays < 0) {
        return `${baseDate} (已过${Math.abs(diffDays)}天)`;
      } else {
        return `${baseDate} (今天)`;
      }
    },

    // 处理地图对话框关闭
    handleMapDialogClosed() {
      // 不再重置经纬度，保留用户选择的位置
      // 留空实现，确保不会重置表单中的经纬度值
    },

    // 获取桥机型号
    getBridgeMachineModels(project) {
      if (!project) return [];
      
      // 尝试从不同可能的字段名获取桥机型号
      if (project.bridge_machine_models) {
        if (Array.isArray(project.bridge_machine_models)) {
          return project.bridge_machine_models;
        } else if (typeof project.bridge_machine_models === 'string') {
          try {
            const parsed = JSON.parse(project.bridge_machine_models);
            return Array.isArray(parsed) ? parsed : [parsed];
          } catch (e) {
            return [project.bridge_machine_models];
          }
        }
      }
      
      if (project.bridgeMachineModels && Array.isArray(project.bridgeMachineModels)) {
        return project.bridgeMachineModels;
      }
      
      if (project.bbm_type) {
        if (typeof project.bbm_type === 'string') {
          try {
            const parsed = JSON.parse(project.bbm_type);
            return Array.isArray(parsed) ? parsed : [parsed];
          } catch (e) {
            return [project.bbm_type];
          }
        }
        return [project.bbm_type];
      }
      
      return [];
    },

    // 获取进度百分比
    getProgressPercentage(project) {
      if (!project) return 0;
      
      let percentage = 0;
      
      // 尝试从不同可能的字段名获取进度
      if (project.progress_percentage !== undefined) {
        percentage = parseFloat(project.progress_percentage);
      } else if (project.progress !== undefined) {
        percentage = parseFloat(project.progress) * 100;
      }
      
      // 确保返回有效数字，且在0-100范围内
      if (isNaN(percentage)) return 0;
      if (percentage < 0) return 0;
      if (percentage > 100) return 100;
      
      return percentage;
    },

    // 格式化坐标
    getFormattedCoordinate(value) {
      if (value === null || value === undefined) return '0.0000';
      try {
        return parseFloat(value).toFixed(4);
      } catch (e) {
        return '0.0000';
      }
    },

    // 验证坐标输入
    validateCoordinate(coordinate) {
      if (this.projectForm[coordinate] === null || this.projectForm[coordinate] === undefined) {
        this.$message.warning(`请输入有效的${coordinate === 'longitude' ? '经度' : '纬度'}`);
        return;
      }
      
      let value = parseFloat(this.projectForm[coordinate]);
      
      // 将输入转换为数字并更新
      this.projectForm[coordinate] = value;
      
      if (coordinate === 'longitude') {
        // 经度范围检查 (-180到180)
        if (value < -180 || value > 180) {
          this.$message.warning('经度应在 -180 到 180 之间');
          this.projectForm.longitude = value > 180 ? 180 : (value < -180 ? -180 : value);
        }
      } else if (coordinate === 'latitude') {
        // 纬度范围检查 (-90到90)
        if (value < -90 || value > 90) {
          this.$message.warning('纬度应在 -90 到 90 之间');
          this.projectForm.latitude = value > 90 ? 90 : (value < -90 ? -90 : value);
        }
      }
    },

    // 获取所有造桥机型号
    async fetchBridgeMachineModels() {
      try {
        const response = await getAllModels();
        if (response && response.code === 0 && response.data) {
          // 处理嵌套的list结构的响应数据
          const modelList = response.data.list || response.data;
          
          // 转换为选择器需要的格式
          this.bridgeMachineOptions = modelList.map(model => ({
            value: model.model_code || model.model_name,
            label: model.model_name || model.model_code
          }));
          console.log('造桥机型号获取成功:', this.bridgeMachineOptions);
        } else {
          console.warn('获取造桥机型号失败:', response);
          // 保留默认选项
        }
      } catch (error) {
        console.error('获取造桥机型号出错:', error);
        // 保留默认选项
      }
    }
  },
  computed: {
    isProjectNoDisabled() {
      return this.isEdit;
    }
  },
  beforeDestroy() {
    // 清除所有计时器和事件监听器
    clearTimeout(this.debounceTimer);
    
    // 清除所有事件监听器
    if (this.map) {
      this.map.destroy();
    }
    
    // 移除添加的文档级别事件监听器
    document.removeEventListener('wheel', () => {}, { passive: true });
    document.removeEventListener('touchstart', () => {}, { passive: true });
    document.removeEventListener('touchmove', () => {}, { passive: true });
  }
}
</script>

<style lang="scss" scoped>
.project-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.progress-text {
  margin-left: 10px;
  color: #666;
}

::v-deep .el-progress-bar__inner {
  transition: width 0.6s ease;
}

.map-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
  }
}

.map-container {
  position: relative;
  height: 500px;
  width: 100%;
  
  .map {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .map-search {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 300px;
    z-index: 99;
    background: rgba(255, 255, 255, 0.85);
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.location-input-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.coordinates-input {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .coordinates-separator {
    color: #606266;
    font-size: 14px;
  }
}

.image-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.image-uploader .el-upload:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.image {
  width: 178px;
  height: 178px;
  display: block;
}

.drawer-footer {
  text-align: right;
  margin-top: 20px;
}

.editable-input {
  ::v-deep .el-input__inner {
    background-color: #fff !important;
    cursor: text !important;
    color: #606266 !important;
    border-color: #dcdfe6 !important;
  }
}

.custom-input {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #409EFF;
  }
  
  &::placeholder {
    color: #c0c4cc;
  }
}
</style> 