/* 中央列样式 */
.model-viewer-panel {
  height: 100vh;
  width: 100vw;
  background-color: transparent !important; /* 强制透明背景 */
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: visible;
  z-index: 1; /* 确保在左右两侧模块下方 */
  pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
  backdrop-filter: none !important; /* 禁用背景模糊 */
  -webkit-backdrop-filter: none !important;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5vh 1vw;
  background-color: rgba(0, 40, 80, 0.5);
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  color: #00a8ff;
  font-size: 0.9rem;
}

.widget-status {
  font-size: 0.75rem;
  color: #7fdbff;
}

.widget-status i {
  margin-right: 0.3vw;
}

.model-content {
  flex: 1;
  position: relative;
  overflow: visible;
  width: 100vw;
  height: 100vh; /* 全屏高度 */
  pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
}

#center-model-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: auto !important;
  background-color: transparent !important; /* 强制透明背景 */
  min-height: 100vh;
  max-height: none;
  backdrop-filter: none !important; /* 禁用背景模糊 */
  -webkit-backdrop-filter: none !important;
}

/* 确保在大屏幕模式下3D模型容器正确显示 */
@media screen and (min-width: 1920px) {
  #center-model-container {
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    overflow: visible !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 10 !important; /* 提高z-index，确保能接收鼠标事件 */
    pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
    background-color: transparent !important; /* 透明背景 */
    min-height: 100vh !important; /* 确保最小高度为视口高度 */
    max-height: none !important; /* 移除最大高度限制 */
  }

  #center-model-container canvas {
    width: 100vw !important;
    height: 100vh !important;
    display: block !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 10 !important; /* 提高z-index，确保能接收鼠标事件 */
    pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
    min-height: 100vh !important; /* 确保最小高度为视口高度 */
    background-color: transparent !important; /* 透明背景 */
  }

  .model-viewer-panel {
    height: 100vh !important;
    width: 100vw !important;
    overflow: visible !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
    background-color: transparent !important; /* 透明背景 */
  }

  .model-content {
    height: 100vh !important;
    width: 100vw !important;
    overflow: visible !important;
    pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    min-height: 100vh !important; /* 确保最小高度为视口高度 */
    max-height: none !important; /* 移除最大高度限制 */
    background-color: transparent !important; /* 透明背景 */
  }
}

.model-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 26, 51, 0.3);
  z-index: 400;
  pointer-events: auto !important;
  visibility: visible !important;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 168, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00a8ff;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #00a8ff;
  font-size: 0.9rem;
}

/* 备用图片样式 */
.fallback-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 400;
  background-color: transparent !important; /* 强制透明背景 */
  pointer-events: auto !important;
  visibility: visible !important;
  backdrop-filter: none !important; /* 禁用背景模糊 */
  -webkit-backdrop-filter: none !important;
}

.fallback-text {
  text-align: center;
  color: #ffffff;
  padding: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(0, 168, 255, 0.5);
  box-shadow: 0 0 20px rgba(0, 168, 255, 0.3);
}

.fallback-text i {
  font-size: 48px;
  color: #ffaa00;
  margin-bottom: 20px;
  display: block;
}

.fallback-text p {
  font-size: 24px;
  margin: 0;
}

.model-info-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 30, 60, 0.7);
  border: 1px solid rgba(0, 168, 255, 0.5);
  border-radius: 4px;
  padding: 8px;
  z-index: 5;
  backdrop-filter: blur(3px);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  max-width: 250px;
}

.model-info-item {
  margin-bottom: 5px;
  font-size: 0.8rem;
  display: flex;
  justify-content: space-between;
}

.info-label {
  color: #7fdbff;
  margin-right: 10px;
}

.info-value {
  color: #fff;
  font-weight: bold;
}

.info-value.status {
  color: #00ff9d;
  text-shadow: 0 0 5px rgba(0, 255, 157, 0.7);
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px 0;
  background-color: rgba(0, 30, 60, 0.5);
  border-top: 1px solid rgba(0, 168, 255, 0.3);
  height: 50px; /* 固定高度，确保与左右两侧模块底部对齐 */
  box-sizing: border-box;
}

.control-btn {
  background-color: rgba(0, 40, 80, 0.8);
  border: 1px solid rgba(0, 168, 255, 0.5);
  color: #00a8ff;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s;
}

.control-btn:hover {
  background-color: rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  color: #fff;
}

.control-btn i {
  margin-right: 5px;
}

/* 显示模式切换按钮样式 */
.display-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.toggle-btn {
  background-color: rgba(0, 40, 80, 0.8);
  border: 1px solid rgba(0, 168, 255, 0.5);
  color: #00a8ff;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.toggle-btn:hover {
  background-color: rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 168, 255, 0.7);
  color: #fff;
}
