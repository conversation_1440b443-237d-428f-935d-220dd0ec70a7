# 中国地图数据文件

由于跨域资源共享 (CORS) 限制，应用无法直接从阿里云 DataV 服务加载地图数据。有以下几种解决方案：

## 解决方案一：使用本地文件

### 必要文件

1. `100000_full.json` - 中国全图
2. 其他需要的地图数据文件（省级、市级等）

### 下载方法

#### 方法一：直接下载

1. 访问 https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json
2. 保存文件到 `public/assets/map/100000_full.json`

#### 方法二：使用命令行下载

```bash
# 在项目根目录执行
curl -o public/assets/map/100000_full.json https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json
```

## 解决方案二：使用开发代理服务器

项目已在 `vue.config.js` 中配置了代理服务器：

```js
devServer: {
  proxy: {
    '/api/map': {
      target: 'https://geo.datav.aliyun.com',
      changeOrigin: true,
      pathRewrite: {
        '^/api/map': '/areas_v3/bound'
      }
    }
  }
}
```

这使得在开发环境中，可以通过 `/api/map/100000_full.json` 访问远程地图数据，绕过浏览器的 CORS 限制。

**注意：** 此方法仅在开发环境中有效。在生产环境中，仍需要使用本地文件或配置服务器端代理。

## 解决方案三：配置后端代理

如果您有自己的后端服务，可以在后端实现代理接口：

```js
// Node.js Express 示例
app.get('/api/map/:code', async (req, res) => {
  try {
    const { code } = req.params;
    const response = await axios.get(`https://geo.datav.aliyun.com/areas_v3/bound/${code}_full.json`);
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 应用行为

应用会按以下顺序尝试加载地图数据：

1. 首先尝试从本地 `/assets/map/${code}_full.json` 加载
2. 如果本地加载失败，尝试通过代理 `/api/map/${code}_full.json` 加载
3. 如果两种方法都失败，显示警告消息和解决指南

## 支持的地图代码

- `100000` - 中国全图
- `110000` - 北京
- `310000` - 上海
- `440000` - 广东
- ...等其他省市代码 