<template>
  <div class="camera-modal-overlay" v-if="visible" @click.self="closeModal">
    <div class="camera-modal-content">
      <!-- 背景装饰元素 -->
      <div class="tech-bg-element tech-circle"></div>
      <div class="tech-bg-element tech-grid"></div>
      <div class="tech-bg-element tech-lines"></div>

      <!-- 模态框头部 -->
      <div class="camera-modal-header">
        <div class="header-left">
          <i class="fas fa-video"></i>
          <h3>{{ camera ? camera.label : '摄像头详情' }}</h3>
        </div>
        <div class="header-right">
          <div class="camera-status-indicator" v-if="camera">
            <span class="status-dot" :class="getStatusClass(camera.status)"></span>
            <span class="status-text">{{ getStatusText(camera.status) }}</span>
          </div>
          <button class="close-button" @click="closeModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="camera-modal-body" v-if="camera">
        <div class="modal-content-wrapper">
          <!-- 左侧：视频播放区域 -->
          <div class="video-section">
            <div class="video-container" ref="videoContainer">
              <!-- Video.js 将在这里初始化 -->
            </div>

            <!-- 视频控制按钮 -->
            <div class="video-controls">
              <button class="tech-button screenshot-button" @click="takeScreenshot" v-if="videoPlayer">
                <i class="fas fa-camera"></i>
                截图
              </button>
              <button class="tech-button" @click="toggleMute" v-if="videoPlayer">
                <i :class="isMuted ? 'fas fa-volume-mute' : 'fas fa-volume-up'"></i>
                {{ isMuted ? '取消静音' : '静音' }}
              </button>
            </div>


          </div>

          <!-- 右侧：摄像头信息 -->
          <div class="info-section">
            <!-- 状态卡片 -->
            <div class="status-card">
              <div class="status-header">
                <div class="status-title">
                  <i class="fas fa-video"></i>
                  <span>{{ camera.label }}</span>
                </div>
                <div class="status-indicator" :class="getStatusClass(camera.status)">
                  <span class="status-dot"></span>
                  <span>{{ getStatusText(camera.status) }}</span>
                </div>
              </div>

              <div class="status-body">
                <div class="status-row">
                  <div class="status-cell">
                    <div class="status-cell-icon"><i class="fas fa-fingerprint"></i></div>
                    <div class="status-cell-value">{{ camera.id }}</div>
                    <div class="status-cell-label">摄像头ID</div>
                  </div>
                  <div class="status-cell">
                    <div class="status-cell-icon"><i class="fas fa-server"></i></div>
                    <div class="status-cell-value">{{ camera.device }}</div>
                    <div class="status-cell-label">设备名称</div>
                  </div>
                </div>

                <div class="status-row">
                  <div class="status-cell">
                    <div class="status-cell-icon"><i class="fas fa-camera"></i></div>
                    <div class="status-cell-value">{{ getCameraTypeText(camera.type) }}</div>
                    <div class="status-cell-label">摄像头类型</div>
                  </div>
                  <div class="status-cell">
                    <!-- 视频信息显示 -->
                    <div class="status-cell-icon"><i class="fas fa-info-circle"></i></div>
                    <div class="status-cell-value">视频信息</div>
                    <div class="status-cell-label">详细参数</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 视频信息面板 -->
            <div class="status-card" v-if="videoPlayer">
              <div class="status-header">
                <div class="status-title">
                  <i class="fas fa-film"></i>
                  <span>视频参数</span>
                </div>
                <!-- 移除实时状态指示器 -->
              </div>
              <div class="status-body">
                <div class="video-info-overlay">
                  <div class="video-info-item">
                    <i class="fas fa-clock"></i>
                    <span>播放时间：{{ currentTime }}</span>
                  </div>
                  <div class="video-info-item">
                    <i class="fas fa-signal"></i>
                    <span>视频质量：{{ videoQuality }}</span>
                  </div>
                  <div class="video-info-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>帧率：{{ frameRate }} FPS</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="camera-modal-footer">
        <div class="tech-corner top-left"></div>
        <div class="tech-corner top-right"></div>
        <div class="tech-corner bottom-left"></div>
        <div class="tech-corner bottom-right"></div>
      </div>
    </div>

    <!-- 截图预览 -->
    <div class="screenshot-preview" v-if="showScreenshotPreview" @click="showScreenshotPreview = false">
      <img :src="screenshotData" alt="截图预览" />
      <button class="screenshot-close" @click="showScreenshotPreview = false">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CameraDetailModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    camera: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      videoPlayer: null,
      isMuted: false,
      // 视频信息
      currentTime: '00:00:00',
      videoQuality: 'HD 1080p',
      frameRate: '25',
      // 设备信息
      uptime: '12小时36分钟',
      lastUpdated: '2023-05-20 15:30:45',
      // 实时监控数据
      cpuUsage: 45,
      memoryUsage: 32,
      downloadSpeed: '5.2',
      uploadSpeed: '1.8',
      // 截图相关
      screenshotData: null,
      showScreenshotPreview: false
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.camera) {
        // 当弹窗显示且有摄像头数据时，初始化视频播放器
        this.$nextTick(() => {
          this.initVideoPlayer();
        });
      } else {
        // 当弹窗关闭时，销毁视频播放器
        this.destroyVideoPlayer();
      }
    }
  },
  methods: {
    closeModal() {
      this.destroyVideoPlayer();
      this.$emit('close');
    },

    initVideoPlayer() {
      // 确保video.js已加载
      if (typeof window.videojs === 'undefined') {
        console.error('Video.js not loaded!');
        this.$message ? this.$message.error('视频播放器未加载，请刷新页面重试') : console.error('视频播放器未加载，请刷新页面重试');
        return;
      }

      // 确保本地 videojs 变量可用
      const videojs = window.videojs;

      // 清空容器
      const videoContainer = this.$refs.videoContainer;
      if (!videoContainer) {
        console.error('找不到视频容器');

        // 在小屏幕上，可能需要延迟一点时间再次尝试获取容器
        setTimeout(() => {
          if (this.$refs.videoContainer) {
            console.log('延迟后找到视频容器，继续初始化播放器');
            this.initVideoPlayer();
          } else {
            console.error('延迟后仍找不到视频容器，无法播放视频');
          }
        }, 500);

        return;
      }

      videoContainer.innerHTML = '';

      // 创建视频元素
      const videoElement = document.createElement('video');
      videoElement.className = 'video-js vjs-default-skin vjs-big-play-centered';
      videoElement.id = `modal-video-player-${this.camera.id}`;
      videoElement.controls = true;
      videoElement.preload = 'auto';
      videoElement.width = videoContainer.clientWidth || 400;
      videoElement.height = videoContainer.clientHeight || 300;
      videoElement.poster = '/static/images/video-loading.jpg'; // 添加加载中的海报图片

      // 添加到容器
      videoContainer.appendChild(videoElement);

      // 确保摄像头有URL
      if (!this.camera.url && (this.camera.ezviz_url || this.camera.rtspUrl || this.camera.stream_url || this.camera.orig_url)) {
        this.camera.url = this.camera.ezviz_url || this.camera.rtspUrl || this.camera.stream_url || this.camera.orig_url;
      }

      // 如果仍然没有URL，显示错误信息
      if (!this.camera.url) {
        console.error('摄像头没有可用的URL');

        // 更新摄像头状态为故障
        this.updateCameraStatus(2, '无可用视频源');

        videoContainer.innerHTML = `
          <div class="video-error-container">
            <div class="error-message">
              <i class="fas fa-exclamation-triangle"></i>
              <p>无可用视频源</p>
              <span class="error-details">摄像头未配置视频流地址或地址无效</span>
            </div>
            <div class="retry-button" onclick="document.getElementById('retry-camera-${this.camera.id}').click()">
              <i class="fas fa-sync-alt"></i> 重试连接
            </div>
          </div>
        `;

        // 添加一个隐藏的按钮用于重试
        const retryButton = document.createElement('button');
        retryButton.id = `retry-camera-${this.camera.id}`;
        retryButton.style.display = 'none';
        retryButton.addEventListener('click', () => {
          // 重置状态
          this.updateCameraStatus(null, null);
          // 重新尝试初始化
          this.initVideoPlayer();
        });
        document.body.appendChild(retryButton);
        return;
      }

      // 使用getVideoType方法获取视频类型
      const videoType = this.getVideoType(this.camera.url);

      console.log('初始化弹窗播放器，URL:', this.camera.url, '类型:', videoType);

      // 创建播放器实例
      this.videoPlayer = videojs(`modal-video-player-${this.camera.id}`, {
        sources: [{
          src: this.camera.url,
          type: videoType
        }],
        autoplay: true,
        muted: true, // 默认静音，避免自动播放策略限制
        controls: true,
        fluid: true,
        preload: 'auto',
        responsive: true,
        controlBar: {
          children: [
            'playToggle',
            'volumePanel',
            'currentTimeDisplay',
            'timeDivider',
            'durationDisplay',
            'progressControl',
            'fullscreenToggle'
          ],
          volumePanel: {
            inline: false,
            vertical: true
          }
        },
        html5: {
          vhs: {
            overrideNative: true
          },
          nativeVideoTracks: false,
          nativeAudioTracks: false,
          nativeTextTracks: false
        }
      });

      // 设置加载超时检测
      let loadTimeout = setTimeout(() => {
        if (this.videoPlayer && !this.videoPlayer.hasStarted_) {
          console.error('视频加载超时');
          this.handleVideoError('视频加载超时，请检查网络连接或视频源');
        }
      }, 15000); // 15秒超时

      // 监听错误事件
      this.videoPlayer.on('error', (error) => {
        console.error('视频播放错误:', error);
        clearTimeout(loadTimeout);

        // 移除加载指示器
        const loadingIndicator = videoContainer.querySelector('.video-loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.remove();
        }

        // 尝试使用备用URL
        if (this.camera.ezviz_url && this.camera.ezviz_url !== this.camera.url) {
          console.log('尝试使用萤石云URL:', this.camera.ezviz_url);
          this.videoPlayer.src([{
            src: this.camera.ezviz_url,
            type: 'application/x-mpegURL'
          }]);
          this.videoPlayer.play().catch(e => {
            console.error('萤石云URL播放失败:', e);
            this.tryNextBackupUrl();
          });
        } else if (this.camera.rtspUrl && this.camera.rtspUrl !== this.camera.url) {
          this.tryNextBackupUrl();
        } else {
          // 所有URL都失败，显示错误
          this.handleVideoError('视频源无法播放，请检查摄像头状态');
        }
      });

      // 监听播放成功事件，清除超时
      this.videoPlayer.on('playing', () => {
        clearTimeout(loadTimeout);

        // 移除加载指示器
        const loadingIndicator = videoContainer.querySelector('.video-loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.remove();
        }

        // 如果之前有错误状态，恢复正常
        if (this.camera.errorStatus) {
          this.updateCameraStatus(1, '正常');
          this.camera.errorStatus = false;
        }
      });

      // 监听时间更新事件
      this.videoPlayer.on('timeupdate', () => {
        this.updateVideoInfo();
      });

      // 监听播放就绪事件
      this.videoPlayer.on('ready', () => {
        console.log('视频播放器就绪');

        // 在小屏幕上，可能需要手动调整播放器大小
        if (window.innerWidth <= 768) {
          this.videoPlayer.dimensions(videoContainer.clientWidth, videoContainer.clientHeight);
        }
      });

      // 监听播放事件
      this.videoPlayer.on('play', () => {
        console.log('视频开始播放');
        // 启动模拟数据更新
        this.startDataSimulation();
      });

      // 更新静音状态
      this.isMuted = this.videoPlayer.muted();

      // 初始化视频信息
      this.updateVideoInfo();

      // 初始化设备信息
      this.updateDeviceInfo();

      // 监听窗口大小变化，调整播放器大小
      window.addEventListener('resize', this.handleResize);
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.videoPlayer && this.$refs.videoContainer) {
        const videoContainer = this.$refs.videoContainer;
        this.videoPlayer.dimensions(videoContainer.clientWidth, videoContainer.clientHeight);
      }
    },

    // 获取视频类型
    getVideoType(url) {
      if (!url) return 'application/x-mpegURL';

      const lowerUrl = url.toLowerCase();

      // 萤石云URL特殊处理
      if (lowerUrl.includes('open.ys7.com') || lowerUrl.includes('ezopen://')) {
        return 'application/x-mpegURL';
      }

      // 根据URL后缀判断视频类型
      if (lowerUrl.endsWith('.mp4')) {
        return 'video/mp4';
      } else if (lowerUrl.endsWith('.webm')) {
        return 'video/webm';
      } else if (lowerUrl.endsWith('.ogg') || lowerUrl.endsWith('.ogv')) {
        return 'video/ogg';
      } else if (lowerUrl.includes('.m3u8')) {
        return 'application/x-mpegURL';
      } else if (lowerUrl.includes('rtsp://')) {
        return 'application/x-rtsp';
      } else if (lowerUrl.includes('rtmp://')) {
        return 'rtmp/mp4';
      } else {
        // 如果没有明确的扩展名，尝试根据URL中的其他信息判断
        if (lowerUrl.includes('live') || lowerUrl.includes('stream') || lowerUrl.includes('hls') ||
          lowerUrl.includes('openlive')) {
          return 'application/x-mpegURL'; // 可能是HLS流
        } else {
          return 'video/mp4'; // 默认为MP4
        }
      }
    },

    // 更新视频信息
    updateVideoInfo() {
      if (!this.videoPlayer) return;

      // 更新当前时间
      const time = this.videoPlayer.currentTime();
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time % 3600) / 60);
      const seconds = Math.floor(time % 60);
      this.currentTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      // 随机更新帧率（模拟数据）
      if (Math.random() > 0.8) {
        this.frameRate = (24 + Math.floor(Math.random() * 6)).toString();
      }
    },

    // 更新设备信息
    updateDeviceInfo() {
      // 设置最后更新时间
      const now = new Date();
      this.lastUpdated = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

      // 计算在线时长（模拟数据）
      const hours = Math.floor(Math.random() * 24);
      const minutes = Math.floor(Math.random() * 60);
      this.uptime = `${hours}小时${minutes}分钟`;
    },

    // 启动模拟数据更新
    startDataSimulation() {
      // 每3秒更新一次数据
      this.dataSimulationTimer = setInterval(() => {
        // 更新CPU使用率
        this.cpuUsage = Math.min(100, Math.max(10, this.cpuUsage + (Math.random() > 0.5 ? 1 : -1) * Math.floor(Math.random() * 5)));

        // 更新内存使用率
        this.memoryUsage = Math.min(100, Math.max(10, this.memoryUsage + (Math.random() > 0.5 ? 1 : -1) * Math.floor(Math.random() * 3)));

        // 更新网络速度
        this.downloadSpeed = (parseFloat(this.downloadSpeed) + (Math.random() > 0.5 ? 0.1 : -0.1) * Math.random()).toFixed(1);
        this.uploadSpeed = (parseFloat(this.uploadSpeed) + (Math.random() > 0.5 ? 0.1 : -0.1) * Math.random()).toFixed(1);

        // 确保值在合理范围内
        if (parseFloat(this.downloadSpeed) < 0.5) this.downloadSpeed = '0.5';
        if (parseFloat(this.uploadSpeed) < 0.2) this.uploadSpeed = '0.2';
      }, 3000);
    },

    // 截图功能
    takeScreenshot() {
      if (!this.videoPlayer) return;

      try {
        // 获取视频元素
        const videoElement = this.videoPlayer.el().querySelector('video');
        if (!videoElement) {
          console.error('找不到视频元素');
          return;
        }

        // 创建Canvas
        const canvas = document.createElement('canvas');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;

        // 绘制视频帧
        const ctx = canvas.getContext('2d');
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        // 获取图像数据
        this.screenshotData = canvas.toDataURL('image/png');

        // 显示截图预览
        this.showScreenshotPreview = true;

        // 创建下载链接
        const link = document.createElement('a');
        link.href = this.screenshotData;
        link.download = `摄像头截图_${this.camera.label}_${new Date().toISOString().replace(/:/g, '-')}.png`;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 显示成功消息
        this.$message ? this.$message.success('截图已保存') : alert('截图已保存');
      } catch (error) {
        console.error('截图失败:', error);
        this.$message ? this.$message.error('截图失败') : alert('截图失败');
      }
    },

    destroyVideoPlayer() {
      // 清除数据模拟定时器
      if (this.dataSimulationTimer) {
        clearInterval(this.dataSimulationTimer);
        this.dataSimulationTimer = null;
      }

      // 移除窗口大小变化事件监听器
      window.removeEventListener('resize', this.handleResize);

      // 销毁视频播放器
      if (this.videoPlayer) {
        try {
          this.videoPlayer.pause();
          this.videoPlayer.dispose();
          this.videoPlayer = null;
        } catch (error) {
          console.error('销毁视频播放器失败:', error);
        }
      }

      // 清除截图数据
      this.screenshotData = null;
      this.showScreenshotPreview = false;
    },

    toggleMute() {
      if (this.videoPlayer) {
        const newMutedState = !this.videoPlayer.muted();
        this.videoPlayer.muted(newMutedState);
        this.isMuted = newMutedState;
      }
    },

    toggleFullscreen() {
      if (this.videoPlayer) {
        if (this.videoPlayer.isFullscreen()) {
          this.videoPlayer.exitFullscreen();
        } else {
          this.videoPlayer.requestFullscreen();
        }
      }
    },

    getCameraTypeText(type) {
      const typeMap = {
        1: '固定摄像头',
        2: '球机',
        3: '枪机',
        4: '半球',
        5: '鱼眼',
        'default': '普通摄像头'
      };

      return typeMap[type] || typeMap['default'];
    },

    getStatusText(status) {
      const statusMap = {
        0: '离线',
        1: '在线',
        2: '故障',
        'normal': '正常'
      };

      return statusMap[status] || '未知';
    },

    getStatusClass(status) {
      const statusClassMap = {
        0: 'status-offline',
        1: 'status-online',
        2: 'status-error',
        'normal': 'status-online'
      };

      return statusClassMap[status] || '';
    },

    // 尝试下一个备用URL
    tryNextBackupUrl() {
      if (this.camera.rtspUrl && this.camera.rtspUrl !== this.camera.url) {
        console.log('尝试使用备用RTSP URL:', this.camera.rtspUrl);
        this.videoPlayer.src([{
          src: this.camera.rtspUrl,
          type: this.getVideoType(this.camera.rtspUrl)
        }]);
        this.videoPlayer.play().catch(e => {
          console.error('RTSP URL播放失败:', e);
          this.tryLastBackupUrl();
        });
      } else {
        this.tryLastBackupUrl();
      }
    },

    // 尝试最后一个备用URL
    tryLastBackupUrl() {
      if (this.camera.stream_url && this.camera.stream_url !== this.camera.url) {
        console.log('尝试使用备用流URL:', this.camera.stream_url);
        this.videoPlayer.src([{
          src: this.camera.stream_url,
          type: this.getVideoType(this.camera.stream_url)
        }]);
        this.videoPlayer.play().catch(e => {
          console.error('备用流URL播放失败:', e);
          this.handleVideoError('所有视频源均无法播放，请检查摄像头状态');
        });
      } else {
        this.handleVideoError('所有视频源均无法播放，请检查摄像头状态');
      }
    },

    // 处理视频错误
    handleVideoError(errorMessage) {
      console.error(errorMessage);

      // 更新摄像头状态为故障
      this.updateCameraStatus(2, errorMessage);

      // 销毁播放器
      if (this.videoPlayer) {
        try {
          this.videoPlayer.dispose();
          this.videoPlayer = null;
        } catch (e) {
          console.error('销毁播放器失败:', e);
        }
      }

      // 显示错误信息
      const videoContainer = this.$refs.videoContainer;
      if (videoContainer) {
        videoContainer.innerHTML = `
          <div class="video-error-container">
            <div class="error-message">
              <i class="fas fa-exclamation-triangle"></i>
              <p>视频播放失败</p>
              <span class="error-details">${errorMessage}</span>
            </div>
            <div class="retry-button" onclick="document.getElementById('retry-camera-${this.camera.id}').click()">
              <i class="fas fa-sync-alt"></i> 重试连接
            </div>
          </div>
        `;

        // 添加一个隐藏的按钮用于重试
        const retryButton = document.createElement('button');
        retryButton.id = `retry-camera-${this.camera.id}`;
        retryButton.style.display = 'none';
        retryButton.addEventListener('click', () => {
          // 重置状态
          this.updateCameraStatus(null, null);
          // 重新尝试初始化
          this.initVideoPlayer();
        });
        document.body.appendChild(retryButton);
      }
    },

    // 更新摄像头状态
    updateCameraStatus(status, message) {
      if (status !== null) {
        // 保存原始状态以便恢复
        if (!this.camera.originalStatus) {
          this.camera.originalStatus = this.camera.status;
        }

        // 更新状态
        this.camera.status = status;
        this.camera.errorStatus = true;
        this.camera.errorMessage = message;

        // 触发状态更新事件
        this.$emit('status-change', {
          id: this.camera.id,
          status: status,
          message: message
        });
      } else {
        // 恢复原始状态
        if (this.camera.originalStatus !== undefined) {
          this.camera.status = this.camera.originalStatus;
          delete this.camera.originalStatus;
        }
        delete this.camera.errorStatus;
        delete this.camera.errorMessage;
      }
    }
  }
}
</script>

<style scoped>
/* 基础样式 */
.camera-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 10, 30, 0.85);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.camera-modal-content {
  width: 90%;
  max-width: 1200px;
  background-color: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(0, 168, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
  box-shadow:
    0 0 20px rgba(0, 168, 255, 0.2),
    0 0 40px rgba(0, 20, 80, 0.4);
  position: relative;
  backdrop-filter: blur(5px);
}

/* 科技感背景元素 */
.tech-bg-element {
  position: absolute;
  pointer-events: none;
  opacity: 0.15;
  z-index: -1;
}

.tech-circle {
  width: 300px;
  height: 300px;
  border: 2px solid rgba(0, 168, 255, 0.5);
  border-radius: 50%;
  top: -100px;
  right: -100px;
}

.tech-grid {
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 168, 255, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 168, 255, 0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.1;
}

.tech-lines {
  width: 200px;
  height: 200px;
  border-left: 1px solid rgba(0, 168, 255, 0.5);
  border-bottom: 1px solid rgba(0, 168, 255, 0.5);
  bottom: 0;
  left: 0;
}

/* 角落装饰 */
.tech-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border-color: #00a8ff;
  border-style: solid;
  opacity: 0.8;
}

.top-left {
  top: 0;
  left: 0;
  border-width: 2px 0 0 2px;
  border-radius: 4px 0 0 0;
}

.top-right {
  top: 0;
  right: 0;
  border-width: 2px 2px 0 0;
  border-radius: 0 4px 0 0;
}

.bottom-left {
  bottom: 0;
  left: 0;
  border-width: 0 0 2px 2px;
  border-radius: 0 0 0 4px;
}

.bottom-right {
  bottom: 0;
  right: 0;
  border-width: 0 2px 2px 0;
  border-radius: 0 0 4px 0;
}

/* 头部样式 */
.camera-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: rgba(0, 30, 60, 0.7);
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  position: relative;
}

.camera-modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
      transparent,
      rgba(0, 168, 255, 0.8) 20%,
      rgba(0, 168, 255, 0.8) 80%,
      transparent);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left i {
  color: #00a8ff;
  font-size: 20px;
}

.header-left h3 {
  margin: 0;
  color: #00a8ff;
  font-size: 18px;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.camera-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-text {
  font-size: 14px;
}

.status-online {
  background-color: #52c41a;
  color: #52c41a;
  box-shadow: 0 0 8px #52c41a;
}

.status-offline {
  background-color: #ff4d4f;
  color: #ff4d4f;
  box-shadow: 0 0 8px #ff4d4f;
}

.status-error {
  background-color: #faad14;
  color: #faad14;
  box-shadow: 0 0 8px #faad14;
}

.close-button {
  background: none;
  border: none;
  color: #00a8ff;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s;
}

.close-button:hover {
  color: #ff4d4f;
  transform: rotate(90deg);
}

/* 内容区域样式 */
.camera-modal-body {
  padding: 10px;
}

.modal-content-wrapper {
  display: flex;
  gap: 10px;
}

/* 视频区域 */
.video-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.video-container {
  width: 100%;
  height: 400px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1; /* 确保正确的层叠顺序 */
}

.video-error-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 85, 85, 0.3);
  box-shadow: 0 0 15px rgba(255, 85, 85, 0.2);
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  text-align: center;
}

.error-message i {
  font-size: 24px;
  color: #ff5555;
  margin-bottom: 10px;
}

.error-message p {
  color: #fff;
  font-size: 16px;
  margin: 0 0 5px 0;
  font-weight: bold;
}

.error-message .error-details {
  color: #ff9999;
  font-size: 12px;
  display: block;
  margin-top: 5px;
  max-width: 80%;
  text-align: center;
}

.retry-button {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: rgba(0, 40, 80, 0.7);
  color: #00a8ff;
  border: 1px solid rgba(0, 168, 255, 0.5);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.retry-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 168, 255, 0.2), transparent);
  transition: all 0.6s;
}

.retry-button:hover {
  background-color: rgba(0, 60, 100, 0.9);
  border-color: #00a8ff;
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.4);
}

.retry-button:hover::before {
  left: 100%;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, rgba(0, 168, 255, 0.1) 0%, transparent 100%),
    linear-gradient(-45deg, rgba(0, 168, 255, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

/* 视频加载指示器 */
.video-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100; /* 提高z-index确保在最上层 */
  pointer-events: none; /* 允许点击穿透 */
}

.loading-spinner {
  font-size: 48px; /* 增大图标尺寸 */
  color: #00a8ff;
  margin-bottom: 15px;
  animation: spin 1.5s infinite linear;
  text-shadow: 0 0 15px rgba(0, 168, 255, 0.8); /* 添加发光效果 */
}

.loading-text {
  color: #fff;
  font-size: 18px; /* 增大字体尺寸 */
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  font-weight: 500; /* 加粗字体 */
  background-color: rgba(0, 20, 40, 0.7); /* 添加背景色 */
  padding: 5px 15px;
  border-radius: 4px;
  border-left: 2px solid #00a8ff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loader-pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

.video-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 10px;
  flex-wrap: wrap;
  width: 100%;
  padding: 5px 0;
  background-color: rgba(0, 20, 40, 0.4);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 信息区域 */
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

/* 状态卡片 */
.status-card {
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 40, 80, 0.5);
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
}

.status-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #00a8ff;
  font-size: 16px;
  font-weight: 500;
}

.status-title i {
  font-size: 18px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.2);
}

.status-indicator .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-body {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-row {
  display: flex;
  gap: 10px;
}

.status-cell {
  flex: 1;
  background-color: rgba(0, 40, 80, 0.3);
  border-radius: 6px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 6px;
  transition: all 0.3s;
  border: 1px solid rgba(0, 168, 255, 0.1);
}

.status-cell:hover {
  background-color: rgba(0, 40, 80, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 168, 255, 0.3);
}

.status-cell-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 40, 80, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00a8ff;
  font-size: 16px;
  margin-bottom: 3px;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.2);
}

.status-cell-value {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  word-break: break-all;
}

.status-cell-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 数据面板 */
.data-panel {
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.data-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(0, 40, 80, 0.5);
  border-bottom: 1px solid rgba(0, 168, 255, 0.2);
  color: #00a8ff;
  font-size: 14px;
}

.data-metrics {
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.metric-item {
  background-color: rgba(0, 40, 80, 0.3);
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
  border: 1px solid rgba(0, 168, 255, 0.1);
}

.metric-item:hover {
  background-color: rgba(0, 40, 80, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 168, 255, 0.3);
}

.metric-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 40, 80, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00a8ff;
  font-size: 14px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.metric-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.metric-chart {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00a8ff;
  font-size: 16px;
}

/* 环形进度条 */
.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-bg {
  fill: transparent;
  stroke: rgba(0, 168, 255, 0.1);
  stroke-width: 3;
}

.progress-ring-circle {
  fill: transparent;
  stroke: #00a8ff;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease;
}

/* 连接面板 */
.connection-panel {
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #52c41a;
  font-size: 14px;
}

.connection-status .connection-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #52c41a;
  display: inline-block;
  animation: pulse 1.5s infinite;
}

.connection-time {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

.recording {
  color: #ff4d4f;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

/* 底部样式 */
.camera-modal-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  position: relative;
  background-color: rgba(0, 30, 60, 0.4);
  border-top: 1px solid rgba(0, 168, 255, 0.2);
}

.camera-modal-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
      transparent,
      rgba(0, 168, 255, 0.5) 20%,
      rgba(0, 168, 255, 0.5) 80%,
      transparent);
}

/* 科技感按钮 */
.tech-button {
  padding: 6px 12px;
  background-color: rgba(0, 40, 80, 0.6);
  color: #00a8ff;
  border: 1px solid rgba(0, 168, 255, 0.5);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  font-size: 12px;
  min-width: 80px;
  justify-content: center;
}

.tech-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: all 0.5s;
}

.tech-button:hover {
  background-color: rgba(0, 60, 100, 0.8);
  border-color: #00a8ff;
  box-shadow: 0 0 10px rgba(0, 168, 255, 0.5);
  transform: translateY(-1px);
}

.tech-button:active {
  transform: translateY(1px);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
}

.tech-button:hover::before {
  left: 100%;
}

.tech-button i {
  font-size: 14px;
}

/* Video.js 自定义样式 */
/* 使用 ::v-deep 替代 /deep/ */
::v-deep .video-js {
  width: 100% !important;
  height: 100% !important;
  background-color: transparent !important;
}

::v-deep .vjs-default-skin {
  color: #00a8ff;
}

::v-deep .vjs-default-skin .vjs-big-play-button {
  background-color: rgba(0, 40, 80, 0.7);
  border-color: #00a8ff;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  line-height: 60px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s;
}

::v-deep .vjs-default-skin .vjs-big-play-button:hover {
  background-color: rgba(0, 60, 100, 0.9);
  box-shadow: 0 0 15px rgba(0, 168, 255, 0.7);
  transform: translate(-50%, -50%) scale(1.1);
}

::v-deep .vjs-default-skin .vjs-control-bar {
  background-color: rgba(0, 40, 80, 0.7);
  backdrop-filter: blur(5px);
  height: 3em;
  border-top: 1px solid rgba(0, 168, 255, 0.2);
}

::v-deep .vjs-default-skin .vjs-slider {
  background-color: rgba(0, 168, 255, 0.3);
  height: 0.4em;
}

::v-deep .vjs-default-skin .vjs-play-progress,
::v-deep .vjs-default-skin .vjs-volume-level {
  background-color: #00a8ff;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.8);
}

::v-deep .vjs-default-skin .vjs-progress-control {
  top: -0.5em;
  position: relative;
}

::v-deep .vjs-default-skin .vjs-progress-holder {
  height: 0.4em;
}

::v-deep .vjs-default-skin .vjs-time-control {
  font-size: 1em;
  line-height: 3em;
}

::v-deep .vjs-default-skin .vjs-current-time,
::v-deep .vjs-default-skin .vjs-duration {
  padding-right: 0.5em;
  padding-left: 0.5em;
}

::v-deep .vjs-default-skin .vjs-control {
  width: 3em;
  height: 3em;
  line-height: 3em;
}

/* 视频信息覆盖层 */
.video-info-overlay {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 5;
  width: 100%;
}

.video-info-item {
  background-color: rgba(0, 20, 40, 0.7);
  padding: 6px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #fff;
  border-left: 2px solid #00a8ff;
  backdrop-filter: blur(2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-bottom: 2px;
  transition: all 0.3s ease;
}

.video-info-item:hover {
  background-color: rgba(0, 30, 60, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.video-info-item i {
  color: #00a8ff;
  font-size: 14px;
  width: 16px;
  text-align: center;
}

/* 连接状态样式 */
.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #52c41a;
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
  display: inline-block;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
    transform: scale(1);
  }

  70% {
    opacity: 0.7;
    box-shadow: 0 0 0 6px rgba(82, 196, 26, 0);
    transform: scale(1.05);
  }

  100% {
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
    transform: scale(1);
  }
}

/* 实时数据监控样式 */
.realtime-monitor {
  margin-top: 20px;
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 168, 255, 0.2);
}

/* 隐藏视频信息面板中的刷新状态图标 */
.status-header .widget-status {
  display: none !important;
}

.monitor-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.monitor-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.monitor-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.monitor-value {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: rgba(0, 40, 80, 0.5);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a8ff, #00d0ff);
  border-radius: 3px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.network-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #fff;
}

.network-indicator i {
  color: #00a8ff;
  font-size: 10px;
}

/* 截图预览样式 */
.screenshot-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 10, 30, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.screenshot-preview img {
  max-width: 90%;
  max-height: 90%;
  border: 2px solid #00a8ff;
  box-shadow: 0 0 30px rgba(0, 168, 255, 0.5);
}

.screenshot-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .modal-content-wrapper {
    flex-direction: column;
  }

  .video-container {
    height: 300px;
  }

  .video-info-overlay {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .data-metrics {
    grid-template-columns: 1fr;
  }

  .camera-modal-content {
    width: 95%;
    max-width: 95%;
  }
}

@media (max-width: 768px) {
  .video-container {
    height: 250px;
  }

  .camera-modal-header {
    padding: 8px 12px;
  }

  .header-left h3 {
    font-size: 16px;
  }

  .video-controls {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .status-row {
    flex-direction: column;
  }

  .connection-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .camera-modal-content {
    width: 98%;
    max-width: 98%;
  }

  .video-container {
    height: 200px;
  }

  .tech-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .camera-modal-header {
    padding: 6px 10px;
  }

  .header-left h3 {
    font-size: 14px;
  }

  .header-left i {
    font-size: 16px;
  }

  .status-cell-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .status-cell-value {
    font-size: 12px;
  }

  .status-cell-label {
    font-size: 10px;
  }
}
</style>
