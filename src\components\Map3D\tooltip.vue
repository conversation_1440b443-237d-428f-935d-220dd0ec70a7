<template>
  <div class="map-tooltip">
    <div class="tooltip-content">{{ text }}</div>
  </div>
</template>

<script>
export default {
  name: 'MapTooltip',
  props: {
    text: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.map-tooltip {
  position: relative;
  
  .tooltip-content {
    white-space: pre-line;
    font-size: 14px;
    line-height: 1.5;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid rgba(25, 31, 45, 0.9);
    }
  }
}
</style> 