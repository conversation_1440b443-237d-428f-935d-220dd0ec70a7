<template>
  <div class="camera-container">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增摄像头
      </el-button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索摄像头名称"
        style="width: 200px; margin-left: 16px"
        clearable
        @clear="handleSearch"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
    </div>

    <!-- 摄像头列表 -->
    <el-table
      v-loading="loading"
      :data="cameraList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="摄像头ID" width="80" fixed="left" />
      <el-table-column prop="camera_name" label="摄像头名称" min-width="150" />
      <el-table-column prop="camera_type" label="类型" width="120">
        <template slot-scope="scope">
          <el-tag :type="getTypeTag(scope.row.camera_type)">
            {{ getTypeText(scope.row.camera_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ezviz_url" label="萤石云URL" min-width="200" show-overflow-tooltip />
      <el-table-column prop="device.device_name" label="所属设备" min-width="150" />
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
              class="operation-button"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              class="operation-button"
            >删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 摄像头表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="cameraForm"
        :model="cameraForm"
        :rules="cameraRules"
        label-width="120px"
      >
        <el-form-item label="摄像头名称" prop="camera_name">
          <el-input v-model="cameraForm.camera_name" placeholder="请输入摄像头名称"></el-input>
        </el-form-item>
        <el-form-item label="摄像头类型" prop="camera_type">
          <el-select v-model="cameraForm.camera_type" placeholder="请选择摄像头类型">
            <el-option label="球机" :value="1"></el-option>
            <el-option label="枪机" :value="2"></el-option>
            <el-option label="半球" :value="3"></el-option>
            <el-option label="其他" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="摄像头状态" prop="status">
          <el-select v-model="cameraForm.status" placeholder="请选择摄像头状态">
            <el-option label="在线" :value="1"></el-option>
            <el-option label="离线" :value="0"></el-option>
            <el-option label="维护中" :value="2"></el-option>
            <el-option label="故障" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="萤石云URL" prop="ezviz_url">
          <el-input v-model="cameraForm.ezviz_url" placeholder="请输入萤石云摄像头URL"></el-input>
        </el-form-item>
        <el-form-item label="所属设备" prop="device_id">
          <el-select 
            v-model="cameraForm.device_id" 
            placeholder="请选择所属设备" 
            clearable
            filterable
            remote
            reserve-keyword
            :remote-method="handleDeviceSearch"
            :loading="deviceLoading"
          >
            <el-option
              v-for="device in deviceList"
              :key="device.id"
              :label="device.device_name"
              :value="device.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCameraList, createCamera, updateCamera, deleteCamera, updateCameraStatus, batchDeleteCameras } from '@/api/camera'
import { getDeviceList } from '@/api/device' // 假设有设备API

export default {
  name: 'CameraManagement',
  data() {
    return {
      loading: false,
      searchQuery: '',
      cameraList: [],
      deviceList: [],
      deviceLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      cameraForm: {
        camera_name: '',
        camera_type: '',
        status: 0,
        ezviz_url: '',
        device_id: ''
      },
      cameraRules: {
        camera_name: [
          { required: true, message: '请输入摄像头名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        camera_type: [
          { required: true, message: '请选择摄像头类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择摄像头状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchCameraList()
    this.fetchDeviceList()
  },
  methods: {
    // 获取摄像头列表
    async fetchCameraList() {
      this.loading = true
      try {
        const res = await getCameraList({
          page: this.currentPage,
          pageSize: this.pageSize,
          query: this.searchQuery
        })
        console.log('摄像头列表API返回数据:', res) // 添加调试信息
        if (res && res.data.list) {
          this.cameraList = Array.isArray(res.data.list) ? res.data.list : []
          this.total = res.data.total || 0
        } else {
          this.cameraList = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取摄像头列表失败:', error)
        this.cameraList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 获取设备列表
    async fetchDeviceList(query = '') {
      this.deviceLoading = true
      try {
        const params = {}
        if (query) {
          params.name = query
        }
        const res = await getDeviceList(params)
        this.deviceList = res.data.list || []
      } catch (error) {
        console.error('获取设备列表失败:', error)
        // 如果API请求失败，使用备用模拟数据
        this.deviceList = [
        ]
      } finally {
        this.deviceLoading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchCameraList()
    },

    // 设备选择器远程搜索
    handleDeviceSearch(query) {
      if (query !== '') {
        this.fetchDeviceList(query)
      } else {
        this.fetchDeviceList()
      }
    },

    // 新增摄像头
    handleAdd() {
      this.dialogTitle = '新增摄像头'
      this.dialogVisible = true
      this.cameraForm = {
        camera_name: '',
        camera_type: 1,
        status: 0,
        ezviz_url: '',
        device_id: ''
      }
      // 加载设备列表
      this.fetchDeviceList()
    },

    // 编辑摄像头
    handleEdit(row) {
      this.dialogTitle = '编辑摄像头'
      this.dialogVisible = true
      this.cameraForm = { ...row }
      // 加载设备列表
      this.fetchDeviceList()
    },

    // 删除摄像头
    handleDelete(row) {
      this.$confirm('确认删除该摄像头吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          await deleteCamera(row.id)
          this.$message.success('删除成功')
          this.fetchCameraList()
        } catch (error) {
          console.error('删除摄像头失败:', error)
        }
      }).catch(() => {})
    },

    // 提交表单
    handleSubmit() {
      this.$refs.cameraForm.validate(async (valid) => {
        if (valid) {
          try {
            if (this.cameraForm.id) {
              // 更新摄像头
              await updateCamera(this.cameraForm)
            } else {
              // 创建新摄像头
              await createCamera(this.cameraForm)
            }
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.fetchCameraList()
          } catch (error) {
            console.error('保存摄像头失败:', error)
          }
        }
      })
    },

    // 摄像头类型
    getTypeTag(type) {
      const typeMap = {
        1: 'success',
        2: 'info',
        3: 'warning',
        4: 'info'
      }
      return typeMap[type] || 'info'
    },

    getTypeText(type) {
      const typeMap = {
        1: '球机',
        2: '枪机',
        3: '半球',
        4: '其他'
      }
      return typeMap[type] || '未知'
    },

    // 摄像头状态
    getStatusType(status) {
      const statusMap = {
        1: 'success',
        0: 'info',
        2: 'warning',
        3: 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        1: '在线',
        0: '离线',
        2: '维护中',
        3: '故障'
      }
      return statusMap[status] || '未知'
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchCameraList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchCameraList()
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.cameraForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.camera-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-start;
  align-items: flex-start;
  padding-left: 8px;
}

.operation-button {
  width: 90px;
  text-align: center;
  padding: 0;
  margin: 0;
  height: 28px;
  line-height: 26px;
}
</style> 