<template>
  <div class="camera-management-page">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <!-- 摄像头统计信息 -->
      <div class="camera-stats-horizontal">
        <div class="stat-card">
          <div class="stat-icon total">
            <i class="fas fa-video"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ cameraStats.total }}</div>
            <div class="stat-label">摄像头总数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon online">
            <i class="fas fa-play-circle"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ cameraStats.online }}</div>
            <div class="stat-label">在线监控</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon offline">
            <i class="fas fa-stop-circle"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ cameraStats.offline }}</div>
            <div class="stat-label">离线设备</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon devices">
            <i class="fas fa-server"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ cameraStats.devices }}</div>
            <div class="stat-label">监控设备</div>
          </div>
        </div>
      </div>

      <!-- 设备筛选器 -->
      <div class="device-filter">
        <div class="filter-label">
          <i class="fas fa-filter"></i>
          设备筛选:
        </div>
        <div class="device-tabs">
          <div
            class="device-tab"
            :class="{ 'active': selectedDevice === null }"
            @click="selectAllDevices"
          >
            <i class="fas fa-th-large"></i>
            全部摄像头
          </div>
          <div
            v-for="device in deviceList"
            :key="device.id"
            class="device-tab"
            :class="{ 'active': selectedDevice && selectedDevice.id === device.id }"
            @click="selectDevice(device)"
          >
            <i class="fas fa-microchip"></i>
            {{ device.device_name || device.name || '未命名设备' }}
            <span class="camera-count">({{ device.cameras ? device.cameras.length : 0 }})</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div v-if="loading" class="loading-container">
        <i class="fas fa-spinner fa-spin"></i>
        <span>加载摄像头数据中...</span>
      </div>

      <div v-else-if="displayCameras.length === 0" class="no-data-container">
        <i class="fas fa-video-slash"></i>
        <h3>暂无摄像头数据</h3>
        <p>{{ selectedDevice ? '该设备暂无摄像头' : '项目中暂无摄像头设备' }}</p>
      </div>

      <!-- 摄像头监控网格 -->
      <div v-else class="camera-monitor-grid">
        <div
          v-for="camera in displayCameras"
          :key="camera.id"
          class="camera-monitor-card"
          :class="{ 'online': camera.status === 1, 'offline': camera.status !== 1 }"
        >
          <!-- 摄像头头部信息 -->
          <div class="camera-monitor-header">
            <div class="camera-title">
              <div class="camera-name">
                <i class="fas fa-video"></i>
                {{ camera.camera_name || camera.name || '未命名摄像头' }}
              </div>
              <div class="device-info">
                <i class="fas fa-microchip"></i>
                {{ camera.deviceName || '未知设备' }}
              </div>
            </div>
            <div class="camera-status-indicator" :class="getCameraStatusClass(camera.status)">
              <div class="status-dot"></div>
              <span>{{ getCameraStatusText(camera.status) }}</span>
            </div>
          </div>

          <!-- 视频监控区域 -->
          <div class="video-monitor-area">
            <div v-if="camera.ezviz_url && camera.status === 1" class="video-player">
              <div
                :ref="`videoContainer_${camera.id}`"
                class="video-container"
                @click="playVideo(camera)"
              >
                <!-- Video.js 播放器将在这里动态创建 -->
              </div>
              <div class="video-overlay">
                <button class="fullscreen-btn" @click="enterFullscreen(camera)" title="全屏观看">
                  <i class="fas fa-expand"></i>
                </button>
                <div class="video-status" v-if="getVideoStatus(camera.id)">
                  <span class="status-text">{{ getVideoStatus(camera.id) }}</span>
                </div>
              </div>
            </div>
            <div v-else class="video-offline">
              <div class="offline-icon">
                <i class="fas fa-video-slash"></i>
              </div>
              <div class="offline-text">
                <p>{{ camera.status === 1 ? '暂无视频流' : '摄像头离线' }}</p>
                <small>{{ formatTime(camera.updated_at) }}</small>
              </div>
            </div>
          </div>

          <!-- 摄像头信息和控制 -->
          <div class="camera-monitor-footer">
            <div class="camera-details">
              <div class="detail-item type-item">
                <span class="label">类型:</span>
                <span class="value type-value">{{ getCameraTypeText(camera.camera_type) }}</span>
              </div>
            </div>
            <div class="camera-controls">
              <!-- <button
                v-if="camera.ezviz_url && camera.status === 1"
                :class="['control-btn', 'play-btn', { 'disabled-btn': !camera.ezviz_url || camera.status !== 1 }]"
                @click="camera.ezviz_url && camera.status === 1 ? playVideo(camera) : null"
                :title="camera.ezviz_url && camera.status === 1 ? '播放/暂停视频' : '摄像头离线或无视频源'"
              >
                <i class="fas fa-play"></i>
              </button> -->
              <!-- <button
                :class="['control-btn', 'info-btn', { 'disabled-btn': camera.status !== 1 }]"
                @click="camera.status === 1 ? showCameraDetail(camera) : null"
                :title="camera.status === 1 ? '详细信息' : '摄像头离线'"
              >
                <i class="fas fa-info-circle"></i>
              </button> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 摄像头详情弹窗 -->
    <CameraDetailModal
      :visible="showModal"
      :camera="selectedCamera"
      @close="closeModal"
    />
  </div>
</template>

<script>
import { getDeviceList, getProjectDevices, getDeviceCameras } from '@/api/device'
import CameraDetailModal from './CameraDetailModal.vue'

export default {
  name: 'CameraManagement',
  components: {
    CameraDetailModal
  },
  props: {
    projectId: {
      type: [String, Number],
      required: true
    },
    selectedProject: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      deviceList: [],
      selectedDevice: null,
      allCameras: [], // 所有摄像头数据
      cameraStats: {
        total: 0,
        online: 0,
        offline: 0,
        devices: 0
      },
      videoStatus: {}, // 视频状态管理
      videoPlayers: {}, // Video.js播放器实例管理
      // 弹窗相关
      showModal: false,
      selectedCamera: null
    }
  },
  computed: {
    projectName() {
      return this.selectedProject?.name || '未知项目'
    },
    // 当前显示的摄像头列表
    displayCameras() {
      if (this.selectedDevice) {
        // 显示选中设备的摄像头
        return this.allCameras.filter(camera => camera.device_id === this.selectedDevice.id)
      } else {
        // 显示所有摄像头
        return this.allCameras
      }
    }
  },
  mounted() {
    this.fetchDeviceData()
  },
  updated() {
    // 当数据更新后，自动初始化有URL的在线摄像头播放器
    this.$nextTick(() => {
      this.autoInitializeVideos()
    })
  },
  beforeDestroy() {
    // 清理所有Video.js播放器实例
    Object.keys(this.videoPlayers).forEach(cameraId => {
      const player = this.videoPlayers[cameraId]
      if (player && typeof player.dispose === 'function') {
        try {
          player.dispose()
        } catch (error) {
          console.warn(`销毁摄像头 ${cameraId} 播放器失败:`, error)
        }
      }
    })
    this.videoPlayers = {}
  },
  methods: {
    // 获取设备数据
    async fetchDeviceData() {
      this.loading = true
      try {
        console.log('获取项目设备数据，项目ID:', this.projectId)

        // 优先使用项目专用API获取设备列表
        let response
        try {
          response = await getProjectDevices(this.projectId)
        } catch (error) {
          console.warn('项目专用设备API失败，使用通用API:', error)
          response = await getDeviceList({ project_id: this.projectId })
        }

        if (response && response.code === 0 && response.data) {
          this.deviceList = response.data.list || response.data || []
          this.allCameras = [] // 重置所有摄像头数据

          // 为每个设备获取摄像头信息
          for (let device of this.deviceList) {
            try {
              const cameraResponse = await getDeviceCameras(device.id)
              if (cameraResponse && cameraResponse.code === 0) {
                device.cameras = cameraResponse.data || []

                // 将摄像头添加到全局列表，并添加设备信息
                device.cameras.forEach(camera => {
                  camera.deviceName = device.device_name || device.name || '未命名设备'
                  camera.deviceId = device.id
                  this.allCameras.push(camera)
                })
              } else {
                device.cameras = []
              }
            } catch (error) {
              console.error(`获取设备 ${device.id} 的摄像头信息失败:`, error)
              device.cameras = []
            }
          }

          this.calculateCameraStats()

          // 数据加载完成后，自动初始化视频播放器
          this.$nextTick(() => {
            this.autoInitializeVideos()
          })
        } else {
          console.warn('获取设备列表失败:', response)
          this.deviceList = []
          this.allCameras = []
        }
      } catch (error) {
        console.error('获取设备数据失败:', error)
        this.deviceList = []
        this.allCameras = []
      } finally {
        this.loading = false
      }
    },

    // 计算摄像头统计信息
    calculateCameraStats() {
      const totalCameras = this.allCameras.length
      const onlineCameras = this.allCameras.filter(camera => camera.status === 1).length
      const offlineCameras = totalCameras - onlineCameras
      const devicesWithCameras = this.deviceList.filter(device =>
        device.cameras && device.cameras.length > 0
      ).length

      this.cameraStats = {
        total: totalCameras,
        online: onlineCameras,
        offline: offlineCameras,
        devices: devicesWithCameras
      }
    },

    // 选择设备
    selectDevice(device) {
      this.selectedDevice = device
    },

    // 选择所有设备（显示所有摄像头）
    selectAllDevices() {
      this.selectedDevice = null
    },

    // 获取设备状态类名
    getDeviceStatusClass(status) {
      return {
        'online': status === 1,
        'offline': status === 0 || status === null
      }
    },

    // 获取设备状态图标
    getDeviceStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取设备状态文本
    getDeviceStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取摄像头状态类名
    getCameraStatusClass(status) {
      return {
        'online': status === 1,
        'offline': status === 0 || status === null
      }
    },

    // 获取摄像头状态图标
    getCameraStatusIcon(status) {
      return status === 1 ? 'fas fa-circle' : 'fas fa-circle'
    },

    // 获取摄像头状态文本
    getCameraStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },

    // 获取摄像头类型文本
    getCameraTypeText(type) {
      const typeMap = {
        1: '球机',
        2: '枪机',
        3: '半球',
        4: '其他'
      }
      return typeMap[type] || '未知'
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '-'
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return timeStr
      }
    },

    // 获取视频封面
    getVideoPoster() {
      // 可以返回一个默认的视频封面图片
      return ''
    },

    // 获取视频状态
    getVideoStatus(cameraId) {
      return this.videoStatus[cameraId] || ''
    },

    // 获取视频类型
    getVideoType(url) {
      if (!url) return 'video/mp4'

      const urlLower = url.toLowerCase()
      if (urlLower.includes('.m3u8') || urlLower.includes('hls')) {
        return 'application/x-mpegURL'
      } else if (urlLower.includes('.mp4')) {
        return 'video/mp4'
      } else if (urlLower.includes('.webm')) {
        return 'video/webm'
      } else if (urlLower.includes('.ogg')) {
        return 'video/ogg'
      } else if (urlLower.includes('rtmp')) {
        return 'rtmp/mp4'
      } else {
        // 默认尝试HLS格式
        return 'application/x-mpegURL'
      }
    },

    // 初始化Video.js播放器
    initVideoPlayer(camera) {
      // 检查是否已经初始化过
      if (this.videoPlayers[camera.id]) {
        // 如果已经初始化，尝试播放/暂停
        const player = this.videoPlayers[camera.id]
        if (player.paused()) {
          player.play().catch(error => {
            console.error('视频播放失败:', error)
            this.$set(this.videoStatus, camera.id, '播放失败')
          })
        } else {
          player.pause()
        }
        return
      }

      // 确保Video.js已加载
      if (typeof window.videojs === 'undefined') {
        console.error('Video.js not loaded!')
        this.$set(this.videoStatus, camera.id, '播放器未加载')
        return
      }

      const videoContainer = this.$refs[`videoContainer_${camera.id}`]
      if (!videoContainer) {
        console.error('找不到视频容器')
        return
      }

      // 确保videoContainer是DOM元素
      const containerElement = Array.isArray(videoContainer) ? videoContainer[0] : videoContainer
      if (!containerElement || typeof containerElement.appendChild !== 'function') {
        console.error('视频容器不是有效的DOM元素:', containerElement)
        return
      }

      // 清空容器
      containerElement.innerHTML = ''

      // 创建视频元素
      const videoElement = document.createElement('video')
      videoElement.className = 'video-js vjs-default-skin'
      videoElement.id = `camera-video-player-${camera.id}`
      videoElement.controls = true
      videoElement.preload = 'auto'
      videoElement.width = containerElement.clientWidth || 400
      videoElement.height = containerElement.clientHeight || 200 // 使用容器高度
      videoElement.poster = this.getVideoPoster()

      // 添加到容器
      containerElement.appendChild(videoElement)

      // 确定视频类型
      const videoType = this.getVideoType(camera.ezviz_url)
      console.log('初始化摄像头播放器，URL:', camera.ezviz_url, '类型:', videoType)

      // 创建播放器实例
      const videojs = window.videojs
      const player = videojs(`camera-video-player-${camera.id}`, {
        sources: [{
          src: camera.ezviz_url,
          type: videoType
        }],
        autoplay: true,
        muted: true, // 默认静音，避免自动播放策略限制
        controls: true,
        fluid: false, // 不使用流式布局，使用固定高度
        preload: 'auto',
        responsive: false, // 不使用响应式，使用固定尺寸
        width: containerElement.clientWidth || 400,
        height: containerElement.clientHeight || 200,
        html5: {
          vhs: {
            overrideNative: true
          },
          nativeVideoTracks: false,
          nativeAudioTracks: false,
          nativeTextTracks: false
        },
        controlBar: {
          children: [
            'playToggle',
            'volumePanel',
            'currentTimeDisplay',
            'timeDivider',
            'durationDisplay',
            'progressControl',
            'fullscreenToggle'
          ]
        }
      })

      // 存储播放器实例
      this.$set(this.videoPlayers, camera.id, player)

      // 设置加载状态
      this.$set(this.videoStatus, camera.id, '正在加载...')

      // 监听播放器事件
      player.ready(() => {
        console.log(`摄像头 ${camera.camera_name} 播放器准备就绪`)
        this.$set(this.videoStatus, camera.id, '')

        // 播放器准备就绪后尝试播放
        setTimeout(() => {
          player.play().catch(error => {
            console.warn(`摄像头 ${camera.camera_name} 自动播放失败:`, error)
            this.$set(this.videoStatus, camera.id, '点击播放')
          })
        }, 100)
      })

      player.on('loadstart', () => {
        this.$set(this.videoStatus, camera.id, '正在加载...')
      })

      player.on('canplay', () => {
        this.$set(this.videoStatus, camera.id, '')
        // 当视频可以播放时，尝试播放
        player.play().catch(error => {
          console.warn(`摄像头 ${camera.camera_name} 播放失败:`, error)
          this.$set(this.videoStatus, camera.id, '点击播放')
        })
      })

      player.on('play', () => {
        console.log(`摄像头 ${camera.camera_name} 开始播放`)
        this.$set(this.videoStatus, camera.id, '正在播放')
      })

      player.on('pause', () => {
        console.log(`摄像头 ${camera.camera_name} 暂停播放`)
        this.$set(this.videoStatus, camera.id, '已暂停')
      })

      player.on('error', (error) => {
        console.error(`摄像头 ${camera.camera_name} 播放错误:`, error)
        this.$set(this.videoStatus, camera.id, '播放失败')
      })

      player.on('ended', () => {
        console.log(`摄像头 ${camera.camera_name} 播放结束`)
        this.$set(this.videoStatus, camera.id, '播放结束')
      })
    },

    // 播放视频
    playVideo(camera) {
      const player = this.videoPlayers[camera.id]
      if (player) {
        if (player.paused()) {
          player.play().catch(error => {
            console.error('视频播放失败:', error)
            this.$set(this.videoStatus, camera.id, '播放失败')
            this.$message && this.$message.error('视频播放失败，请检查视频源')
          })
        } else {
          player.pause()
        }
      } else {
        // 如果播放器不存在，先初始化
        this.initVideoPlayer(camera)
      }
    },

    // 进入全屏观看
    enterFullscreen(camera) {
      const player = this.videoPlayers[camera.id]
      if (player) {
        // 使用Video.js的全屏功能
        if (player.requestFullscreen) {
          player.requestFullscreen()
        } else {
          // 如果Video.js全屏不可用，尝试原生全屏
          const videoElement = player.el().querySelector('video')
          if (videoElement) {
            if (videoElement.requestFullscreen) {
              videoElement.requestFullscreen()
            } else if (videoElement.webkitRequestFullscreen) {
              videoElement.webkitRequestFullscreen()
            } else if (videoElement.mozRequestFullScreen) {
              videoElement.mozRequestFullScreen()
            } else if (videoElement.msRequestFullscreen) {
              videoElement.msRequestFullscreen()
            }
          }
        }
      } else {
        // 如果播放器不存在，先初始化再全屏
        this.initVideoPlayer(camera)
        this.$nextTick(() => {
          this.enterFullscreen(camera)
        })
      }
    },

    // 显示摄像头详情
    showCameraDetail(camera) {
      console.log('显示摄像头详情:', camera)

      // 转换摄像头数据格式以适配CameraDetailModal组件
      const modalCamera = {
        ...camera,
        id: camera.id,
        label: camera.camera_name || camera.name || '未命名摄像头', // CameraDetailModal期望的是label属性
        url: camera.ezviz_url || camera.url, // 使用ezviz_url作为主要URL
        ezviz_url: camera.ezviz_url, // 保留原始URL
        name: camera.camera_name || camera.name || '未命名摄像头',
        device: camera.deviceName || '未知设备', // CameraDetailModal期望的是device属性
        deviceName: camera.deviceName || '未知设备',
        status: camera.status,
        location: camera.location || '未设置',
        type: camera.camera_type, // 保留原始类型数值
        camera_type: camera.camera_type // 保留原始字段
      }

      this.selectedCamera = modalCamera
      this.showModal = true
    },

    // 关闭摄像头详情弹窗
    closeModal() {
      this.showModal = false
      this.selectedCamera = null
    },

    // 自动初始化有URL的在线摄像头视频播放器
    autoInitializeVideos() {
      // 延迟一点时间确保DOM已经渲染完成
      setTimeout(() => {
        console.log('开始自动初始化视频播放器，摄像头数量:', this.displayCameras.length)
        this.displayCameras.forEach(camera => {
          // 只为有URL且在线的摄像头自动初始化播放器
          if (camera.ezviz_url && camera.status === 1 && !this.videoPlayers[camera.id]) {
            console.log(`自动初始化摄像头 ${camera.camera_name} 的播放器，URL:`, camera.ezviz_url)

            // 检查DOM容器是否存在
            const containerRef = this.$refs[`videoContainer_${camera.id}`]
            if (containerRef) {
              console.log(`找到摄像头 ${camera.id} 的容器，开始初始化`)
              this.initVideoPlayer(camera)
            } else {
              console.warn(`未找到摄像头 ${camera.id} 的容器，跳过初始化`)
            }
          } else {
            if (!camera.ezviz_url) {
              console.log(`摄像头 ${camera.camera_name} 没有URL，跳过`)
            } else if (camera.status !== 1) {
              console.log(`摄像头 ${camera.camera_name} 离线，跳过`)
            } else if (this.videoPlayers[camera.id]) {
              console.log(`摄像头 ${camera.camera_name} 已初始化，跳过`)
            }
          }
        })
      }, 1000) // 增加延迟时间确保DOM渲染完成
    }
  }
}
</script>

<style scoped>
/* 摄像头监控中心样式 */
.camera-management-page {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
}

/* 顶部控制栏 */
.control-bar {
  background: rgba(0, 20, 40, 0.9);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

/* 水平统计信息 */
.camera-stats-horizontal {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  justify-content: space-between;
}

.stat-card {
  flex: 1;
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.8), rgba(0, 50, 100, 0.6));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #7fdbff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(127, 219, 255, 0.15);
  border-color: rgba(127, 219, 255, 0.6);
}

.stat-card:hover::before {
  opacity: 1;
}

/* 统计卡片图标样式 */
.stat-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: #ffffff;
  background: linear-gradient(135deg, #7fdbff, #0074d9);
  box-shadow: 0 4px 15px rgba(127, 219, 255, 0.3);
}

.stat-icon.online {
  background: linear-gradient(135deg, #2ecc40, #27ae60);
}

.stat-icon.offline {
  background: linear-gradient(135deg, #ff4136, #e74c3c);
}

.stat-icon.devices {
  background: linear-gradient(135deg, #ff851b, #f39c12);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #7fdbff;
  text-shadow: 0 0 10px rgba(127, 219, 255, 0.5);
  margin-bottom: 4px;
  font-family: 'Orbitron', monospace;
}

.stat-card:nth-child(2) .stat-number {
  color: #2ecc40;
  text-shadow: 0 0 10px rgba(46, 204, 64, 0.5);
}

.stat-card:nth-child(3) .stat-number {
  color: #ff4136;
  text-shadow: 0 0 10px rgba(255, 65, 54, 0.5);
}

.stat-card:nth-child(4) .stat-number {
  color: #ff851b;
  text-shadow: 0 0 10px rgba(255, 133, 27, 0.5);
}

.stat-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 设备筛选器 */
.device-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7fdbff;
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
}

.device-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  flex: 1;
}

.device-tab {
  background: rgba(0, 30, 60, 0.6);
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 20px;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.device-tab:hover {
  background: rgba(0, 50, 100, 0.8);
  border-color: rgba(127, 219, 255, 0.5);
  transform: translateY(-1px);
}

.device-tab.active {
  background: linear-gradient(135deg, rgba(127, 219, 255, 0.2), rgba(0, 74, 217, 0.3));
  border-color: #7fdbff;
  color: #ffffff;
  box-shadow: 0 0 15px rgba(127, 219, 255, 0.3);
}

.camera-count {
  color: #7fdbff;
  font-weight: 600;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
}

/* 摄像头监控网格 */
.camera-monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  scroll-behavior: smooth;
  /* 确保滚动条始终可见 */
  scrollbar-width: thin;
  scrollbar-color: rgba(127, 219, 255, 0.4) rgba(0, 0, 0, 0.1);
  /* 移动设备滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* 确保最小高度为0，允许收缩 */
  min-height: 0;
}

/* 摄像头监控卡片 */
.camera-monitor-card {
  background: linear-gradient(135deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 100, 0.7));
  border: 1px solid rgba(127, 219, 255, 0.3);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
  height: auto;
  max-height: 320px; /* 限制最大高度 */
  display: flex;
  flex-direction: column;
}

.camera-monitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #7fdbff, transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.camera-monitor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(127, 219, 255, 0.2);
  border-color: rgba(127, 219, 255, 0.6);
}

.camera-monitor-card:hover::before {
  opacity: 1;
}

.camera-monitor-card.online {
  border-left: 4px solid #2ecc40;
}

.camera-monitor-card.offline {
  border-left: 4px solid #ff4136;
}

/* 摄像头监控头部 */
.camera-monitor-header {
  padding: 10px 15px;
  background: rgba(0, 40, 80, 0.6);
  border-bottom: 1px solid rgba(127, 219, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* 防止压缩 */
}

.camera-title {
  flex: 1;
}

.camera-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.device-info {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  gap: 6px;
}

.camera-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid;
}

.camera-status-indicator.online {
  color: #2ecc40;
  background: rgba(46, 204, 64, 0.1);
  border-color: rgba(46, 204, 64, 0.3);
}

.camera-status-indicator.offline {
  color: #ff4136;
  background: rgba(255, 65, 54, 0.1);
  border-color: rgba(255, 65, 54, 0.3);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

/* 视频监控区域 */
.video-monitor-area {
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  height: 200px;
  max-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex: 1; /* 占据主要空间 */
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  border-radius: 4px;
}

.monitor-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.video-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.video-player:hover .video-overlay {
  opacity: 1;
}

.fullscreen-btn {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(127, 219, 255, 0.5);
  border-radius: 4px;
  color: #7fdbff;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fullscreen-btn:hover {
  background: rgba(127, 219, 255, 0.2);
  border-color: #7fdbff;
}

.video-status {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #7fdbff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  border: 1px solid rgba(127, 219, 255, 0.3);
}

.status-text {
  color: #7fdbff;
}

.video-offline {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.offline-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #ff4136;
}

.offline-text p {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.offline-text small {
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.8rem;
}

/* 摄像头监控底部 */
.camera-monitor-footer {
  padding: 10px 15px;
  background: rgba(0, 20, 40, 0.6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  flex-shrink: 0; /* 防止压缩 */
}

.camera-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
}

.detail-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.detail-item .value {
  color: #ffffff;
}

/* 类型项左对齐 */
.detail-item.type-item {
  justify-content: flex-start;
}

.detail-item.type-item .type-value {
  margin-left: 8px;
}

.camera-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 8px 12px;
  border: 1px solid;
  border-radius: 6px;
  background: transparent;
  color: #ffffff;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.control-btn:hover {
  transform: translateY(-1px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.control-btn:disabled:hover {
  transform: none;
  background: transparent;
}

.play-btn {
  border-color: rgba(46, 204, 64, 0.5);
  color: #2ecc40;
}

.play-btn:hover {
  background: rgba(46, 204, 64, 0.1);
  border-color: #2ecc40;
}



.info-btn {
  border-color: rgba(127, 219, 255, 0.5);
  color: #7fdbff;
}

.info-btn:hover {
  background: rgba(127, 219, 255, 0.1);
  border-color: #7fdbff;
}

/* 禁用按钮样式 */
.control-btn.disabled-btn {
  background: rgba(100, 100, 100, 0.2) !important;
  border-color: rgba(100, 100, 100, 0.3) !important;
  color: #666666 !important;
  cursor: not-allowed !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
}

.control-btn.disabled-btn:hover {
  background: rgba(100, 100, 100, 0.2) !important;
  border-color: rgba(100, 100, 100, 0.3) !important;
  color: #666666 !important;
  transform: none !important;
}

/* 加载和无数据状态 */
.loading-container,
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  flex: 1;
}

.loading-container i,
.no-data-container i {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #7fdbff;
}

.loading-container i.fa-spin {
  animation: spin 1s linear infinite;
}

.no-data-container h3 {
  margin: 0 0 10px 0;
  color: #ffffff;
  font-size: 1.5rem;
}

.no-data-container p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.camera-monitor-grid::-webkit-scrollbar {
  width: 10px;
}

.camera-monitor-grid::-webkit-scrollbar-track {
  background: rgba(0, 20, 40, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(127, 219, 255, 0.1);
}

.camera-monitor-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(127, 219, 255, 0.4), rgba(127, 219, 255, 0.6));
  border-radius: 6px;
  border: 1px solid rgba(127, 219, 255, 0.2);
  transition: all 0.3s ease;
}

.camera-monitor-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(127, 219, 255, 0.6), rgba(127, 219, 255, 0.8));
  border-color: rgba(127, 219, 255, 0.4);
  box-shadow: 0 0 8px rgba(127, 219, 255, 0.3);
}

.camera-monitor-grid::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(127, 219, 255, 0.7), rgba(127, 219, 255, 0.9));
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .camera-management-page {
    padding: 15px;
  }

  .control-bar {
    padding: 15px;
  }

  .camera-stats-horizontal {
    gap: 15px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-icon {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .camera-monitor-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 15px;
    padding: 15px;
    /* 在小屏幕上确保滚动正常工作 */
    max-height: calc(100vh - 200px);
  }

  .device-tabs {
    gap: 8px;
  }

  .device-tab {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 900px) {
  .camera-management-page {
    padding: 10px;
  }

  .control-bar {
    padding: 12px;
  }

  .camera-stats-horizontal {
    gap: 10px;
    flex-wrap: wrap;
  }

  .stat-card {
    padding: 10px;
    min-width: calc(50% - 5px);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .device-filter {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .device-tabs {
    width: 100%;
  }

  .device-tab {
    padding: 5px 10px;
    font-size: 0.75rem;
  }

  .camera-monitor-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    padding: 12px;
    /* 在中等屏幕上确保滚动正常工作 */
    max-height: calc(100vh - 180px);
  }

  .camera-monitor-header {
    padding: 12px 15px;
  }

  .camera-name {
    font-size: 1rem;
  }

  .device-info {
    font-size: 0.75rem;
  }

  .video-monitor-area {
    height: 180px;
    max-height: 180px;
  }

  .video-player {
    height: 180px;
  }

  .video-offline {
    height: 180px;
  }

  .camera-monitor-footer {
    padding: 12px 15px;
  }

  .control-btn {
    padding: 6px 10px;
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .camera-management-page {
    padding: 8px;
  }

  .control-bar {
    padding: 10px;
    margin-bottom: 15px;
  }

  .camera-stats-horizontal {
    gap: 8px;
    margin-bottom: 15px;
  }

  .stat-card {
    padding: 8px;
    min-width: calc(50% - 4px);
  }

  .stat-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .stat-number {
    font-size: 1.3rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .device-filter {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-label {
    font-size: 0.8rem;
  }

  .device-tabs {
    width: 100%;
    gap: 6px;
  }

  .device-tab {
    padding: 4px 8px;
    font-size: 0.7rem;
  }

  .camera-monitor-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
    /* 在小屏幕上确保滚动正常工作 */
    max-height: calc(100vh - 160px);
  }

  .camera-monitor-header {
    padding: 10px 12px;
  }

  .camera-name {
    font-size: 0.9rem;
  }

  .device-info {
    font-size: 0.7rem;
  }

  .camera-status-indicator {
    font-size: 0.75rem;
    padding: 3px 6px;
  }

  .video-monitor-area {
    height: 160px;
    max-height: 160px;
  }

  .video-player {
    height: 160px;
  }

  .video-offline {
    height: 160px;
  }

  .offline-icon {
    font-size: 2.5rem;
  }

  .camera-monitor-footer {
    padding: 10px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .camera-details {
    width: 100%;
  }

  .detail-item {
    font-size: 0.75rem;
  }

  .camera-controls {
    width: 100%;
    justify-content: flex-end;
  }

  .control-btn {
    padding: 5px 8px;
    font-size: 0.7rem;
  }

  .loading-container,
  .no-data-container {
    padding: 40px 15px;
  }

  .loading-container i,
  .no-data-container i {
    font-size: 2.5rem;
  }

  .no-data-container h3 {
    font-size: 1.3rem;
  }

  .no-data-container p {
    font-size: 0.9rem;
  }
}

/* Video.js 自定义样式 */
::v-deep .video-js {
  width: 100% !important;
  height: 100% !important;
  background-color: transparent !important;
  font-family: inherit !important;
}

::v-deep .vjs-default-skin {
  color: #00a8ff;
}

::v-deep .vjs-default-skin .vjs-big-play-button {
  background-color: rgba(0, 40, 80, 0.7);
  border-color: #00a8ff;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  line-height: 60px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s;
}

::v-deep .vjs-default-skin .vjs-big-play-button:hover {
  background-color: rgba(0, 168, 255, 0.2);
  border-color: #7fdbff;
}

::v-deep .vjs-default-skin .vjs-control-bar {
  background-color: rgba(0, 40, 80, 0.8);
  backdrop-filter: blur(5px);
}

::v-deep .vjs-default-skin .vjs-slider {
  background-color: rgba(0, 168, 255, 0.3);
}

::v-deep .vjs-default-skin .vjs-play-progress,
::v-deep .vjs-default-skin .vjs-volume-level {
  background-color: #00a8ff;
}

::v-deep .vjs-default-skin .vjs-control:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 168, 255, 0.5);
}

::v-deep .vjs-default-skin .vjs-button > .vjs-icon-placeholder:before {
  color: #ffffff;
}

::v-deep .vjs-default-skin .vjs-button:hover > .vjs-icon-placeholder:before {
  color: #7fdbff;
}

::v-deep .vjs-loading-spinner {
  border-color: #7fdbff transparent transparent transparent;
}

::v-deep .vjs-poster {
  background-size: cover;
  background-position: center;
}
</style>
