import request from '@/utils/axios'

/**
 * 获取项目天气信息
 * 根据项目ID获取项目所在地区的天气信息
 *
 * @param {string|number} projectId - 项目ID
 * @returns {Promise} - 返回包含天气信息的Promise对象
 */
export function getProjectWeather(projectId) {
  console.log('Calling weather API for project:', projectId);

  return request({
    path: `/api/system/project-weather/latest-by-number/${projectId}`,
    method: 'get'
  }).then(response => {
    console.log('Weather API response received:', response);

    // 打印原始响应
    console.log('Original weather API response data:', JSON.stringify(response.data, null, 2));

    return response;
  }).catch(error => {
    console.error('Weather API error:', error);
    // 异常情况直接抛出错误，不返回测试数据
    throw error;
  });
}

