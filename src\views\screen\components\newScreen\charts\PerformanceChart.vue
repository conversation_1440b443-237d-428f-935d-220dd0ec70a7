<template>
  <div class="chart-container">
    <canvas ref="chart"></canvas>
  </div>
</template>

<script>
import Chart from 'chart.js'

export default {
  name: 'PerformanceChart',
  data() {
    return {
      chart: null,
      chartData: {
        labels: ['起重效率', '行走速度', '定位精度', '能耗指标', '安全系数'],
        datasets: [{
          label: '实际性能',
          data: [95, 90, 98, 85, 99],
          backgroundColor: 'rgba(0, 168, 255, 0.7)',
          borderColor: 'rgba(0, 168, 255, 1)',
          borderWidth: 1
        }, {
          label: '设计标准',
          data: [90, 85, 95, 80, 95],
          backgroundColor: 'rgba(0, 255, 157, 0.7)',
          borderColor: 'rgba(0, 255, 157, 1)',
          borderWidth: 1
        }]
      },
      chartOptions: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              fontColor: 'rgba(255, 255, 255, 0.7)',
              callback: function(value) {
                return value + '%';
              }
            },
            gridLines: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }],
          xAxes: [{
            ticks: {
              fontColor: 'rgba(255, 255, 255, 0.7)'
            },
            gridLines: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }]
        },
        legend: {
          labels: {
            fontColor: 'rgba(255, 255, 255, 0.7)'
          }
        }
      }
    }
  },
  mounted() {
    this.createChart();
    this.startDataSimulation();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
    }
  },
  methods: {
    createChart() {
      const ctx = this.$refs.chart.getContext('2d');
      this.chart = new Chart(ctx, {
        type: 'bar',
        data: this.chartData,
        options: this.chartOptions
      });
    },
    startDataSimulation() {
      // 每30秒更新一次数据
      this.simulationInterval = setInterval(() => {
        // 更新实际性能数据，添加小幅度随机变化
        this.chartData.datasets[0].data = this.chartData.datasets[0].data.map(value => {
          // 在原值基础上增加或减少最多3个百分点
          const change = Math.random() * 6 - 3;
          // 确保值在80-100之间
          return Math.min(100, Math.max(80, value + change));
        });
        
        // 更新图表
        this.chart.update();
      }, 30000);
    }
  }
}
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
