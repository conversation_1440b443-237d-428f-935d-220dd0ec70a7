/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  background-color: #001a33; /* 纯深蓝色背景 */
  color: #fff;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  box-sizing: border-box;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

html::-webkit-scrollbar, body::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

body {
  position: relative;
}

/* 小屏幕模式下允许滚动 */
@media screen and (max-width: 1366px) {
  html, body {
    overflow-x: hidden;
    overflow-y: auto;
  }
}

/* 大屏幕模式下禁止滚动 */
@media screen and (min-width: 1367px) {
  html, body {
    overflow: hidden;
  }
}

/* 全屏模式样式 */
.fullscreen-mode {
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #001528 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 在全屏模式下隐藏设置面板 */
.fullscreen-mode .rightPanel-container,
.fullscreen-active .rightPanel-container,
html.fullscreen-mode .rightPanel-container,
html.fullscreen-active .rightPanel-container,
body.fullscreen-mode .rightPanel-container,
body.fullscreen-active .rightPanel-container {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 在全屏模式下隐藏侧边栏 */
.fullscreen-mode .sidebar-container,
.fullscreen-active .sidebar-container,
html.fullscreen-mode .sidebar-container,
html.fullscreen-active .sidebar-container,
body.fullscreen-mode .sidebar-container,
body.fullscreen-active .sidebar-container {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  pointer-events: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  height: 0 !important;
  max-width: 0 !important;
  max-height: 0 !important;
  z-index: -9999 !important;
  transform: translateX(-100%) !important;
  transition: none !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* 确保侧边栏的所有子元素也被隐藏 */
.fullscreen-mode .sidebar-container *,
.fullscreen-active .sidebar-container *,
html.fullscreen-mode .sidebar-container *,
html.fullscreen-mode .sidebar-container *,
body.fullscreen-mode .sidebar-container *,
body.fullscreen-active .sidebar-container * {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 确保全屏模式下的主容器占满整个屏幕 */
.fullscreen-mode .main-container,
.fullscreen-active .main-container,
html.fullscreen-mode .main-container,
html.fullscreen-mode .main-container,
body.fullscreen-mode .main-container,
body.fullscreen-active .main-container {
  margin-left: 0 !important;
  width: 100% !important;
  max-width: 100vw !important;
  left: 0 !important;
}

/* 全屏模式下的特殊处理 */
.fullscreen-mode #app,
.fullscreen-active #app,
html.fullscreen-mode #app,
html.fullscreen-active #app,
body.fullscreen-mode #app,
body.fullscreen-active #app {
  overflow: hidden !important;
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: #001528 !important;
}

/* 小屏幕模式样式 */
.small-screen-mode {
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  background-color: #001528 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  touch-action: none !important; /* 禁止触摸滚动 */
  -webkit-overflow-scrolling: none !important; /* 禁止iOS惯性滚动 */
  overscroll-behavior: none !important; /* 禁止过度滚动行为 */
}

/* 确保大屏幕模式下内容正常显示 */
@media screen and (min-width: 1920px) {
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    width: 100vw !important;
    height: 100vh !important;
  }

  #app {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 确保滚动条样式与整体设计一致 */
  ::-webkit-scrollbar {
    width: 8px;
    background-color: rgba(0, 20, 40, 0.3);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 168, 255, 0.5);
    border-radius: 4px;
  }

  /* 确保3D模型容器可见 */
  .model-container, .model-wrapper, .model-view {
    overflow: visible !important;
    height: auto !important;
    min-height: 500px !important;
  }

  /* 确保所有图表和模块可见 */
  .chart-container, .module-container, .panel {
    overflow: visible !important;
    height: auto !important;
    min-height: 300px !important;
  }

  /* 移除所有可能的边距和填充 */
  .dashboard-container {
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 30, 60, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 168, 255, 0.7);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 168, 255, 0.9);
}

/* 响应式调整 */
@media screen and (max-width: 1366px) {
  .widget-header {
    font-size: 14px;
    padding: 6px 10px;
  }

  .widget-content {
    padding: 8px;
  }

  /* 小屏幕模式下确保内容完全显示 */
  .dashboard-container {
    transform-origin: center center !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: translate(-50%, -50%) !important;
  }

  /* 确保所有容器在小屏幕上正确显示 */
  .widget, .model-viewer-panel, .left-column, .center-column, .right-column {
    overflow: hidden !important;
  }

  /* 确保左侧列在小屏幕上正确显示 */
  .left-column {
    height: 100% !important;
    overflow: hidden !important;
  }

  /* 确保环境监控模块在小屏幕上正确显示 */
  .environment-monitor {
    height: 400px !important;
    overflow: hidden !important;
  }

  /* 确保主内容区域在小屏幕上正确显示 */
  .main-content {
    overflow: hidden !important;
    height: calc(100% - 80px) !important;
  }

  /* 禁止所有可能的滚动 */
  html, body, #app, .dashboard-container, .main-content, .left-column, .center-column, .right-column {
    overflow: hidden !important;
    touch-action: none !important;
    -webkit-overflow-scrolling: none !important;
    overscroll-behavior: none !important;
  }

  /* 固定body和html */
  html, body {
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }
}

@media screen and (max-width: 1024px) {
  .widget-header {
    font-size: 12px;
    padding: 5px 8px;
  }

  .widget-content {
    padding: 6px;
  }
}

/* 添加一些动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* 确保图表容器在所有屏幕尺寸下都能正确显示 */
.chart-container {
  min-height: 200px;
}
