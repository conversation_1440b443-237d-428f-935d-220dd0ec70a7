<template>
  <div class="model-container default-theme">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增型号
      </el-button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索型号名称"
        style="width: 200px; margin-left: 16px"
        clearable
        @clear="handleSearch"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
    </div>

    <!-- 型号列表 -->
    <el-table
      v-loading="loading"
      :data="modelList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="型号名称" min-width="150" />
      <el-table-column prop="code" label="型号编码" min-width="150" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column label="创建时间" prop="created_at" min-width="150">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 型号表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="modelForm"
        :model="modelForm"
        :rules="modelRules"
        label-width="100px"
      >
        <el-form-item label="型号名称" prop="name">
          <el-input v-model="modelForm.name" placeholder="请输入型号名称"></el-input>
        </el-form-item>
        <el-form-item label="型号编码" prop="code">
          <el-input v-model="modelForm.code" placeholder="请输入型号编码">
            <el-button slot="append" @click="modelForm.code = generateRandomCode()">随机生成</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="modelForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入型号描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAllModels,
  addModel,
  getModelById,
  updateModel,
  deleteModel
} from '@/api/system/model'

export default {
  name: 'ModelManagement',
  data() {
    return {
      loading: false,
      searchQuery: '',
      modelList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      modelForm: {
        id: undefined,
        name: '',
        code: '',
        description: ''
      },
      modelRules: {
        name: [
          { required: true, message: '请输入型号名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入型号编码', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchModelList()
  },
  mounted() {
    // 确保页面样式是正确的
    document.body.style.background = ''
    document.body.style.overflow = 'auto'
    document.documentElement.style.overflow = 'auto'
    
    // 移除大屏页面可能添加的类
    document.body.classList.remove('screen-mode')
    document.documentElement.classList.remove('screen-mode')
    
    // 确保布局正确渲染
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 100)
  },
  beforeDestroy() {
    // 重置所有引用
    this.modelList = []
    this.modelForm = {
      id: undefined,
      name: '',
      code: '',
      description: ''
    }
  },
  methods: {
    // 获取型号列表
    async fetchModelList() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchQuery
        }
        const response = await getAllModels(params)
        
        if (response && response.code === 0 && response.data) {
          // 设置总数
          this.total = response.data.total || 0
          
          // 映射数据字段
          const mappedData = response.data.list.map(item => ({
            id: item.id,
            name: item.model_name,
            code: item.model_code,
            description: item.description,
            created_at: item.created_at,
            updated_at: item.updated_at
          }))
          
          this.modelList = mappedData
        } else {
          this.$message.error(response?.message || '获取型号列表失败')
          this.modelList = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取型号列表失败:', error)
        this.$message.error('获取型号列表失败，请稍后重试')
        this.modelList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchModelList()
    },

    // 新增型号
    handleAdd() {
      this.dialogTitle = '新增型号'
      this.dialogVisible = true
      this.modelForm = {
        id: undefined,
        name: '',
        code: this.generateRandomCode(),
        description: ''
      }
    },

    // 生成随机型号编码
    generateRandomCode() {
      const prefix = 'BCM'
      const randomNum = Math.floor(10000 + Math.random() * 90000) // 5位随机数
      return `${prefix}${randomNum}`
    },

    // 编辑型号
    async handleEdit(row) {
      this.dialogTitle = '编辑型号'
      this.dialogVisible = true
      
      try {
        const response = await getModelById(row.id)
        
        if (response && response.code === 0 && response.data) {
          this.modelForm = {
            id: response.data.id,
            name: response.data.model_name,
            code: response.data.model_code,
            description: response.data.description || ''
          }
        } else {
          this.$message.error(response?.message || '获取型号详情失败')
        }
      } catch (error) {
        console.error('获取型号详情失败:', error)
        this.$message.error('获取型号详情失败，请稍后重试')
      }
    },

    // 删除型号
    handleDelete(row) {
      this.$confirm('确认删除该型号吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteModel(row.id)
          
          if (response && response.code === 0) {
            this.$message.success('删除成功')
            this.fetchModelList()
          } else {
            this.$message.error(response?.message || '删除型号失败')
          }
        } catch (error) {
          console.error('删除型号失败:', error)
          this.$message.error('删除型号失败，请稍后重试')
        }
      }).catch(() => {})
    },

    // 提交表单
    handleSubmit() {
      this.$refs.modelForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            const submitData = { 
              model_name: this.modelForm.name,
              model_code: this.modelForm.code,
              description: this.modelForm.description
            }
            
            if (this.modelForm.id) {
              // 更新型号
              response = await updateModel(this.modelForm.id, submitData)
            } else {
              // 创建型号
              response = await addModel(submitData)
            }
            
            if (response && response.code === 0) {
              this.$message.success('保存成功')
              this.dialogVisible = false
              this.fetchModelList()
            } else {
              this.$message.error(response?.message || '保存型号失败')
            }
          } catch (error) {
            console.error('保存型号失败:', error)
            this.$message.error('保存型号失败，请稍后重试')
          }
        }
      })
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchModelList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchModelList()
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.modelForm.resetFields()
    }
  }
}
</script>

<style scoped>
.model-container {
  padding: 20px;
  background-color: #fff; /* 确保背景为白色 */
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1; /* 确保层级高于可能的背景元素 */
  width: 100%;
  min-height: calc(100vh - 120px);
  overflow: auto;
}

/* 重置可能继承的全局样式 */
:deep(.el-table) {
  background-color: #fff;
  color: #606266;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
}

:deep(.el-table td) {
  color: #606266;
}

:deep(.el-button) {
  background-color: #fff;
  color: #606266;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  color: #fff;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  color: #fff;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.no-data {
  color: #909399;
  font-style: italic;
}
</style> 