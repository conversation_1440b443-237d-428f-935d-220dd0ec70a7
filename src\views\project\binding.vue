<template>
  <div class="project-binding-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>项目绑定到单位</span>
        <div class="operation">
          <el-alert
            v-if="!isAdmin"
            title="当前功能仅限管理员使用"
            type="warning"
            show-icon
            :closable="false"
          ></el-alert>
        </div>
      </div>
      
      <!-- 单位选择 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item label="选择单位">
            <el-select
              v-model="filterForm.accountId"
              placeholder="请选择单位"
              @change="handleAccountChange"
              :disabled="!isAdmin"
              clearable
            >
              <el-option
                v-for="item in accountList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 项目绑定状态标题 -->
      <div class="table-title">
        <h3>{{ getTableTitle() }}</h3>
      </div>
      
      <!-- 项目列表 -->
      <el-table
        v-loading="tableLoading"
        :data="projectList"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="number" label="项目号" width="180"></el-table-column>
        <el-table-column prop="name" label="项目名称" min-width="200"></el-table-column>
        <el-table-column prop="account_name" label="所属单位" min-width="150"></el-table-column>
        <el-table-column prop="location" label="项目位置" min-width="150"></el-table-column>
        <el-table-column label="项目状态" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTypeFromCode(scope.row.status)">
              {{ getStatusTextFromCode(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="安装日期" width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.installation_date) || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <!-- 如果项目所属单位是admin，显示绑定按钮 -->
            <el-button
              v-if="isAdminAccount(scope.row.account_id) && isAdmin"
              size="mini"
              type="primary"
              @click="showBindToAccountDialog(scope.row)"
            >绑定</el-button>
            
            <!-- 如果项目所属单位不是admin，显示解除绑定按钮 -->
            <el-button
              v-else-if="!isAdminAccount(scope.row.account_id) && isAdmin"
              size="mini"
              type="danger"
              @click="handleUnbindToAdmin(scope.row)"
            >解除绑定</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 绑定到账户对话框 -->
    <el-dialog
      title="绑定项目到单位"
      :visible.sync="bindToAccountDialogVisible"
      width="40%"
    >
      <div class="bind-account-form">
        <el-form :model="bindToAccountForm" label-width="100px">
          <el-form-item label="项目名称">
            <span>{{ bindToAccountForm.projectName }}</span>
          </el-form-item>
          <el-form-item label="选择单位" required>
            <el-select
              v-model="bindToAccountForm.accountId"
              placeholder="请选择要绑定的单位"
              style="width: 100%;"
            >
              <el-option
                v-for="item in accountList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                :disabled="isAdminAccount(item.id)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="bindToAccountDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="bindProjectToSelectedAccount" :loading="bindLoading" :disabled="!bindToAccountForm.accountId">确认绑定</el-button>
      </div>
    </el-dialog>

    <!-- 搜索项目对话框(原绑定项目对话框，保留但隐藏) -->
    <el-dialog
      title="绑定项目到单位"
      :visible.sync="bindDialogVisible"
      width="50%"
    >
      <div class="bind-search">
        <el-input
          v-model="searchQuery"
          placeholder="搜索项目名称或项目号"
          style="width: 300px; margin-bottom: 20px;"
          @keyup.enter.native="searchProjects"
        >
          <el-button slot="append" icon="el-icon-search" @click="searchProjects"></el-button>
        </el-input>
      </div>

      <el-table
        v-loading="bindTableLoading"
        :data="availableProjects"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        ></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="number" label="项目号" width="180"></el-table-column>
        <el-table-column prop="name" label="项目名称" min-width="200"></el-table-column>
        <el-table-column prop="location" label="项目位置" min-width="150"></el-table-column>
        <el-table-column label="项目状态" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTypeFromCode(scope.row.status)">
              {{ getStatusTextFromCode(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="bindDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="bindProjects" :loading="bindLoading" :disabled="selectedProjects.length === 0">绑 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMore } from '@/api/admin';
import { getProjectList, bindProjectToAccount, getAccountProjects } from '@/api/project';
import { checkPermi } from '@/utils/permission';

export default {
  name: 'AccountBinding',
  data() {
    return {
      isAdmin: false,
      accountList: [],
      projectList: [],
      allProjects: [], // 用于存储所有项目
      availableProjects: [],
      selectedProjects: [],
      tableLoading: false,
      bindTableLoading: false,
      bindLoading: false,
      bindDialogVisible: false,
      bindToAccountDialogVisible: false, // 新增：绑定到账户对话框
      bindToAccountForm: { // 新增：绑定到账户表单
        projectId: null,
        projectName: '',
        accountId: null
      },
      searchQuery: '',
      filterForm: {
        accountId: '',
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      searchPagination: {
        page: 1,
        size: 10,
        total: 0
      },
      adminAccountId: 0 // admin账户ID，默认为0
    };
  },
  created() {
    this.checkAdminPermission();
    this.getAccountList();
    this.fetchAllProjects();
  },
  methods: {
    // 检查管理员权限
    checkAdminPermission() {
      this.isAdmin = this.$store.state.user.admin;
      if (!this.isAdmin) {
        this.$message.warning('当前功能仅限管理员使用');
      }
    },
    
    // 检查是否是admin账户
    isAdminAccount(accountId) {
      return accountId === this.adminAccountId || accountId === '0';
    },
    
    // 获取账户列表
    async getAccountList() {
      try {
        const response = await getMore({ page: 1, size: 1000 });
        if (response && response.data) {
          this.accountList = response.data;
        }
      } catch (error) {
        console.error('获取单位列表失败:', error);
        this.$message.error('获取单位列表失败');
      }
    },
    
    // 获取所有项目列表
    async fetchAllProjects() {
      this.tableLoading = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.size
        };
        
        // 使用getAccountProjects方法获取所有项目列表，使用adminAccountId表示获取所有项目
        const response = await getAccountProjects();
        if (response && response.code === 0 && response.data) {
          // 确保数据结构与账户项目列表一致
          this.projectList = (response.data.list || []).map(project => {
            // 标准化数据结构
            return this.normalizeProjectData(project);
          });
          this.allProjects = [...this.projectList]; // 保存所有项目的副本
          this.pagination.total = response.data.total || 0;
        } else {
          this.projectList = [];
          this.allProjects = [];
          this.pagination.total = 0;
          this.$message.error(response?.message || '获取项目列表失败');
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
        this.$message.error('获取项目列表失败');
        this.projectList = [];
        this.allProjects = [];
        this.pagination.total = 0;
      } finally {
        this.tableLoading = false;
      }
    },
    
    // 获取表格标题
    getTableTitle() {
      if (this.filterForm.accountId) {
        const account = this.accountList.find(a => a.id === this.filterForm.accountId);
        return account ? `${account.name}绑定的项目列表` : '当前单位绑定的项目列表';
      } else {
        return '所有项目绑定关系';
      }
    },
    
    // 处理账户变更
    handleAccountChange(accountId) {
      if (!accountId) {
        // 如果清除了选择的单位，显示所有项目
        this.fetchAllProjects();
      } else {
        // 选择单位后自动查询
        this.fetchAccountProjects();
      }
    },
    
    // 处理筛选（保留方法但不再使用）
    handleFilter() {
      if (this.filterForm.accountId !== '' && this.filterForm.accountId !== undefined) {
        this.fetchAccountProjects();
      } else {
        // 如果未选择单位，则显示所有项目
        this.fetchAllProjects();
      }
    },
    
    // 获取账户绑定的项目列表
    async fetchAccountProjects() {
      if (this.filterForm.accountId === '' || this.filterForm.accountId === undefined) {
        this.$message.warning('请先选择单位');
        return;
      }
      
      this.tableLoading = true;
      try {
        const response = await getAccountProjects(this.filterForm.accountId);
        if (response && response.code === 0 && response.data) {
          // 确保数据结构与全部项目列表一致
          this.projectList = (response.data.list || []).map(project => {
            // 标准化数据结构
            return this.normalizeProjectData(project);
          });
          this.pagination.total = response.data.total || 0;
        } else {
          this.projectList = [];
          this.pagination.total = 0;
          this.$message.error(response?.message || '获取项目列表失败');
        }
      } catch (error) {
        console.error('获取账户项目失败:', error);
        this.$message.error('获取账户项目失败');
        this.projectList = [];
        this.pagination.total = 0;
      } finally {
        this.tableLoading = false;
      }
    },
    
    // 显示绑定到账户对话框
    showBindToAccountDialog(project) {
      this.bindToAccountForm = {
        projectId: project.id,
        projectName: project.name,
        accountId: null
      };
      this.bindToAccountDialogVisible = true;
    },
    
    // 处理解除绑定到admin
    async handleUnbindToAdmin(project) {
      try {
        await this.$confirm(`确定要解除项目"${project.name}"与当前单位的绑定关系并绑定到管理员账户吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const data = {
          account_id: this.adminAccountId, // 解绑后绑定到admin
          project_id: project.id,
          old_account_id: project.account_id // 当前项目所属账户
        };
        
        const response = await bindProjectToAccount(data);
        if (response && response.code === 0) {
          this.$message.success('解除绑定并绑定到管理员成功');
          this.fetchAllProjects(); // 刷新列表
        } else {
          this.$message.error(response?.message || '操作失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('操作失败:', error);
          this.$message.error('操作失败');
        }
      }
    },
    
    // 绑定项目到选择的账户
    async bindProjectToSelectedAccount() {
      if (!this.bindToAccountForm.accountId) {
        this.$message.warning('请选择要绑定的单位');
        return;
      }
      
      this.bindLoading = true;
      try {
        const data = {
          account_id: this.bindToAccountForm.accountId,
          project_id: this.bindToAccountForm.projectId,
          old_account_id: this.adminAccountId // 指定原账户为admin
        };
        
        const response = await bindProjectToAccount(data);
        if (response && response.code === 0) {
          this.$message.success('项目绑定成功');
          this.bindToAccountDialogVisible = false;
          this.fetchAllProjects(); // 刷新列表
        } else {
          this.$message.error(response?.message || '项目绑定失败');
        }
      } catch (error) {
        console.error('绑定项目失败:', error);
        this.$message.error('绑定项目失败');
      } finally {
        this.bindLoading = false;
      }
    },
    
    // 原绑定项目相关函数保留
    showBindDialog() {
      if (this.filterForm.accountId === '' || this.filterForm.accountId === undefined) {
        this.$message.warning('请先选择单位');
        return;
      }
      
      this.bindDialogVisible = true;
      this.searchProjects();
    },
    
    // 搜索可绑定的项目
    async searchProjects() {
      this.bindTableLoading = true;
      try {
        const params = {
          page: this.searchPagination.page,
          pageSize: this.searchPagination.size,
          query: this.searchQuery
        };
        
        // 从admin账户获取所有项目
        const response = await getAccountProjects(this.adminAccountId);
        if (response && response.code === 0 && response.data) {
          // 过滤已绑定的项目和匹配搜索条件的项目
          const boundProjectIds = this.projectList.map(p => p.id);
          this.availableProjects = (response.data.list || [])
            .map(project => this.normalizeProjectData(project))
            .filter(p => {
              // 过滤掉已绑定的项目
              if (boundProjectIds.includes(p.id)) return false;
              
              // 如果有搜索关键字，过滤匹配项目名称或项目号的项目
              if (this.searchQuery) {
                const searchLower = this.searchQuery.toLowerCase();
                return (p.name && p.name.toLowerCase().includes(searchLower)) || 
                       (p.number && p.number.toLowerCase().includes(searchLower));
              }
              
              return true;
            });
          this.searchPagination.total = this.availableProjects.length;
        } else {
          this.availableProjects = [];
          this.searchPagination.total = 0;
          this.$message.error(response?.message || '获取可绑定项目列表失败');
        }
      } catch (error) {
        console.error('搜索项目失败:', error);
        this.$message.error('搜索项目失败');
        this.availableProjects = [];
        this.searchPagination.total = 0;
      } finally {
        this.bindTableLoading = false;
      }
    },
    
    // 处理选择项目变更
    handleSelectionChange(selection) {
      this.selectedProjects = selection;
    },
    
    // 绑定项目到账户
    async bindProjects() {
      if (this.selectedProjects.length === 0) {
        this.$message.warning('请选择要绑定的项目');
        return;
      }
      
      if (this.filterForm.accountId === '' || this.filterForm.accountId === undefined) {
        this.$message.warning('请先选择单位');
        return;
      }
      
      // 标准化选中的项目，确保数据结构一致
      const normalizedSelectedProjects = this.selectedProjects.map(p => this.normalizeProjectData(p));
      
      this.bindLoading = true;
      try {
        // 逐个绑定项目
        for (const project of normalizedSelectedProjects) {
          const data = {
            account_id: this.filterForm.accountId,
            project_id: project.id,
            old_account_id: project.account_id || this.adminAccountId
          };
          
          const response = await bindProjectToAccount(data);
          if (!(response && response.code === 0)) {
            this.$message.error(`项目"${project.name}"绑定失败: ${response?.message || '未知错误'}`);
          }
        }
        
        this.$message.success('项目绑定操作完成');
        this.bindDialogVisible = false;
        this.fetchAccountProjects(); // 刷新列表
        // 同时更新allProjects以保持数据同步
        this.fetchAllProjects();
      } catch (error) {
        console.error('绑定项目失败:', error);
        this.$message.error('绑定项目失败');
      } finally {
        this.bindLoading = false;
      }
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.size = val;
      if (this.filterForm.accountId !== '' && this.filterForm.accountId !== undefined) {
        this.fetchAccountProjects();
      } else {
        this.fetchAllProjects();
      }
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val;
      if (this.filterForm.accountId !== '' && this.filterForm.accountId !== undefined) {
        this.fetchAccountProjects();
      } else {
        this.fetchAllProjects();
      }
    },
    
    // 获取项目状态类型
    getStatusTypeFromCode(statusCode) {
      const statusTypeMap = {
        0: 'info',
        1: 'primary',
        2: 'success',
        3: 'warning'
      };
      return statusTypeMap[statusCode] || 'info';
    },
    
    // 获取项目状态文本
    getStatusTextFromCode(statusCode) {
      const statusMap = {
        0: '筹备中',
        1: '进行中',
        2: '已完成',
        3: '已暂停'
      };
      return statusMap[statusCode] || '未知状态';
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },
    
    // 标准化项目数据结构
    normalizeProjectData(project) {
      // 创建一个新对象，确保必要的字段都存在
      const normalizedProject = { ...project };
      
      // 确保account_name字段存在
      if (!normalizedProject.account_name && normalizedProject.account && normalizedProject.account.name) {
        normalizedProject.account_name = normalizedProject.account.name;
      }
      
      // 确保account_id字段存在
      if (normalizedProject.account_id === undefined && normalizedProject.account && normalizedProject.account.id !== undefined) {
        normalizedProject.account_id = normalizedProject.account.id;
      }
      
      // 确保安装日期字段统一
      if (!normalizedProject.installation_date && normalizedProject.installationDate) {
        normalizedProject.installation_date = normalizedProject.installationDate;
      } else if (!normalizedProject.installation_date && normalizedProject.installDate) {
        normalizedProject.installation_date = normalizedProject.installDate;
      }
      
      // 确保项目号字段统一
      if (!normalizedProject.number && normalizedProject.projectNo) {
        normalizedProject.number = normalizedProject.projectNo;
      }
      
      // 确保状态字段是数字类型
      if (normalizedProject.status !== undefined && typeof normalizedProject.status !== 'number') {
        // 尝试将状态转换为数字
        normalizedProject.status = parseInt(normalizedProject.status) || 0;
      } else if (normalizedProject.status === undefined) {
        // 设置默认状态为0(筹备中)
        normalizedProject.status = 0;
      }
      
      // 确保ID字段为数字
      if (normalizedProject.id !== undefined && typeof normalizedProject.id !== 'number') {
        normalizedProject.id = parseInt(normalizedProject.id);
      }
      
      // 确保位置字段存在
      if (!normalizedProject.location) {
        normalizedProject.location = '未设置';
      }
      
      return normalizedProject;
    }
  }
};
</script>

<style lang="scss" scoped>
.project-binding-container {
  padding: 20px;
  
  .filter-section {
    margin-bottom: 20px;
  }
  
  .operation {
    float: right;
    margin-top: -5px;
  }
  
  .table-title {
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
      color: #606266;
      font-size: 18px;
      font-weight: 500;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .bind-search {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
  }
  
  .bind-account-form {
    padding: 0 20px;
  }
}
</style> 