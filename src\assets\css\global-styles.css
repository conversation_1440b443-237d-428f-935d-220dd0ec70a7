/* 全局样式 - 确保背景全屏显示 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
}

/* 确保全屏模式下背景正确显示 */
body.fullscreen-mode,
html.fullscreen-mode,
body.small-screen-mode,
html.small-screen-mode,
body.fullscreen-active,
html.fullscreen-active {
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
}

/* 确保所有容器正确显示 */
.dashboard-container,
.main-content {
  overflow: visible !important;
  pointer-events: none !important; /* 确保鼠标事件可以传递到模型 */
}

.tech-background {
  overflow: visible !important;
}

/* 确保3D透视效果正确显示 */
.tech-background {
  perspective: 1000px !important;
  transform-style: preserve-3d !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: -1 !important;
}

/* 设置列的定位，使左右两侧模块漂浮在模型上面 */
.center-column {
  position: fixed !important;
  width: 100vw !important;
  height: 100vh !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1 !important;
  overflow: visible !important;
  min-height: 100vh !important; /* 确保最小高度为视口高度 */
  max-height: none !important; /* 移除最大高度限制 */
}

.left-column {
  position: relative !important;
  z-index: 10 !important;
  width: 350px !important;
  pointer-events: auto !important; /* 确保左侧列可以接收鼠标事件 */
}

.right-column {
  position: relative !important;
  z-index: 10 !important;
  width: 350px !important;
  pointer-events: auto !important; /* 确保右侧列可以接收鼠标事件 */
}
