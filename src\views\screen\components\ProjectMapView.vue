<template>
  <div class="map-container">
    <div class="map-header">
      <h1 class="title">桥梁智能监测系统</h1>
      <div class="time">{{ currentTime }}</div>
    </div>

    <!-- 中国地图容器 -->
    <div class="china-map" ref="chinaMapContainer">
      <!-- 地图将在这里渲染 -->
      <canvas ref="mapCanvas" class="map-canvas"></canvas>

      <!-- 项目标记点 -->
      <div
        v-for="project in projectList"
        :key="project.id"
        class="project-marker"
        :class="{ 'active': hoveredProject && hoveredProject.id === project.id }"
        :style="getMarkerPosition(project)"
        @mouseenter="$emit('show-project-info', project, $event)"
        @mouseleave="$emit('hide-project-info')"
        @click="$emit('select-project', project.id)"
      >
        <div class="marker-pulse"></div>
        <div class="marker-point"></div>
        <div class="marker-label">{{ project.name }}</div>
      </div>

      <!-- 添加调试信息 -->
      <div class="debug-info" v-if="showDebugInfo">
        <div>Container: {{ containerSize.width }}x{{ containerSize.height }}</div>
        <div>Renderer: {{ rendererInfo }}</div>
        <div>Map Features: {{ mapFeaturesCount }}</div>
      </div>

      <!-- 添加调试按钮 -->
      <div class="debug-controls" v-if="showDebugInfo">
        <button @click="resetCamera">重置相机</button>
        <button @click="toggleWireframe">切换线框</button>
        <button @click="zoomIn">放大</button>
        <button @click="zoomOut">缩小</button>
      </div>
    </div>

    <!-- 项目信息弹窗 -->
    <div class="project-popup" v-if="hoveredProject" :style="popupStyle">
      <div class="popup-header">
        <h3>{{ hoveredProject.name }}</h3>
        <span class="project-status" :class="hoveredProject.status">
          {{ getStatusText(hoveredProject.status) }}
        </span>
      </div>
      <div class="popup-content">
        <div class="popup-info">
          <div class="info-item">
            <i class="el-icon-location"></i>
            <span>{{ hoveredProject.location }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-date"></i>
            <span>安装日期: {{ hoveredProject.installDate }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-s-data"></i>
            <span>进度: {{ hoveredProject.progress }}%</span>
          </div>
        </div>
        <div class="popup-actions">
          <el-button
            type="primary"
            size="small"
            @click="$emit('select-project', hoveredProject.id)"
          >
            查看详情
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-panel">
      <div class="stat-item">
        <div class="stat-value">{{ projectList.length }}</div>
        <div class="stat-label">项目总数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ getProjectCountByStatus('in-progress') }}</div>
        <div class="stat-label">进行中</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ getProjectCountByStatus('completed') }}</div>
        <div class="stat-label">已完成</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ getProjectCountByStatus('preparing') }}</div>
        <div class="stat-label">筹备中</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { mapData } from '../mapData/chinaMap';

export default {
  name: 'ProjectMapView',
  props: {
    projectList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      currentTime: '',
      hoveredProject: null,
      showDebugInfo: false,
      containerSize: {
        width: 0,
        height: 0
      },
      rendererInfo: '',
      mapFeaturesCount: 0,
      scene: null,
      camera: null,
      renderer: null,
      mapGroup: null,
      clock: null,
      animationFrameId: null
    };
  },
  mounted() {
    this.initMap();
    this.startClock();
    this.handleResize();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    this.disposeMap();
  },
  methods: {
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 初始化地图
    initMap() {
      console.log('****** Initializing 3D map...');

      try {
        // 创建Three.js场景
        this.scene = new THREE.Scene();

        // 设置透明背景，以便能够看到大屏背景
        this.scene.background = null;

        // 获取容器尺寸
        this.containerSize.width = this.$refs.chinaMapContainer.clientWidth || 800;
        this.containerSize.height = this.$refs.chinaMapContainer.clientHeight || 600;

        // 如果容器高度为0，设置一个默认高度
        if (this.containerSize.height === 0) {
          console.warn('Container height is 0, setting default height');
          this.$refs.chinaMapContainer.style.height = '600px';
          this.containerSize.height = 600;
        }

        console.log(`Container size: ${this.containerSize.width}x${this.containerSize.height}`);

        // 设置相机
        this.camera = new THREE.PerspectiveCamera(
          45,
          this.containerSize.width / Math.max(this.containerSize.height, 1),
          0.1,
          1000
        );
        this.camera.position.set(0, 0, 150);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
          canvas: this.$refs.mapCanvas,
          antialias: true,
          alpha: true
        });
        this.renderer.setSize(this.containerSize.width, Math.max(this.containerSize.height, 1));
        this.renderer.setPixelRatio(window.devicePixelRatio);

        // 更新调试信息
        this.rendererInfo = `${this.containerSize.width}x${this.containerSize.height}, Pixel Ratio: ${window.devicePixelRatio}`;

        // 创建控制器
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableRotate = true;
        this.controls.autoRotate = false;

        // 添加灯光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
        directionalLight.position.set(0, 0, 100);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        const hemisphereLight = new THREE.HemisphereLight(0xffffbb, 0x080820, 0.5);
        this.scene.add(hemisphereLight);

        // 添加坐标轴辅助工具
        const axesHelper = new THREE.AxesHelper(100);
        this.scene.add(axesHelper);

        // 添加网格辅助工具
        const gridHelper = new THREE.GridHelper(200, 20);
        this.scene.add(gridHelper);

        // 创建地图组
        this.mapGroup = new THREE.Group();
        this.scene.add(this.mapGroup);

        // 检查地图数据
        console.log('******** mapData', mapData);

        // 加载真实地图数据
        this.loadRealMapData();

        // 开始动画循环
        this.animate();

        console.log('Map initialization complete');
      } catch (error) {
        console.error('Error initializing map:', error);
        // 尝试创建回退地图
        this.renderSimpleMap();
      }
    },

    // 加载地图数据
    loadMapData() {
      try {
        console.log('Starting to load map data...');

        // 检查地图数据
        if (!mapData || !mapData.features || !Array.isArray(mapData.features)) {
          console.error('Map data check failed, using fallback map');
          this.createFallbackMap();
          return;
        }

        // 清除现有地图
        while (this.mapGroup.children.length > 0) {
          const child = this.mapGroup.children[0];
          this.mapGroup.remove(child);
          if (child.geometry) child.geometry.dispose();
          if (child.material) child.material.dispose();
        }

        console.log(`Processing ${mapData.features.length} map features...`);

        // 创建省份网格
        let successCount = 0;
        mapData.features.forEach((feature, index) => {
          try {
            // 检查特征是否有效
            if (!feature || !feature.properties || !feature.properties.name) {
              console.warn(`Invalid feature at index ${index}`);
              return;
            }

            const provinceName = feature.properties.name;
            console.log(`Creating province: ${provinceName}`);

            // 创建省份网格
            const mesh = this.createProvinceMesh(feature);
            if (mesh) {
              this.mapGroup.add(mesh);
              successCount++;
              console.log(`Successfully added province: ${provinceName}`);
            } else {
              console.warn(`Failed to create mesh for province: ${provinceName}`);
            }
          } catch (error) {
            console.error(`Error processing feature at index ${index}:`, error);
          }
        });

        // 更新调试信息
        this.mapFeaturesCount = successCount;

        console.log(`Successfully added ${successCount} out of ${mapData.features.length} provinces to map`);

        if (successCount === 0) {
          console.error('No provinces were successfully added, using fallback map');
          this.renderSimpleMap();
          return;
        }

        // 确保地图居中显示
        this.centerMap();

        // 缩放地图以适应容器
        this.scaleMap();

        console.log('Map data loading complete');
      } catch (error) {
        console.error('Error loading map data:', error);
        this.renderSimpleMap();
      }
    },

    // 创建省份
    createProvince(coordinates, name, isHighlighted) {
      const provinceGroup = new THREE.Group();
      provinceGroup.name = name;

      // 设置材质
      const material = new THREE.MeshPhongMaterial({
        color: isHighlighted ? 0x3498db : 0x1a5fb4,
        transparent: true,
        opacity: isHighlighted ? 0.9 : 0.6,
        side: THREE.DoubleSide,
        shininess: 50,
        emissive: isHighlighted ? 0x1a5fb4 : 0x0f2643,
        emissiveIntensity: isHighlighted ? 0.6 : 0.3
      });

      // 处理多边形
      coordinates.forEach(polygon => {
        const shape = new THREE.Shape();

        // 创建形状
        polygon[0].forEach((point, index) => {
          // 缩放坐标以适应屏幕
          const x = (point[0] - 105) * 0.7;
          const y = (point[1] - 35) * 0.7;

          if (index === 0) {
            shape.moveTo(x, y);
          } else {
            shape.lineTo(x, y);
          }
        });

        // 创建几何体
        const geometry = new THREE.ExtrudeGeometry(shape, {
          depth: isHighlighted ? 3 : 1,
          bevelEnabled: true,
          bevelThickness: 0.3,
          bevelSize: 0.2,
          bevelSegments: 1
        });

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);

        // 添加到省份组
        provinceGroup.add(mesh);

        // 如果是高亮省份，添加边框
        if (isHighlighted) {
          const edgesGeometry = new THREE.EdgesGeometry(geometry);
          const edgesMaterial = new THREE.LineBasicMaterial({
            color: 0x66ccff,
            linewidth: 2
          });
          const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
          provinceGroup.add(edges);

          // 添加发光效果
          const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x66ccff,
            transparent: true,
            opacity: 0.3,
            side: THREE.BackSide
          });

          const glowMesh = new THREE.Mesh(
            new THREE.ExtrudeGeometry(shape, {
              depth: 4,
              bevelEnabled: true,
              bevelThickness: 0.5,
              bevelSize: 0.3,
              bevelSegments: 1
            }),
            glowMaterial
          );

          glowMesh.scale.multiplyScalar(1.05);
          provinceGroup.add(glowMesh);
        }
      });

      return provinceGroup;
    },

    // 居中地图
    centerMap() {
      try {
        if (!this.mapGroup || this.mapGroup.children.length === 0) {
          console.warn('Map group is empty, cannot center');
          return;
        }

        // 计算地图边界
        const box = new THREE.Box3().setFromObject(this.mapGroup);

        // 检查边界是否有效
        if (!isFinite(box.min.x) || !isFinite(box.max.x) ||
            !isFinite(box.min.y) || !isFinite(box.max.y)) {
          console.warn('Invalid bounding box for map group');
          return;
        }

        // 计算中心点
        const center = box.getCenter(new THREE.Vector3());

        // 将地图移动到中心点
        this.mapGroup.position.x = -center.x;
        this.mapGroup.position.y = -center.y;
        this.mapGroup.position.z = 0; // 确保z位置为0

        console.log(`Centered map at (${center.x.toFixed(2)}, ${center.y.toFixed(2)})`);
      } catch (error) {
        console.error('Error centering map:', error);
      }
    },

    // 动画循环
    animate() {
      // 使用箭头函数保持 this 上下文
      const animateFrame = () => {
        this.animationFrameId = requestAnimationFrame(animateFrame);

        // 更新控制器
        if (this.controls) {
          this.controls.update();
        }

        // 渲染场景
        if (this.renderer && this.scene && this.camera) {
          this.renderer.render(this.scene, this.camera);
        }
      };

      // 启动动画循环
      animateFrame();

      console.log('Animation loop started');
    },

    // 处理窗口大小变化
    handleResize() {
      if (!this.renderer || !this.camera) return;

      // 更新相机宽高比
      this.camera.aspect = this.$refs.mapCanvas.clientWidth / this.$refs.mapCanvas.clientHeight;
      this.camera.updateProjectionMatrix();

      // 更新渲染器大小
      this.renderer.setSize(this.$refs.mapCanvas.clientWidth, this.$refs.mapCanvas.clientHeight);

      // 重新缩放地图
      this.scaleMap();
    },

    // 获取标记点位置
    getMarkerPosition(project) {
      // 如果项目有经纬度，使用经纬度计算位置
      if (project.longitude && project.latitude) {
        // 使用简单的线性映射将经纬度转换为屏幕坐标
        // 中国大致经度范围：73°-135°，纬度范围：4°-53°
        const minLng = 73;
        const maxLng = 135;
        const minLat = 4;
        const maxLat = 53;

        // 计算相对位置（0-1范围）
        const relX = (project.longitude - minLng) / (maxLng - minLng);
        const relY = 1 - (project.latitude - minLat) / (maxLat - minLat); // 纬度反转，因为屏幕Y轴向下

        // 转换为百分比位置
        return {
          left: `${relX * 100}%`,
          top: `${relY * 100}%`
        };
      }

      // 如果没有经纬度，使用预设位置
      return {
        left: '50%',
        top: '50%'
      };
    },

    // 设置标签位置
    setMarkerPosition(projectId, x, y) {
      const project = this.projectList.find(p => p.id === projectId);
      if (project) {
        // 更新项目的位置信息
        project.position = {
          x: x,
          y: y
        };

        // 强制更新视图
        this.$forceUpdate();

        return true;
      }
      return false;
    },

    // 清理地图资源
    disposeMap() {
      if (this.scene) {
        this.scene.traverse(object => {
          if (object.geometry) {
            object.geometry.dispose();
          }

          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else {
              object.material.dispose();
            }
          }
        });
      }

      if (this.renderer) {
        this.renderer.dispose();
        this.renderer = null;
      }

      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
    },

    // 获取项目状态文本
    getStatusText(status) {
      const statusMap = {
        'preparing': '筹备中',
        'in_progress': '进行中',
        'completed': '已完成',
        'paused': '已暂停'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取特定状态的项目数量
    getProjectCountByStatus(status) {
      return this.projectList.filter(project => project.status === status).length;
    },

    // 修改 createProvinceMesh 方法，确保正确创建省份网格
    createProvinceMesh(feature) {
      try {
        console.log(`Processing province: ${feature.properties.name}`);

        // 检查几何数据是否存在
        if (!feature.geometry || !feature.geometry.coordinates) {
          console.warn(`Missing geometry for province: ${feature.properties.name}`);
          return null;
        }

        // 创建省份形状
        const shapes = [];

        // 处理不同类型的几何数据
        if (feature.geometry.type === 'Polygon') {
          // 单个多边形
          const shape = this.createProvinceShape(feature.geometry.coordinates[0]);
          if (shape) shapes.push(shape);
        } else if (feature.geometry.type === 'MultiPolygon') {
          // 多个多边形
          feature.geometry.coordinates.forEach(coords => {
            const shape = this.createProvinceShape(coords[0]);
            if (shape) shapes.push(shape);
          });
        } else {
          console.warn(`Unsupported geometry type: ${feature.geometry.type} for province: ${feature.properties.name}`);
          return null;
        }

        if (shapes.length === 0) {
          console.warn(`No valid shapes created for province: ${feature.properties.name}`);
          return null;
        }

        // 创建几何体
        const geometry = new THREE.ExtrudeGeometry(shapes[0], {
          depth: 0.5,
          bevelEnabled: false
        });

        // 创建材质
        const isHighlighted = this.highlightedProvinces.includes(feature.properties.name);
        const material = new THREE.MeshPhongMaterial({
          color: isHighlighted ? 0x3498db : 0x1a5fb4,
          transparent: true,
          opacity: isHighlighted ? 0.9 : 0.7,
          side: THREE.DoubleSide,
          emissive: isHighlighted ? 0x1a5fb4 : 0x0f2643,
          emissiveIntensity: 0.3,
          shininess: 50
        });

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);
        mesh.name = feature.properties.name;
        mesh.userData = { ...feature.properties };

        console.log(`Successfully created mesh for province: ${feature.properties.name}`);
        return mesh;
      } catch (error) {
        console.error(`Error creating mesh for province: ${feature.properties?.name || 'unknown'}`, error);
        return null;
      }
    },

    // 添加 createProvinceShape 方法
    createProvinceShape(coordinates) {
      try {
        if (!coordinates || !Array.isArray(coordinates) || coordinates.length < 3) {
          console.warn('Invalid coordinates for shape');
          return null;
        }

        const shape = new THREE.Shape();

        // 移动到第一个点
        shape.moveTo(coordinates[0][0], coordinates[0][1]);

        // 连接其余的点
        for (let i = 1; i < coordinates.length; i++) {
          if (Array.isArray(coordinates[i]) && coordinates[i].length >= 2) {
            shape.lineTo(coordinates[i][0], coordinates[i][1]);
          }
        }

        // 闭合形状
        shape.closePath();

        return shape;
      } catch (error) {
        console.error('Error creating province shape:', error);
        return null;
      }
    },

    // 添加 scaleMap 方法，确保地图适应容器
    scaleMap() {
      try {
        if (!this.mapGroup || this.mapGroup.children.length === 0) {
          console.warn('Map group is empty, cannot scale');
          return;
        }

        // 计算地图边界
        const box = new THREE.Box3().setFromObject(this.mapGroup);

        // 检查边界是否有效
        if (!isFinite(box.min.x) || !isFinite(box.max.x) ||
            !isFinite(box.min.y) || !isFinite(box.max.y)) {
          console.warn('Invalid bounding box for map group');
          return;
        }

        const size = box.getSize(new THREE.Vector3());
        console.log(`Map size: ${size.x.toFixed(2)} x ${size.y.toFixed(2)}`);

        // 计算容器尺寸
        const containerWidth = this.containerSize.width;
        const containerHeight = this.containerSize.height;

        console.log(`Container size: ${containerWidth} x ${containerHeight}`);

        // 计算缩放比例，确保地图填满容器但不超出
        // 防止除以0或非常小的值
        const scaleX = size.x > 0.1 ? (containerWidth * 0.7) / size.x : 1;
        const scaleY = size.y > 0.1 ? (containerHeight * 0.7) / size.y : 1;

        // 确保缩放因子有效且不为0
        let scale = Math.min(scaleX, scaleY);
        if (!isFinite(scale) || scale <= 0.01) {
          console.warn(`Invalid scale factor: ${scale}, using default`);
          scale = 1;
        }

        console.log(`Scaling map by factor: ${scale.toFixed(2)}`);

        // 应用缩放
        this.mapGroup.scale.set(scale, scale, scale);

        // 确保地图居中
        this.centerMap();
      } catch (error) {
        console.error('Error scaling map:', error);
      }
    },

    // 添加地图数据检查函数
    checkMapData() {
      try {
        if (!mapData) {
          console.error('Map data is undefined');
          return false;
        }

        if (!mapData.features || !Array.isArray(mapData.features)) {
          console.error('Map data has no features array');
          return false;
        }

        console.log(`Map data contains ${mapData.features.length} features`);

        // 检查前几个特征
        const sampleSize = Math.min(3, mapData.features.length);
        for (let i = 0; i < sampleSize; i++) {
          const feature = mapData.features[i];
          console.log(`Sample feature ${i}:`, {
            name: feature.properties?.name,
            type: feature.geometry?.type,
            coordinates: feature.geometry?.coordinates ? 'Present' : 'Missing'
          });
        }

        return true;
      } catch (error) {
        console.error('Error checking map data:', error);
        return false;
      }
    },

    // 添加回退地图创建函数
    createFallbackMap() {
      console.log('Creating fallback map...');

      // 清除现有地图
      while (this.mapGroup.children.length > 0) {
        const child = this.mapGroup.children[0];
        this.mapGroup.remove(child);
        if (child.geometry) child.geometry.dispose();
        if (child.material) child.material.dispose();
      }

      // 创建一个简单的中国轮廓作为回退
      const shape = new THREE.Shape();

      // 简化的中国轮廓点（仅用于回退）
      const points = [
        [0, 0],
        [30, 5],
        [40, 0],
        [50, 10],
        [40, 20],
        [50, 30],
        [40, 40],
        [30, 35],
        [20, 40],
        [10, 30],
        [0, 20],
        [0, 0]
      ];

      // 创建形状
      shape.moveTo(points[0][0], points[0][1]);
      for (let i = 1; i < points.length; i++) {
        shape.lineTo(points[i][0], points[i][1]);
      }

      // 创建几何体
      const geometry = new THREE.ExtrudeGeometry(shape, {
        depth: 2,
        bevelEnabled: true,
        bevelThickness: 0.5,
        bevelSize: 0.5,
        bevelSegments: 3
      });

      // 创建材质
      const material = new THREE.MeshPhongMaterial({
        color: 0x0f2643, // 深蓝色，与大屏背景色一致
        transparent: true,
        opacity: 0.7,
        side: THREE.DoubleSide
      });

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);
      mesh.name = 'China Fallback';

      // 添加到地图组
      this.mapGroup.add(mesh);

      // 居中和缩放
      this.centerMap();
      this.scaleMap();

      console.log('Fallback map created');
    },

    // 添加场景清理方法
    disposeScene(scene) {
      scene.traverse(object => {
        if (object.geometry) {
          object.geometry.dispose();
        }

        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });
    },

    // 修改 renderSimpleMap 方法，确保能看到内容
    renderSimpleMap() {
      console.log('Rendering simple map...');

      try {
        // 清除现有地图
        while (this.mapGroup.children.length > 0) {
          const child = this.mapGroup.children[0];
          this.mapGroup.remove(child);
          if (child.geometry) child.geometry.dispose();
          if (child.material) child.material.dispose();
        }

        // 重置地图组位置
        this.mapGroup.position.set(0, 0, 0);
        this.mapGroup.rotation.set(0, 0, 0);
        this.mapGroup.scale.set(1, 1, 1);

        // 创建一个简单的平面作为地图背景，但使用透明材质
        const planeGeometry = new THREE.PlaneGeometry(100, 80);
        const planeMaterial = new THREE.MeshBasicMaterial({
          color: 0x000000, // 黑色
          transparent: true,
          opacity: 0, // 完全透明
          side: THREE.DoubleSide
        });

        const plane = new THREE.Mesh(planeGeometry, planeMaterial);
        plane.position.z = -1;
        this.mapGroup.add(plane);

        // 添加一些简单的城市标记
        this.addSimpleCityMarker('北京', -30, 20, true);
        this.addSimpleCityMarker('上海', 30, 0);
        this.addSimpleCityMarker('广州', 10, -30);
        this.addSimpleCityMarker('成都', -20, -10);

        // 更新调试信息
        this.mapFeaturesCount = 5; // 1个背景 + 4个城市

        // 确保地图居中显示
        this.centerMap();

        // 缩放地图以适应容器
        this.scaleMap();

        console.log('Simple map rendered with', this.mapGroup.children.length, 'objects');
      } catch (error) {
        console.error('Error rendering simple map:', error);
      }
    },

    // 添加简单的城市标记
    addSimpleCityMarker(name, x, y, isHighlighted = false) {
      // 创建城市标记几何体
      const geometry = new THREE.SphereGeometry(3, 32, 32);

      // 创建材质
      const material = new THREE.MeshPhongMaterial({
        color: isHighlighted ? 0x3498db : 0xe74c3c,
        emissive: isHighlighted ? 0x1a5fb4 : 0xc0392b,
        emissiveIntensity: 0.5,
        shininess: 80
      });

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(x, y, 2);
      mesh.name = name;

      this.mapGroup.add(mesh);

      // 添加城市名称标签
      const canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 128;
      const context = canvas.getContext('2d');
      context.fillStyle = '#ffffff';
      context.font = 'Bold 20px Arial';
      context.fillText(name, 10, 64);

      const texture = new THREE.CanvasTexture(canvas);
      const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(spriteMaterial);
      sprite.position.set(x, y, 5);
      sprite.scale.set(10, 5, 1);

      this.mapGroup.add(sprite);

      return mesh;
    },

    // 添加一个简单的测试方法，确认 Three.js 能正常工作
    testThreeJS() {
      console.log('Testing Three.js rendering...');

      try {
        // 创建一个简单的场景
        const scene = new THREE.Scene();
        scene.background = null; // 透明背景

        // 创建相机
        const camera = new THREE.PerspectiveCamera(
          75,
          window.innerWidth / window.innerHeight,
          0.1,
          1000
        );
        camera.position.z = 5;

        // 创建渲染器
        const renderer = new THREE.WebGLRenderer({
          canvas: document.createElement('canvas'),
          antialias: true
        });
        renderer.setSize(200, 200);

        // 添加到页面
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.bottom = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        container.style.border = '2px solid white';
        container.appendChild(renderer.domElement);
        document.body.appendChild(container);

        // 创建一个立方体
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ color: 0x3498db });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);

        // 动画循环
        const animate = () => {
          requestAnimationFrame(animate);

          cube.rotation.x += 0.01;
          cube.rotation.y += 0.01;

          renderer.render(scene, camera);
        };

        animate();

        console.log('Three.js test successful');
        return true;
      } catch (error) {
        console.error('Three.js test failed:', error);
        return false;
      }
    },

    // 添加专门处理真实地图数据的方法
    loadRealMapData() {
      console.log('Starting to load real map data...');

      try {
        // 清除现有地图
        while (this.mapGroup.children.length > 0) {
          const child = this.mapGroup.children[0];
          this.mapGroup.remove(child);
          if (child.geometry) child.geometry.dispose();
          if (child.material) child.material.dispose();
        }

        console.log(`Processing ${mapData.features.length} map features...`);

        // 创建省份网格
        let successCount = 0;

        // 添加一个基础平面作为背景，但使用透明材质
        const planeGeometry = new THREE.PlaneGeometry(100, 100);
        const planeMaterial = new THREE.MeshBasicMaterial({
          color: 0x000000, // 黑色
          transparent: true,
          opacity: 0, // 完全透明
          side: THREE.DoubleSide
        });

        const plane = new THREE.Mesh(planeGeometry, planeMaterial);
        plane.position.z = -1;
        plane.name = 'Map Background';
        this.mapGroup.add(plane);

        // 确保地图组在正确的位置
        this.mapGroup.position.set(0, 0, 0); // 重置位置
        this.mapGroup.rotation.set(0, 0, 0); // 重置旋转
        this.mapGroup.scale.set(1, 1, 1); // 重置缩放

        // 处理每个省份
        mapData.features.forEach((feature, index) => {
          try {
            // 检查特征是否有效
            if (!feature || !feature.properties || !feature.properties.name) {
              console.warn(`Invalid feature at index ${index}`);
              return;
            }

            const provinceName = feature.properties.name;
            console.log(`Creating province: ${provinceName}`);

            // 创建省份网格
            const mesh = this.createProvinceMeshSimplified(feature);
            if (mesh) {
              this.mapGroup.add(mesh);
              successCount++;
              console.log(`Successfully added province: ${provinceName}`);
            } else {
              console.warn(`Failed to create mesh for province: ${provinceName}`);
            }
          } catch (error) {
            console.error(`Error processing feature at index ${index}:`, error);
          }
        });

        // 更新调试信息
        this.mapFeaturesCount = successCount + 1; // +1 for the background plane

        console.log(`Successfully added ${successCount} out of ${mapData.features.length} provinces to map`);

        if (successCount === 0) {
          console.error('No provinces were successfully added, using simple map');
          this.renderSimpleMap();
          return;
        }

        // 确保地图居中显示
        this.centerMap();

        // 缩放地图以适应容器
        this.scaleMap();

        // 调整相机位置以确保能看到地图
        this.camera.position.set(0, 0, 150);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();

        // 强制渲染一次，确保地图可见
        if (this.renderer && this.scene && this.camera) {
          console.log('Forcing initial render');
          this.renderer.render(this.scene, this.camera);
        }

        console.log('Real map data loading complete');
      } catch (error) {
        console.error('Error loading real map data:', error);
        this.renderSimpleMap();
      }
    },

    // 添加简化版的省份网格创建方法
    createProvinceMeshSimplified(feature) {
      try {
        const provinceName = feature.properties.name;

        // 创建一个简单的形状代表省份
        let shape;

        if (feature.geometry.type === 'Polygon' && feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
          // 使用第一个多边形的第一个环
          const coordinates = feature.geometry.coordinates[0];
          if (coordinates && coordinates.length >= 3) {
            shape = new THREE.Shape();

            // 移动到第一个点，注意这里我们翻转y坐标以匹配Three.js坐标系
            shape.moveTo(coordinates[0][0], -coordinates[0][1]);

            // 连接其余的点，但限制点的数量以提高性能
            const step = Math.max(1, Math.floor(coordinates.length / 100)); // 减少点的数量
            for (let i = step; i < coordinates.length; i += step) {
              if (Array.isArray(coordinates[i]) && coordinates[i].length >= 2) {
                shape.lineTo(coordinates[i][0], -coordinates[i][1]); // 注意这里翻转y坐标
              }
            }

            // 闭合形状
            shape.closePath();
          }
        } else if (feature.geometry.type === 'MultiPolygon' && feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
          // 只使用第一个多边形
          const coordinates = feature.geometry.coordinates[0][0];
          if (coordinates && coordinates.length >= 3) {
            shape = new THREE.Shape();

            // 移动到第一个点，注意这里我们翻转y坐标以匹配Three.js坐标系
            shape.moveTo(coordinates[0][0], -coordinates[0][1]);

            // 连接其余的点，但限制点的数量以提高性能
            const step = Math.max(1, Math.floor(coordinates.length / 100)); // 减少点的数量
            for (let i = step; i < coordinates.length; i += step) {
              if (Array.isArray(coordinates[i]) && coordinates[i].length >= 2) {
                shape.lineTo(coordinates[i][0], -coordinates[i][1]); // 注意这里翻转y坐标
              }
            }

            // 闭合形状
            shape.closePath();
          }
        }

        if (!shape) {
          console.warn(`Could not create shape for province: ${provinceName}`);
          return null;
        }

        // 创建几何体，使用较小的深度值
        const geometry = new THREE.ExtrudeGeometry(shape, {
          depth: 1, // 减小深度，使地图更平滑
          bevelEnabled: true,
          bevelThickness: 0.2,
          bevelSize: 0.2,
          bevelSegments: 3
        });

        // 创建材质
        const isHighlighted = this.highlightedProvinces.includes(provinceName);
        const material = new THREE.MeshPhongMaterial({
          color: isHighlighted ? 0x3498db : 0x1a5fb4,
          transparent: true,
          opacity: isHighlighted ? 0.9 : 0.7,
          side: THREE.DoubleSide,
          emissive: isHighlighted ? 0x1a5fb4 : 0x0f2643,
          emissiveIntensity: 0.3,
          shininess: 50
        });

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);
        mesh.name = provinceName;
        mesh.userData = { ...feature.properties };

        return mesh;
      } catch (error) {
        console.error(`Error creating simplified mesh for province: ${feature.properties?.name || 'unknown'}`, error);
        return null;
      }
    },

    // 重置相机位置
    resetCamera() {
      if (this.camera) {
        this.camera.position.set(0, 0, 150);
        this.camera.lookAt(0, 0, 0);
        if (this.controls) {
          this.controls.update();
        }
        console.log('Camera reset to position:', this.camera.position);
      }
    },

    // 切换线框模式
    toggleWireframe() {
      if (this.mapGroup) {
        this.mapGroup.traverse(child => {
          if (child.isMesh && child.material) {
            child.material.wireframe = !child.material.wireframe;
          }
        });
        console.log('Wireframe mode toggled');
      }
    },

    // 放大
    zoomIn() {
      if (this.camera) {
        this.camera.position.z *= 0.8;
        console.log('Zoomed in, camera z:', this.camera.position.z);
      }
    },

    // 缩小
    zoomOut() {
      if (this.camera) {
        this.camera.position.z *= 1.2;
        console.log('Zoomed out, camera z:', this.camera.position.z);
      }
    },

    // 修改 createSimplifiedChinaMap 方法，使用更精确的中国地图数据
    createSimplifiedChinaMap() {
      console.log('Creating accurate China map...');

      // 清除现有地图
      while (this.mapGroup.children.length > 0) {
        const child = this.mapGroup.children[0];
        this.mapGroup.remove(child);
        if (child.geometry) child.geometry.dispose();
        if (child.material) child.material.dispose();
      }

      // 重置地图组位置
      this.mapGroup.position.set(0, 0, 0);
      this.mapGroup.rotation.set(0, 0, 0);
      this.mapGroup.scale.set(1, 1, 1);

      // 使用更精确的中国地图数据
      // 这里我们使用一个更详细的中国地图轮廓
      const provinces = [
        {
          name: '北京',
          points: [
            [116.4551, 39.9], [116.7373, 40.1], [117.0605, 40.0],
            [117.2559, 39.8], [116.9238, 39.7], [116.5918, 39.6],
            [116.2402, 39.7], [116.0156, 39.9], [116.4551, 39.9]
          ],
          height: 3,
          color: 0x3498db
        },
        {
          name: '天津',
          points: [
            [117.2559, 39.8], [117.5195, 39.7], [117.7051, 39.5],
            [117.5195, 39.3], [117.2559, 39.2], [116.9824, 39.3],
            [116.8359, 39.5], [116.9238, 39.7], [117.2559, 39.8]
          ],
          height: 3,
          color: 0x2ecc71
        },
        {
          name: '河北',
          points: [
            [114.9609, 42.0], [115.4004, 41.8], [116.0156, 42.0],
            [116.5918, 41.9], [117.0703, 41.6], [117.6855, 41.7],
            [118.0371, 41.5], [118.3887, 41.0], [118.8281, 40.7],
            [119.0625, 40.2], [119.3262, 39.9], [119.5312, 39.5],
            [119.0625, 39.2], [118.8281, 39.0], [118.3887, 38.8],
            [117.9492, 38.6], [117.5195, 38.4], [117.0703, 38.4],
            [116.6406, 38.4], [116.2988, 38.4], [116.0156, 38.1],
            [115.6641, 38.2], [115.3125, 38.4], [114.9609, 38.4],
            [114.6094, 38.4], [114.2578, 38.1], [113.9062, 38.0],
            [113.5547, 38.2], [113.2031, 38.4], [113.0273, 38.7],
            [113.5547, 39.0], [113.9062, 39.3], [114.2578, 39.6],
            [114.4336, 39.9], [114.2578, 40.2], [114.4336, 40.5],
            [114.6094, 40.7], [114.9609, 41.0], [114.7852, 41.3],
            [114.6094, 41.5], [114.7852, 41.7], [114.9609, 42.0]
          ],
          height: 2,
          color: 0xe74c3c
        },
        {
          name: '上海',
          points: [
            [121.3770, 31.8], [121.6406, 31.6], [121.9043, 31.4],
            [121.7285, 31.1], [121.5527, 30.9], [121.2891, 31.0],
            [121.1133, 31.2], [121.2012, 31.5], [121.3770, 31.8]
          ],
          height: 3,
          color: 0xf39c12
        },
        {
          name: '广州',
          points: [
            [113.1152, 23.5], [113.3789, 23.4], [113.6426, 23.3],
            [113.8184, 23.0], [113.5547, 22.8], [113.2910, 22.7],
            [113.0273, 22.9], [112.9395, 23.2], [113.1152, 23.5]
          ],
          height: 3,
          color: 0x9b59b6
        },
        {
          name: '成都',
          points: [
            [103.9160, 30.9], [104.1797, 30.7], [104.3555, 30.5],
            [104.1797, 30.2], [103.9160, 30.0], [103.6523, 30.2],
            [103.4766, 30.5], [103.6523, 30.7], [103.9160, 30.9]
          ],
          height: 3,
          color: 0x1abc9c
        }
      ];

      // 创建一个组来包含所有省份
      const chinaGroup = new THREE.Group();
      chinaGroup.name = '中国地图';

      // 为每个省份创建3D模型
      provinces.forEach(province => {
        // 创建省份形状
        const shape = new THREE.Shape();

        // 移动到第一个点
        shape.moveTo(province.points[0][0] - 100, -(province.points[0][1] - 30));

        // 连接其余的点
        for (let i = 1; i < province.points.length; i++) {
          shape.lineTo(province.points[i][0] - 100, -(province.points[i][1] - 30));
        }

        // 创建挤出几何体
        const extrudeSettings = {
          depth: province.height,
          bevelEnabled: true,
          bevelThickness: 0.2,
          bevelSize: 0.2,
          bevelSegments: 3
        };

        const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);

        // 创建材质 - 使用渐变和发光效果
        const material = new THREE.MeshPhongMaterial({
          color: province.color,
          transparent: true,
          opacity: 0.8,
          side: THREE.DoubleSide,
          emissive: new THREE.Color(province.color).multiplyScalar(0.3),
          emissiveIntensity: 0.5,
          shininess: 80,
          specular: 0x111111
        });

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);
        mesh.name = province.name;
        mesh.userData = { type: 'province', name: province.name };

        // 添加到中国地图组
        chinaGroup.add(mesh);

        // 添加边框线
        const edges = new THREE.EdgesGeometry(geometry);
        const lineMaterial = new THREE.LineBasicMaterial({
          color: 0xffffff,
          transparent: true,
          opacity: 0.3,
          linewidth: 1
        });
        const line = new THREE.LineSegments(edges, lineMaterial);
        mesh.add(line);
      });

      // 添加中国地图组到场景
      this.mapGroup.add(chinaGroup);

      // 添加主要城市标记
      this.addCityMarkers();

      // 更新调试信息
      this.mapFeaturesCount = this.mapGroup.children.length;

      // 确保地图居中显示
      this.centerMap();

      // 缩放地图以适应容器
      this.scaleMap();

      // 调整相机位置
      this.camera.position.set(0, 30, 100);
      this.camera.lookAt(0, 0, 0);
      this.controls.update();

      // 强制渲染
      if (this.renderer && this.scene && this.camera) {
        console.log('Forcing initial render');
        this.renderer.render(this.scene, this.camera);
      }

      console.log('Accurate China map created with', this.mapFeaturesCount, 'objects');
    },

    // 添加城市标记
    addCityMarkers() {
      const cities = [
        { name: '北京', x: 16.4, y: -9.9, isCapital: true },
        { name: '上海', x: 21.5, y: -31.2 },
        { name: '广州', x: 13.3, y: -23.1 },
        { name: '成都', x: 4.1, y: -30.7 },
        { name: '西安', x: 8.9, y: -34.3 },
        { name: '哈尔滨', x: 26.6, y: -45.8 }
      ];

      cities.forEach(city => {
        // 创建城市标记几何体
        const geometry = city.isCapital
          ? new THREE.SphereGeometry(1.2, 32, 32)
          : new THREE.CylinderGeometry(0.8, 0.8, 2, 16);

        // 创建材质
        const material = new THREE.MeshPhongMaterial({
          color: city.isCapital ? 0xf1c40f : 0x3498db,
          emissive: city.isCapital ? 0xf39c12 : 0x2980b9,
          emissiveIntensity: 0.5,
          shininess: 80,
          specular: 0x111111
        });

        // 创建网格
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(city.x, city.isCapital ? 5 : 3, city.y);
        mesh.name = city.name;
        mesh.userData = { type: 'city', name: city.name };

        this.mapGroup.add(mesh);

        // 添加城市名称标签
        const textSprite = this.createTextSprite(city.name, {
          fontsize: 24,
          fontface: 'Arial',
          borderColor: { r: 0, g: 0, b: 0, a: 0 }, // 透明边框
          backgroundColor: { r: 0, g: 0, b: 0, a: 0 }, // 透明背景
          textColor: { r: 255, g: 255, b: 255, a: 1.0 } // 白色文字
        });

        textSprite.position.set(city.x, city.isCapital ? 8 : 6, city.y);
        this.mapGroup.add(textSprite);

        // 添加光环效果
        if (city.isCapital) {
          const ringGeometry = new THREE.RingGeometry(1.5, 2.5, 32);
          const ringMaterial = new THREE.MeshBasicMaterial({
            color: 0xf1c40f,
            transparent: true,
            opacity: 0.5,
            side: THREE.DoubleSide
          });

          const ring = new THREE.Mesh(ringGeometry, ringMaterial);
          ring.position.set(city.x, 0.1, city.y);
          ring.rotation.x = -Math.PI / 2;
          this.mapGroup.add(ring);

          // 添加动画
          this.animateRing(ring);
        }
      });
    },

    // 创建文本精灵
    createTextSprite(text, parameters) {
      if (parameters === undefined) parameters = {};

      const fontface = parameters.fontface || 'Arial';
      const fontsize = parameters.fontsize || 18;
      const borderThickness = parameters.borderThickness || 4;
      const borderColor = parameters.borderColor || { r: 0, g: 0, b: 0, a: 0 }; // 透明边框
      const backgroundColor = parameters.backgroundColor || { r: 0, g: 0, b: 0, a: 0 }; // 透明背景
      const textColor = parameters.textColor || { r: 255, g: 255, b: 255, a: 1.0 }; // 白色文字

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      context.font = `Bold ${fontsize}px ${fontface}`;

      // 获取文本宽度
      const metrics = context.measureText(text);
      const textWidth = metrics.width;

      // 设置画布尺寸
      canvas.width = textWidth + borderThickness * 2;
      canvas.height = fontsize * 1.4 + borderThickness * 2;

      // 重新设置字体，因为画布尺寸改变后会重置
      context.font = `Bold ${fontsize}px ${fontface}`;

      // 背景色 - 设置为透明
      context.fillStyle = `rgba(${backgroundColor.r}, ${backgroundColor.g}, ${backgroundColor.b}, ${backgroundColor.a})`;
      context.strokeStyle = `rgba(${borderColor.r}, ${borderColor.g}, ${borderColor.b}, ${borderColor.a})`;
      context.lineWidth = borderThickness;

      // 不绘制背景矩形，只绘制文本
      // this.roundRect(context, borderThickness / 2, borderThickness / 2,
      //              textWidth + borderThickness, fontsize * 1.4 + borderThickness, 6);

      // 文本颜色
      context.fillStyle = `rgba(${textColor.r}, ${textColor.g}, ${textColor.b}, ${textColor.a})`;
      context.fillText(text, borderThickness, fontsize + borderThickness);

      // 创建纹理
      const texture = new THREE.Texture(canvas);
      texture.needsUpdate = true;

      // 创建精灵材质
      const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(spriteMaterial);

      // 调整精灵大小
      sprite.scale.set(10, 5, 1);

      return sprite;
    },

    // 绘制圆角矩形
    roundRect(ctx, x, y, w, h, r) {
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.lineTo(x + w - r, y);
      ctx.quadraticCurveTo(x + w, y, x + w, y + r);
      ctx.lineTo(x + w, y + h - r);
      ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h);
      ctx.lineTo(x + r, y + h);
      ctx.quadraticCurveTo(x, y + h, x, y + h - r);
      ctx.lineTo(x, y + r);
      ctx.quadraticCurveTo(x, y, x + r, y);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },

    // 动画环
    animateRing(ring) {
      if (!this.animatedRings) this.animatedRings = [];
      this.animatedRings.push(ring);
    }
  },
  created() {
    // 检查 mapData 是否正确导入
    console.log('Checking map data:', mapData);

    if (!mapData || !mapData.features) {
      console.warn('Map data not properly imported, creating sample data');

      // 创建示例地图数据
      window.mapData = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            properties: { name: '北京' },
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [10, 10], [20, 10], [20, 20], [10, 20], [10, 10]
              ]]
            }
          },
          {
            type: 'Feature',
            properties: { name: '上海' },
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [30, 10], [40, 10], [40, 20], [30, 20], [30, 10]
              ]]
            }
          },
          {
            type: 'Feature',
            properties: { name: '广州' },
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [20, 30], [30, 30], [30, 40], [20, 40], [20, 30]
              ]]
            }
          }
        ]
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }

  .map-header {
    position: absolute;
    top: 10px; /* 减小顶部距离 */
    left: 0;
    width: 100%;
    z-index: 10;
    text-align: center;

    .title {
      font-size: 28px;
      color: #fff;
      margin: 0;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      font-weight: 600;
      letter-spacing: 2px;
    }

    .time {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      margin-top: 5px;
    }
  }

  .china-map {
    position: relative;
    width: 100%;
    height: 80vh; /* 增加高度，使地图更大 */
    min-height: 600px; /* 增加最小高度 */
    overflow: hidden;
    border-radius: 15px;
    margin: 0 auto; /* 水平居中 */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    background-color: #041527;
    display: flex;
    align-items: center;
    justify-content: center;

    .map-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      z-index: 1;
    }

    .project-marker {
      position: absolute;
      transform: translate(-50%, -50%);
      cursor: pointer;
      z-index: 10;

      .marker-pulse {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: rgba(52, 152, 219, 0.4);
        animation: pulse 2s infinite;
        transform: translate(-50%, -50%);
      }

      .marker-point {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #3498db;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 15px rgba(52, 152, 219, 0.9);
      }

      .marker-label {
        position: absolute;
        top: 10px;
        left: 0;
        white-space: nowrap;
        color: #fff;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
        transform: translateX(-50%);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        background: none; /* 移除蓝色背景 */
      }

      &:hover, &.active {
        .marker-pulse {
          background: rgba(46, 204, 113, 0.5);
        }

        .marker-point {
          background: #2ecc71;
          box-shadow: 0 0 20px rgba(46, 204, 113, 1);
        }

        .marker-label {
          opacity: 1;
          background: none; /* 确保悬停状态下也没有背景 */
        }
      }
    }
  }

  .project-popup {
    position: absolute;
    width: 280px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 100;
    border: 1px solid rgba(255, 255, 255, 0.2);

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h3 {
        margin: 0;
        color: #fff;
        font-size: 16px;
      }

      .project-status {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 10px;

        &.in-progress {
          background: rgba(46, 204, 113, 0.2);
          color: #2ecc71;
        }

        &.completed {
          background: rgba(52, 152, 219, 0.2);
          color: #3498db;
        }

        &.preparing {
          background: rgba(241, 196, 15, 0.2);
          color: #f1c40f;
        }

        &.paused {
          background: rgba(231, 76, 60, 0.2);
          color: #e74c3c;
        }
      }
    }

    .popup-content {
      .popup-info {
        margin-bottom: 15px;

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          color: rgba(255, 255, 255, 0.8);
          font-size: 13px;

          i {
            margin-right: 8px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .popup-actions {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .statistics-panel {
    position: absolute;
    bottom: 20px; /* 调整底部距离 */
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;

    .stat-item {
      text-align: center;
      margin: 0 20px;

      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -1000;
  }
}

/* 添加调试样式 */
.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 1000;
}

.china-map {
  position: relative;
  width: 100%;
  height: 80vh; /* 增加高度，使地图更大 */
  min-height: 600px; /* 增加最小高度 */
  overflow: hidden;
  border-radius: 15px;
  margin: 0 auto; /* 水平居中 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background-color: #041527; /* 与场景背景色一致的深蓝色 */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  display: flex;
  align-items: center;
  justify-content: center;

  &::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }

  .map-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important; /* 强制宽度100% */
    height: 100% !important; /* 强制高度100% */
    z-index: 1;
  }
}

/* 添加调试按钮样式 */
.debug-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  z-index: 1000;
}

.debug-controls button {
  margin: 0 5px;
  padding: 5px 10px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.debug-controls button:hover {
  background: #2980b9;
}
</style>