import * as THREE from 'three'

export function initCamera(dom, options = {}) {
  // 创建相机
  const camera = new THREE.PerspectiveCamera(
    70,
    dom.clientWidth / dom.clientHeight,
    0.1,
    1000
  )

  // 设置相机位置
  camera.position.set(0, 0, 20)
  camera.lookAt(0, 0, 0)

  // 创建相机辅助线 - 仅在显式要求时创建
  let cameraHelper = null;
  if (options.showHelper) {
    cameraHelper = new THREE.CameraHelper(camera);
  }

  return { camera, cameraHelper }
} 