import request from '@/utils/axios'

// 获取项目环境监控数据
export function getProjectEnvironmentData(projectId) {
  console.log('Calling environment API for project:', projectId);

  // 确保使用小写的方法名
  return request({
    path: `/api/system/gateway/project/${projectId}/environment/sensors`,
    method: 'get'
  }).then(response => {
    console.log('Environment API response received:', response);

    // 打印原始响应
    console.log('Original API response data:', JSON.stringify(response.data, null, 2));

    // 如果服务器数据为空或发生错误，返回测试数据
    // if (!response || response.code !== 0 || !response.data || (Array.isArray(response.data) && response.data.length === 0)) {
    //   console.warn('No environment data returned from server, using test data');
    //   response = {
    //     code: 0,
    //     data: getTestEnvironmentData(),
    //     message: 'Test data generated'
    //   };
    // }

    // 打印最终返回的数据
    console.log('Final environment data returned:', JSON.stringify(response.data, null, 2));

    return response;
  }).catch(error => {
    console.error('Environment API error:', error);
    // 不再返回测试数据，直接抛出错误
    throw error;
  });
}

