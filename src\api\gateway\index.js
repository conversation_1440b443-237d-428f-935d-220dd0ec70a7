import request from '@/utils/axios'

// 创建新网关
export function createGateway(data) {
  return request({
    path: '/api/system/gateway/gateways',
    method: 'post',
    data
  })
}

// 获取网关列表
export function getGatewayList(params) {
  return request({
    path: '/api/system/gateway/gateways',
    method: 'get',
    params
  })
}

// 获取网关详情
export function getGatewayDetail(id) {
  return request({
    path: `/api/system/gateway/gateways/${id}`,
    method: 'get'
  })
}

// 更新网关
export function updateGateway(data) {
  return request({
    path: `/api/system/gateway/gateways/${data.id}`,
    method: 'put',
    data
  })
}

// 删除网关
export function deleteGateway(id) {
  return request({
    path: `/api/system/gateway/gateways/${id}`,
    method: 'delete'
  })
}

// 批量删除网关
export function batchDeleteGateways(ids) {
  return request({
    path: '/api/system/gateway/gateways/batch',
    method: 'delete',
    data: { ids }
  })
}

// 更新网关状态
export function updateGatewayStatus(id, status) {
  return request({
    path: `/api/system/gateway/gateways/${id}/status`,
    method: 'patch',
    data: { status }
  })
} 