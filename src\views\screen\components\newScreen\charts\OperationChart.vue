<template>
  <div class="chart-container">
    <canvas ref="chart"></canvas>
  </div>
</template>

<script>
import Chart from 'chart.js'

export default {
  name: 'Operation<PERSON><PERSON>',
  data() {
    return {
      chart: null,
      chartData: {
        labels: ['1日', '2日', '3日', '4日', '5日', '6日', '7日', '8日', '9日', '10日'],
        datasets: [
          {
            label: '工作时长 (小时)',
            data: [8, 9, 10, 8, 7, 9, 10, 8, 9, 10],
            backgroundColor: 'rgba(0, 168, 255, 0.2)',
            borderColor: 'rgba(0, 168, 255, 1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
          },
          {
            label: '吊装重量 (百吨)',
            data: [6.5, 7.2, 8.0, 7.5, 6.8, 7.0, 8.5, 9.0, 8.2, 7.8],
            backgroundColor: 'rgba(0, 255, 157, 0.2)',
            borderColor: 'rgba(0, 255, 157, 1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
          }
        ]
      },
      chartOptions: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              fontColor: 'rgba(255, 255, 255, 0.7)'
            },
            gridLines: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }],
          xAxes: [{
            ticks: {
              fontColor: 'rgba(255, 255, 255, 0.7)'
            },
            gridLines: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }]
        },
        legend: {
          labels: {
            fontColor: 'rgba(255, 255, 255, 0.7)'
          }
        }
      }
    }
  },
  mounted() {
    this.createChart();
    this.startDataSimulation();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
    }
  },
  methods: {
    createChart() {
      const ctx = this.$refs.chart.getContext('2d');
      this.chart = new Chart(ctx, {
        type: 'line',
        data: this.chartData,
        options: this.chartOptions
      });
    },
    startDataSimulation() {
      // 每30秒更新一次数据
      this.simulationInterval = setInterval(() => {
        // 更新工作时长数据
        this.chartData.datasets[0].data = this.chartData.datasets[0].data.map(value => {
          // 在原值基础上增加或减少最多1个小时
          const change = Math.random() * 2 - 1;
          // 确保值在6-12之间
          return Math.min(12, Math.max(6, value + change));
        });
        
        // 更新吊装重量数据
        this.chartData.datasets[1].data = this.chartData.datasets[1].data.map(value => {
          // 在原值基础上增加或减少最多0.5个百吨
          const change = Math.random() * 1 - 0.5;
          // 确保值在5-10之间
          return Math.min(10, Math.max(5, value + change));
        });
        
        // 更新图表
        this.chart.update();
      }, 30000);
    }
  }
}
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
