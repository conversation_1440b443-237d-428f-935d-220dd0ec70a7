<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
  },
  beforeDestroy() {
    // 移除全屏变化事件监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
  },
  methods: {
    handleFullscreenChange() {
      // 检查是否处于全屏模式
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      
      // 根据全屏状态设置类
      if (isFullscreen) {
        document.body.classList.add('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.add('fullscreen-mode', 'fullscreen-active');
        
        // 确保侧边栏隐藏
        const sidebarEl = document.querySelector('.sidebar-container');
        if (sidebarEl) {
          sidebarEl.style.display = 'none';
          sidebarEl.style.visibility = 'hidden';
          sidebarEl.style.opacity = '0';
          sidebarEl.style.width = '0';
          sidebarEl.style.height = '0';
          sidebarEl.style.overflow = 'hidden';
          sidebarEl.style.position = 'absolute';
          sidebarEl.style.left = '-9999px';
          sidebarEl.style.top = '-9999px';
          sidebarEl.style.zIndex = '-9999';
          sidebarEl.style.transform = 'translateX(-100%)';
          sidebarEl.style.pointerEvents = 'none';
          sidebarEl.style.backgroundColor = 'transparent';
          
          // 隐藏所有子元素
          const sidebarChildren = sidebarEl.querySelectorAll('*');
          sidebarChildren.forEach(child => {
            child.style.display = 'none';
            child.style.visibility = 'hidden';
            child.style.opacity = '0';
          });
        }
        
        // 确保主容器占满整个屏幕
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
          mainContainer.style.marginLeft = '0';
          mainContainer.style.width = '100%';
          mainContainer.style.maxWidth = '100vw';
        }
      } else {
        document.body.classList.remove('fullscreen-mode', 'fullscreen-active');
        document.documentElement.classList.remove('fullscreen-mode', 'fullscreen-active');
        
        // 恢复侧边栏显示
        const sidebarEl = document.querySelector('.sidebar-container');
        if (sidebarEl) {
          sidebarEl.style.display = '';
          sidebarEl.style.visibility = '';
          sidebarEl.style.opacity = '';
          sidebarEl.style.width = '';
          sidebarEl.style.height = '';
          sidebarEl.style.overflow = '';
          sidebarEl.style.position = '';
          sidebarEl.style.left = '';
          sidebarEl.style.top = '';
          sidebarEl.style.zIndex = '';
          sidebarEl.style.transform = '';
          sidebarEl.style.pointerEvents = '';
          sidebarEl.style.backgroundColor = '';
          
          // 恢复所有子元素
          const sidebarChildren = sidebarEl.querySelectorAll('*');
          sidebarChildren.forEach(child => {
            child.style.display = '';
            child.style.visibility = '';
            child.style.opacity = '';
          });
        }
        
        // 恢复主容器样式
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
          mainContainer.style.marginLeft = '';
          mainContainer.style.width = '';
          mainContainer.style.maxWidth = '';
        }
      }
      
      // 触发窗口大小变化事件，确保布局正确应用
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);
    }
  }
}
</script>

<style>
.box {
  padding: 20px;
}

/* 全局样式重置 - 确保页面之间不互相影响 */
.default-theme {
  background-color: #fff;
  color: #303133;
}

.default-theme .el-table {
  background-color: #fff;
  color: #606266;
}

.default-theme .el-table th {
  background-color: #f5f7fa;
  color: #606266;
}

.default-theme .el-table td {
  color: #606266;
}

.default-theme .el-button {
  background-color: #fff;
  color: #606266;
}

.default-theme .el-button--primary {
  background-color: #409eff;
  color: #fff;
}

.default-theme .el-button--danger {
  background-color: #f56c6c;
  color: #fff;
}

/* 全屏模式下隐藏侧边栏 */
:fullscreen .sidebar-container,
:-webkit-full-screen .sidebar-container,
:-moz-full-screen .sidebar-container,
:-ms-fullscreen .sidebar-container {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  z-index: -9999 !important;
  background-color: transparent !important;
  transform: translateX(-100%) !important;
  pointer-events: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 全屏模式下隐藏侧边栏的所有子元素 */
:fullscreen .sidebar-container *,
:-webkit-full-screen .sidebar-container *,
:-moz-full-screen .sidebar-container *,
:-ms-fullscreen .sidebar-container * {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 全屏模式下调整主容器 */
:fullscreen .main-container,
:-webkit-full-screen .main-container,
:-moz-full-screen .main-container,
:-ms-fullscreen .main-container {
  margin-left: 0 !important;
  width: 100% !important;
  max-width: 100vw !important;
}

/* 全屏模式下的背景颜色 */
:fullscreen,
:-webkit-full-screen,
:-moz-full-screen,
:-ms-fullscreen {
  background-color: #001528 !important;
}

/* 全屏模式下隐藏滚动条 */
:fullscreen {
  overflow: hidden !important;
}
:-webkit-full-screen {
  overflow: hidden !important;
}
:-moz-full-screen {
  overflow: hidden !important;
}
:-ms-fullscreen {
  overflow: hidden !important;
}
</style>
