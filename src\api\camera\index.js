import request from "@/utils/axios";

// 创建新摄像头
export function createCamera(data) {
  return request({
    path: "/api/system/camera/cameras",
    method: 'post',
    data
  })
}

// 获取摄像头列表
export function getCameraList(params) {
  return request({
    path: "/api/system/camera/cameras",
    method: 'get',
    params
  })
}

// 获取摄像头详情
export function getCameraDetail(id) {
  return request({
    path: `/api/system/camera/cameras/${id}`,
    method: 'get'
  })
}

// 更新摄像头
export function updateCamera(data) {
  return request({
    path: `/api/system/camera/cameras/${data.id}`,
    method: 'put',
    data
  })
}

// 删除摄像头
export function deleteCamera(id) {
  return request({
    path: `/api/system/camera/cameras/${id}`,
    method: 'delete'
  })
}

// 批量删除摄像头
export function batchDeleteCameras(ids) {
  return request({
    path: "/api/system/camera/cameras/batch",
    method: 'delete',
    data: { ids }
  })
}

// 更新摄像头状态
export function updateCameraStatus(id, status) {
  return request({
    path: `/api/system/camera/cameras/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 获取项目摄像头列表
export function getProjectCameras(projectId) {
  return request({
    path: `/api/system/camera/project/${projectId}/cameras`,
    method: 'get'
  })
} 