/* 漂浮模块样式 */

/* 左侧模块漂浮样式 */
.left-column .widget {
  background-color: rgba(0, 30, 60, 0.5); /* 半透明背景 */
  backdrop-filter: blur(8px); /* 背景模糊效果 */
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 168, 255, 0.4);
  margin-bottom: 10px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10; /* 确保在模型上方 */
  transform: translateZ(20px); /* 3D效果 */
}

.left-column .widget:hover {
  box-shadow: 0 0 25px rgba(0, 168, 255, 0.5);
  transform: translateY(-2px) translateZ(30px);
}

/* 右侧模块漂浮样式 */
.right-column .widget {
  background-color: rgba(0, 30, 60, 0.5); /* 半透明背景 */
  backdrop-filter: blur(8px); /* 背景模糊效果 */
  border: 1px solid rgba(0, 168, 255, 0.3);
  border-radius: 4px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 168, 255, 0.4);
  margin-bottom: 10px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10; /* 确保在模型上方 */
  transform: translateZ(20px); /* 3D效果 */
}

.right-column .widget:hover {
  box-shadow: 0 0 25px rgba(0, 168, 255, 0.5);
  transform: translateY(-2px) translateZ(30px);
}

/* 确保模块内容可见 */
.widget-content {
  position: relative;
  z-index: 11;
  height: calc(100% - 30px);
  overflow: hidden;
}

/* 添加漂浮动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px) translateZ(20px);
  }
  50% {
    transform: translateY(-8px) translateZ(25px);
  }
  100% {
    transform: translateY(0px) translateZ(20px);
  }
}

/* 为左侧第一个模块添加漂浮动画 */
.left-column .widget:nth-child(1) {
  animation: float 8s ease-in-out infinite;
}

/* 为左侧第二个模块添加漂浮动画，延迟1秒 */
.left-column .widget:nth-child(2) {
  animation: float 9s ease-in-out 1s infinite;
}

/* 为左侧第三个模块添加漂浮动画，延迟2秒 */
.left-column .widget:nth-child(3) {
  animation: float 10s ease-in-out 2s infinite;
}

/* 为右侧第一个模块添加漂浮动画，延迟0.5秒 */
.right-column .widget:nth-child(1) {
  animation: float 9s ease-in-out 0.5s infinite;
}

/* 为右侧第二个模块添加漂浮动画，延迟1.5秒 */
.right-column .widget:nth-child(2) {
  animation: float 8s ease-in-out 1.5s infinite;
}

/* 为右侧第三个模块添加漂浮动画，延迟2.5秒 */
.right-column .widget:nth-child(3) {
  animation: float 10s ease-in-out 2.5s infinite;
}

/* 确保模块在悬停时停止动画 */
.left-column .widget:hover,
.right-column .widget:hover {
  animation-play-state: paused;
}

/* 模块头部样式 */
.widget-header {
  background-color: rgba(0, 40, 80, 0.7);
  border-bottom: 1px solid rgba(0, 168, 255, 0.4);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  height: 30px;
}

/* 模块标题样式 */
.widget-title {
  color: #00a8ff;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.widget-title i {
  margin-right: 5px;
  font-size: 14px;
}

/* 模块状态样式 */
.widget-status {
  color: rgba(255, 255, 255, 0.7);
  font-size: 10px;
  display: flex;
  align-items: center;
}

.widget-status i {
  margin-right: 3px;
  font-size: 8px;
  color: #00ff9d;
}
