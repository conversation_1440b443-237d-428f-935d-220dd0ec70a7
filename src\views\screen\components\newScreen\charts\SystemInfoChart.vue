<template>
  <div class="chart-container">
    <canvas ref="chart"></canvas>
    <div v-if="isLoading" class="loading-indicator">
      <i class="fas fa-circle-notch fa-spin"></i>
      <span>加载数据中...</span>
    </div>
    <div v-if="hasError" class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      <span>{{ errorMessage || '获取数据失败' }}</span>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js'
import { getProjectForceData } from '@/api/system/force'

export default {
  name: 'SystemInfoChart',
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      systemInfo: [
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸2', value: 0 },
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸2', value: 0 },
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸3', value: 0 },
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸3', value: 0 },
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸4', value: 0 },
        { device: '左边设备', sensor: '左侧穿轴式拉力传感器后轴油缸4', value: 0 },
        { device: '左边设备', sensor: '右侧穿轴式拉力传感器后轴油缸5', value: 0 },
        { device: '左边设备', sensor: '右侧穿轴式拉力传感器后轴油缸5', value: 0 },
        { device: '左边设备', sensor: '右侧穿轴式拉力传感器后轴油缸6', value: 0 }
      ],
      updateInterval: null,
      apiRefreshTimer: null,
      chartColors: [
        'rgba(0, 168, 255, 0.7)',   // 蓝色
        'rgba(0, 255, 157, 0.7)',   // 绿色
        'rgba(255, 170, 0, 0.7)',   // 橙色
        'rgba(255, 85, 85, 0.7)',   // 红色
        'rgba(170, 0, 255, 0.7)',   // 紫色
        'rgba(255, 255, 85, 0.7)'   // 黄色
      ],
      isLoading: false,
      hasError: false,
      errorMessage: '',
      resizeObserver: null
    }
  },
  mounted() {
    this.createChart();

    // 如果有项目ID，获取力传感器数据
    if (this.projectId) {
      this.fetchForceData();
      this.startApiDataRefresh();
    } else {
      // 如果没有项目ID，使用模拟数据
      this.startDataSimulation();
    }

    // 监听容器大小变化，优化响应式表现
    this.setupResizeObserver();

    // 添加窗口大小变化事件监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    if (this.apiRefreshTimer) {
      clearInterval(this.apiRefreshTimer);
    }

    // 清理大小变化监听
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    // 移除窗口大小变化事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    // 监听项目ID变化
    projectId: {
      handler(newVal) {
        // 清除现有定时器
        if (this.updateInterval) {
          clearInterval(this.updateInterval);
          this.updateInterval = null;
        }
        if (this.apiRefreshTimer) {
          clearInterval(this.apiRefreshTimer);
          this.apiRefreshTimer = null;
        }

        // 如果有新的项目ID，获取力传感器数据
        if (newVal) {
          this.fetchForceData();
          this.startApiDataRefresh();
        } else {
          // 如果没有项目ID，使用模拟数据
          this.startDataSimulation();
        }
      },
      immediate: false
    }
  },
  methods: {
    setupResizeObserver() {
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(this.handleContainerResize);
        this.resizeObserver.observe(this.$el);
      }
    },

    handleContainerResize() {
      if (this.chart) {
        this.chart.resize();
        this.chart.update();
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
        this.chart.update();
      }
    },

    createChart() {
      const ctx = this.$refs.chart.getContext('2d');

      // 准备图表数据
      const labels = this.systemInfo.map(item => this.shortenSensorName(item.sensor));
      const data = this.systemInfo.map(item => item.value);

      // 准备背景颜色
      const backgroundColors = this.systemInfo.map((_, index) => {
        return this.chartColors[index % this.chartColors.length];
      });

      // 准备边框颜色
      const borderColors = backgroundColors.map(color => {
        return color.replace('0.7', '1');
      });

      // 创建图表
      this.chart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: '压力值',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: borderColors,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              left: 5,
              right: 5,
              top: 25, // 增加顶部填充以容纳数值标签
              bottom: 10
            }
          },
          scales: {
            yAxes: [{
              ticks: {
                beginAtZero: true,
                fontColor: 'rgba(255, 255, 255, 0.7)',
                fontSize: 9,
                maxTicksLimit: 5,
                callback: function(value) {
                  return value + ' KN';
                }
              },
              gridLines: {
                color: 'rgba(255, 255, 255, 0.1)',
                drawBorder: false,
                zeroLineColor: 'rgba(255, 255, 255, 0.2)'
              }
            }],
            xAxes: [{
              ticks: {
                fontColor: 'rgba(255, 255, 255, 0.7)',
                fontSize: 8,
                maxRotation: 45,
                minRotation: 45,
                callback: function(value) {
                  // 进一步缩短显示的传感器名称
                  if (value.length > 8) {
                    return value.substr(0, 8) + '...';
                  }
                  return value;
                }
              },
              gridLines: {
                display: false
              },
              barPercentage: 0.7,
              categoryPercentage: 0.7
            }]
          },
          legend: {
            display: false
          },
          title: {
            display: false
          },
          animation: {
            duration: 1000,
            easing: 'easeOutQuart'
          },
          tooltips: {
            enabled: true,
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 30, 60, 0.9)',
            titleFontSize: 10,
            bodyFontSize: 9,
            callbacks: {
              title: function(tooltipItems, data) {
                // 显示完整的传感器名称
                const index = tooltipItems[0].index;
                return data.labels[index];
              },
              label: (tooltipItem, data) => {
                // 显示压力值和单位
                const index = tooltipItem.index;
                const unit = this.systemInfo[index] ? this.systemInfo[index].unit : 'KN';
                return '压力值: ' + tooltipItem.value + ' ' + unit;
              },
              footer: (tooltipItems, data) => {
                // 显示设备和网关名称
                const index = tooltipItems[0].index;
                const deviceName = this.systemInfo[index] ? this.systemInfo[index].device : '未知设备';
                return ['设备: ' + deviceName];
              }
            }
          },
          // 自定义绘制函数，用于在柱状图上方显示数值
          animation: {
            onComplete: function() {
              const chartInstance = this.chart;
              const ctx = chartInstance.ctx;
              ctx.font = 'bold 9px Arial';
              ctx.textAlign = 'center';
              ctx.textBaseline = 'bottom';
              ctx.fillStyle = 'white';

              this.data.datasets.forEach(function(dataset, i) {
                const meta = chartInstance.controller.getDatasetMeta(i);
                meta.data.forEach(function(bar, index) {
                  const data = dataset.data[index];
                  if (data > 0) { // 只显示大于0的值
                    ctx.fillText(data + ' KN', bar._model.x, bar._model.y - 5);
                    // 添加发光效果
                    ctx.shadowColor = 'rgba(0, 168, 255, 0.5)';
                    ctx.shadowBlur = 3;
                    ctx.fillText(data + ' KN', bar._model.x, bar._model.y - 5);
                    ctx.shadowBlur = 0;
                  }
                });
              });
            }
          }
        }
      });
    },

    // 缩短传感器名称
    shortenSensorName(name) {
      // 如果名称含有"传感器"，去掉它
      let shortName = name.replace(/传感器/g, '');

      // 提取关键部分
      if (shortName.includes('油缸')) {
        // 例如 "右侧穿轴式拉力传感器后轴油缸5" => "后轴油缸5"
        const match = shortName.match(/([前中后][轴桥].{0,4}油缸\d+)/);
        if (match) {
          return match[1];
        }
      }

      // 如果名称过长，截断它
      if (shortName.length > 10) {
        return shortName.substring(0, 10) + '...';
      }

      return shortName;
    },

    // 从API获取力传感器数据
    fetchForceData() {
      if (!this.projectId) {
        console.warn('No project ID provided for force data');
        return;
      }

      this.isLoading = true;
      this.hasError = false;

      console.log('获取项目力传感器数据，项目ID:', this.projectId);

      getProjectForceData(this.projectId)
        .then(response => {
          if (response && response.code === 0 && response.data) {
            console.log('获取到力传感器数据:', response.data);
            this.processForceData(response.data);
          } else {
            console.warn('力传感器数据API返回错误或空数据:', response);
            this.hasError = true;
            this.errorMessage = '获取力传感器数据失败';
          }
        })
        .catch(error => {
          console.error('获取力传感器数据失败:', error);
          this.hasError = true;
          this.errorMessage = '获取力传感器数据失败: ' + (error.message || '未知错误');
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    // 处理API返回的力传感器数据
    processForceData(data) {
      if (!Array.isArray(data) || data.length === 0) {
        console.warn('力传感器数据格式不正确或为空');
        return;
      }

      // 创建新的系统信息数组
      const newSystemInfo = [];

      // 直接处理每个传感器数据
      data.forEach(sensor => {
        // 设备名称
        const deviceName = sensor.device_name || '未知设备';
        // 网关名称
        const gatewayName = sensor.gateway_name || '未知网关';
        // 传感器名称 (优先使用传感器描述，如果没有则使用传感器名称)
        const sensorName = sensor.sensor_description || sensor.sensor_name || '未知传感器';
        // 传感器单位
        const sensorUnit = sensor.unit || 'KN';

        // 传感器值，如果无效则默认为0
        let sensorValue = 0;
        try {
          sensorValue = parseFloat(sensor.value);
          if (isNaN(sensorValue)) sensorValue = 0;
        } catch (e) {
          console.warn(`传感器 ${sensorName} 的值无效:`, sensor.value);
        }

        // 添加到系统信息数组
        newSystemInfo.push({
          device: deviceName,
          gateway: gatewayName,
          sensor: sensorName,
          value: sensorValue,
          unit: sensorUnit,
          updated_at: sensor.data_updated_at
        });
      });

      // 如果没有传感器数据，保留原有数据
      if (newSystemInfo.length === 0) {
        console.warn('没有有效的传感器数据，保留原有数据');
        return;
      }

      // 更新系统信息
      this.systemInfo = newSystemInfo;

      // 更新图表
      this.updateChartWithNewData();

      console.log('力传感器数据处理完成，传感器数量:', this.systemInfo.length);
    },

    // 更新图表数据
    updateChartWithNewData() {
      if (!this.chart) return;

      // 更新图表标签和数据
      this.chart.data.labels = this.systemInfo.map(item => this.shortenSensorName(item.sensor));
      this.chart.data.datasets[0].data = this.systemInfo.map(item => item.value);

      // 更新图表标题
      const unit = this.systemInfo.length > 0 && this.systemInfo[0].unit ? this.systemInfo[0].unit : 'KN';
      this.chart.data.datasets[0].label = `压力值 (${unit})`;

      // 更新图表颜色
      const backgroundColors = this.systemInfo.map((item, index) => {
        const colorIndex = index % this.chartColors.length;
        let color = this.chartColors[colorIndex];

        // 根据值的大小调整颜色亮度
        if (item.value > 10) {
          color = color.replace('0.7', '0.9'); // 如果值大于10，使颜色更亮
        } else if (item.value === 0) {
          color = color.replace('0.7', '0.4'); // 如果值为0，使颜色更暗
        }

        return color;
      });

      // 更新边框颜色
      const borderColors = backgroundColors.map(color => {
        return color.replace(/[0-9].[0-9]/, '1');
      });

      // 更新图表样式
      this.chart.data.datasets[0].backgroundColor = backgroundColors;
      this.chart.data.datasets[0].borderColor = borderColors;

      // 更新图表
      this.chart.update();

      console.log('图表更新完成，显示 ' + this.systemInfo.length + ' 个传感器数据');
    },

    // 启动API数据刷新定时器
    startApiDataRefresh() {
      // 每30秒刷新一次API数据
      this.apiRefreshTimer = setInterval(() => {
        this.fetchForceData();
      }, 30000); // 30秒
    },

    // 启动模拟数据更新
    startDataSimulation() {
      // 每5秒更新一次数据
      this.updateInterval = setInterval(() => {
        let hasUpdates = false;

        // 随机更新一些值
        this.systemInfo.forEach((item, index) => {
          // 随机决定是否更新这个值
          if (Math.random() > 0.7) {
            const randomValue = Math.floor(Math.random() * 20);
            item.value = randomValue;

            // 更新图表数据
            if (this.chart) {
              this.chart.data.datasets[0].data[index] = randomValue;

              // 根据值的大小调整颜色亮度
              const colorIndex = index % this.chartColors.length;
              let color = this.chartColors[colorIndex];

              // 如果值大于10，使颜色更亮
              if (randomValue > 10) {
                color = color.replace('0.7', '0.9');
              } else if (randomValue === 0) {
                color = color.replace('0.7', '0.4'); // 如果值为0，使颜色更暗
              }

              this.chart.data.datasets[0].backgroundColor[index] = color;
              this.chart.data.datasets[0].borderColor[index] = color.replace(/[0-9].[0-9]/, '1');
            }

            hasUpdates = true;
          }
        });

        // 只有在有更新时才刷新图表
        if (hasUpdates && this.chart) {
          this.chart.update();
        }
      }, 3000); // 缩短更新间隔，使动画效果更明显
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px; /* 增加最小高度以确保图表完全展示 */
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent !important;
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 30, 60, 0.05) !important;
  color: #7fdbff;
  z-index: 10;
  border-radius: 4px;
  backdrop-filter: blur(1px);
}

.loading-indicator i {
  font-size: 2rem;
  margin-bottom: 10px;
  animation: spin 2s infinite linear;
  text-shadow: 0 0 10px rgba(0, 168, 255, 0.3) !important;
}

.loading-indicator span {
  font-size: 0.9rem;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.3) !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 30, 60, 0.05) !important;
  color: #ff5555;
  z-index: 10;
  border-radius: 4px;
  backdrop-filter: blur(1px);
}

.error-indicator i {
  font-size: 2rem;
  margin-bottom: 10px;
  animation: pulse 2s infinite;
  text-shadow: 0 0 10px rgba(255, 85, 85, 0.3) !important;
}

.error-indicator span {
  font-size: 0.9rem;
  text-align: center;
  max-width: 80%;
  margin-bottom: 10px;
  text-shadow: 0 0 5px rgba(255, 85, 85, 0.3) !important;
}

.retry-button {
  background-color: rgba(0, 168, 255, 0.08) !important;
  border: 1px solid rgba(0, 168, 255, 0.15) !important;
  color: #00a8ff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.retry-button:hover {
  background-color: rgba(0, 168, 255, 0.15) !important;
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.15) !important;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

.error-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(60, 0, 0, 0.05) !important;
  color: rgba(255, 150, 150, 0.9);
  z-index: 10;
  backdrop-filter: blur(1px);
}

.error-message i {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: rgba(255, 100, 100, 0.9);
}

.error-message span {
  font-size: 0.8rem;
  text-align: center;
  max-width: 80%;
}

@media (max-width: 1366px) {
  .chart-container {
    min-height: 180px; /* 增加小屏幕下的最小高度以确保图表完全展示 */
    max-height: 180px; /* 增加小屏幕下的最大高度 */
  }
}
</style>
