<template>
  <div class="center-column" style="pointer-events: auto !important; touch-action: auto !important; z-index: 50 !important;">
    <!-- 添加光晕效果 -->
    <div class="tech-glow glow1"></div>
    <div class="tech-glow glow2"></div>
    <div class="tech-glow glow3"></div>

    <div class="model-viewer-panel" style="pointer-events: auto !important; touch-action: auto !important; z-index: 50 !important;">
      <!-- <div class="model-header">
        <span><i class="fas fa-cube"></i> 项目设备3D模型展示</span>
        <span class="widget-status"><i class="fas fa-circle"></i> 实时</span>
      </div> -->
      <div class="model-content" style="pointer-events: auto !important; touch-action: auto !important; z-index: 50 !important;">
        <!-- 3D模型将通过JavaScript加载到这里 -->
        <div id="center-model-container" ref="modelContainer" class="model-container" style="pointer-events: auto !important; touch-action: auto !important; z-index: 50 !important; background-color: transparent !important;"></div>

        <!-- 备用图片 - 当模型加载失败时显示 -->
        <div class="fallback-image" v-if="showFallbackImage" style="z-index: 400 !important; pointer-events: auto !important; visibility: visible !important; background-color: transparent !important;">
          <div class="fallback-text">
            <i class="fas fa-exclamation-triangle"></i>
            <p>模型加载失败，请检查模型文件</p>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div class="model-loading" v-show="isLoading" style="z-index: 400 !important; pointer-events: auto !important; visibility: visible !important; background-color: rgba(0, 26, 51, 0.3) !important;">
          <div class="loading-spinner"></div>
          <div class="loading-text">{{ loadingText }}</div>
        </div>
      </div>
    </div>

    <!-- 这里不需要控制按钮，通过代码配置控制显示模式 -->
  </div>
</template>

<script>
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

export default {
  name: 'CenterColumn',
  data() {
    return {
      isLoading: true,
      loadingText: '加载中...',
      modelStatus: '加载中',
      modelStatusColor: '#ffaa00',
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      model: null,
      loadingProgress: 0,
      showFallbackImage: false, // 控制是否显示备用图片
      animationFrameId: null, // 存储动画帧ID，用于取消动画
      forceUseGif: false, // 强制使用GIF图片而不是3D模型
      autoRotateTimer: null // 存储自动旋转恢复的定时器ID
    }
  },
  mounted() {
    // 初始化3D模型查看器
    this.initModelViewer();

    // 添加全局事件监听器，用于调试
    document.addEventListener('mousedown', this.handleGlobalMouseDown);
    document.addEventListener('mousemove', this.handleGlobalMouseMove);

    // 检查 DRACO 解码器是否存在
    this.checkDracoDecoders();

    // 延迟一段时间后检查模型容器的样式
    setTimeout(() => {
      if (this.$refs.modelContainer) {
        console.log('模型容器样式检查:', {
          pointerEvents: this.$refs.modelContainer.style.pointerEvents,
          touchAction: this.$refs.modelContainer.style.touchAction,
          zIndex: this.$refs.modelContainer.style.zIndex
        });
      }

      const canvas = document.querySelector('#center-model-container canvas');
      if (canvas) {
        console.log('Canvas样式检查:', {
          pointerEvents: canvas.style.pointerEvents,
          touchAction: canvas.style.touchAction,
          zIndex: canvas.style.zIndex
        });
      }
    }, 2000);
  },
  beforeDestroy() {
    // 清理资源
    this.cleanupModelViewer();

    // 移除全局事件监听器
    document.removeEventListener('mousedown', this.handleGlobalMouseDown);
    document.removeEventListener('mousemove', this.handleGlobalMouseMove);

    // 清除自动旋转定时器
    if (this.autoRotateTimer) {
      clearTimeout(this.autoRotateTimer);
      this.autoRotateTimer = null;
    }
  },

  // 添加全局事件处理方法
  handleGlobalMouseDown(event) {
    console.log('全局鼠标按下事件', event.target, event.clientX, event.clientY);
  },

  handleGlobalMouseMove(event) {
    if (event.buttons > 0) {
      console.log('全局鼠标移动事件', event.target, event.clientX, event.clientY);
    }
  },
  methods: {
    // 防抖函数 - 避免频繁触发resize事件
    debounce(func, wait) {
      let timeout;
      return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(context, args);
        }, wait);
      };
    },

    initModelViewer() {
      // 检查是否强制使用GIF图片
      if (this.forceUseGif) {
        console.log('强制使用GIF图片模式');
        this.showFallbackImage = true;
        this.isLoading = false;
        this.modelStatus = '已加载 (GIF图片模式)';
        this.modelStatusColor = '#00a8ff';
        return;
      }

      // 确保THREE.js已加载
      if (typeof THREE === 'undefined') {
        console.error('THREE.js未加载，无法初始化模型查看器');
        this.loadingText = 'THREE.js未加载，无法显示模型';
        this.modelStatus = '加载失败';
        this.modelStatusColor = '#ff5555';
        this.isLoading = false;
        // 显示备用图片
        this.showFallbackImage = true;
        return;
      }

      const modelContainer = this.$refs.modelContainer;
      if (!modelContainer) {
        console.error('模型容器不存在，无法初始化模型查看器');
        // 显示备用图片
        this.showFallbackImage = true;
        this.isLoading = false;
        return;
      }

      // 创建场景
      this.scene = new THREE.Scene();

      // 设置完全透明背景，以便能够看到仪表盘背景
      this.scene.background = null;

      // 确保没有坐标轴辅助工具
      // 不添加坐标轴辅助线，保持场景干净
      // const axesHelper = new THREE.AxesHelper(100);
      // this.scene.add(axesHelper);

      // 添加环境光 - 增强环境光强度
      const ambientLight = new THREE.AmbientLight(0xffffff, 1.0);
      this.scene.add(ambientLight);

      // 添加方向光 - 增强方向光强度
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
      directionalLight.position.set(1, 1, 1);
      directionalLight.castShadow = true; // 启用阴影投射

      // 配置阴影属性
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      directionalLight.shadow.camera.near = 0.5;
      directionalLight.shadow.camera.far = 500;
      directionalLight.shadow.bias = -0.0001;

      this.scene.add(directionalLight);

      // 添加点光源 - 调整光源颜色和强度
      const pointLight1 = new THREE.PointLight(0xffffff, 1.5, 100);
      pointLight1.position.set(5, 5, 5);
      pointLight1.castShadow = true; // 启用阴影投射
      this.scene.add(pointLight1);

      const pointLight2 = new THREE.PointLight(0xffffff, 1.5, 100);
      pointLight2.position.set(-5, 5, -5);
      pointLight2.castShadow = true; // 启用阴影投射
      this.scene.add(pointLight2);

      // 添加第三个点光源，从下方照亮模型
      const pointLight3 = new THREE.PointLight(0xffffff, 0.8, 100);
      pointLight3.position.set(0, -5, 0);
      this.scene.add(pointLight3);

      // 添加半球光 - 提供更自然的环境光照
      const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.6);
      this.scene.add(hemisphereLight);

      // 创建相机 - 使用窗口宽高比，确保模型始终占据整个屏幕
      this.camera = new THREE.PerspectiveCamera(
        55, // 增大视场角，使模型看起来更完整
        window.innerWidth / window.innerHeight,
        0.1,
        2000
      );

      // 调整相机位置，使模型更好地显示在屏幕中
      // 增加相机距离，确保能够看到整个模型
      this.camera.position.set(60, 40, 60); // 进一步增加相机距离，以适应更大的模型

      // 设置相机的上方向为Y轴正方向
      this.camera.up.set(0, 1, 0);

      // 设置相机看向原点
      this.camera.lookAt(0, 0, 0);

      // 设置相机的远近平面，确保模型完全可见
      this.camera.near = 0.1;
      this.camera.far = 2000;
      this.camera.updateProjectionMatrix();

      // 创建渲染器 - 确保抗锯齿和高质量渲染
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true, // 启用透明背景
        precision: 'highp', // 高精度渲染
        logarithmicDepthBuffer: true, // 启用对数深度缓冲，提高深度精度
        premultipliedAlpha: false // 确保透明度正确处理
      });

      // 获取视口尺寸
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      console.log(`初始化渲染器视口尺寸: ${viewportWidth}x${viewportHeight}`);

      // 设置渲染器尺寸为视口尺寸
      this.renderer.setSize(viewportWidth, viewportHeight);
      this.renderer.setPixelRatio(window.devicePixelRatio); // 使用设备像素比例
      this.renderer.outputColorSpace = THREE.SRGBColorSpace; // 使用SRGB颜色空间提高颜色准确性
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap; // 使用PCF软阴影提高阴影质量
      this.renderer.setClearColor(0x000000, 0); // 设置完全透明的背景（alpha值为0）
      this.renderer.autoClear = true; // 确保每帧自动清除
      modelContainer.appendChild(this.renderer.domElement);

      // 确保渲染器的canvas元素可以接收鼠标事件并铺满整个屏幕
      const rendererElement = this.renderer.domElement;
      rendererElement.style.pointerEvents = 'auto !important';
      rendererElement.style.position = 'fixed';
      rendererElement.style.top = '0';
      rendererElement.style.left = '0';
      rendererElement.style.right = '0';
      rendererElement.style.bottom = '0';
      rendererElement.style.margin = '0';
      rendererElement.style.padding = '0';
      rendererElement.style.width = '100vw';
      rendererElement.style.height = '100vh';
      rendererElement.style.zIndex = '50'; // 降低z-index，确保不会覆盖左右功能模块
      rendererElement.style.overflow = 'visible';
      rendererElement.style.transformOrigin = '0 0';
      rendererElement.style.transform = 'scale(1)';
      rendererElement.style.touchAction = 'auto !important';
      rendererElement.style.backgroundColor = 'transparent !important'; // 确保canvas背景透明

      // 确保模型容器也铺满整个屏幕
      if (this.$refs.modelContainer) {
        const modelContainer = this.$refs.modelContainer;
        modelContainer.style.width = '100vw';
        modelContainer.style.height = '100vh';
        modelContainer.style.position = 'fixed';
        modelContainer.style.top = '0';
        modelContainer.style.left = '0';
        modelContainer.style.right = '0';
        modelContainer.style.bottom = '0';
        modelContainer.style.margin = '0';
        modelContainer.style.padding = '0';
        modelContainer.style.zIndex = '50'; // 降低z-index，确保不会覆盖左右功能模块
        modelContainer.style.overflow = 'visible';
        modelContainer.style.transformOrigin = '0 0';
        modelContainer.style.transform = 'scale(1)';
      }

      // 添加轨道控制 - 优化控制参数
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.05;
      this.controls.autoRotate = true;
      this.controls.autoRotateSpeed = 0.3; // 减慢自动旋转速度
      this.controls.enableZoom = true;
      this.controls.enablePan = true;
      this.controls.minDistance = 25; // 增加最小距离，防止过度缩放（从15增加到25）
      this.controls.maxDistance = 100; // 增加最大距离，防止缩放过远（从60增加到100）
      this.controls.target.set(0, 0, 0); // 设置控制目标为原点

      // 确保控制器能够正确接收鼠标事件
      this.controls.enabled = true;
      this.controls.update();

      // 添加鼠标事件监听器
      this.renderer.domElement.addEventListener('mousedown', this.handleMouseDown);
      this.renderer.domElement.addEventListener('mouseup', this.handleMouseUp);
      this.renderer.domElement.addEventListener('mousemove', this.handleMouseMove);

      // 确保模型容器可以接收鼠标事件
      if (this.$refs.modelContainer) {
        this.$refs.modelContainer.style.pointerEvents = 'auto !important';
        this.$refs.modelContainer.style.touchAction = 'auto !important';
        this.$refs.modelContainer.style.zIndex = '50';

        // 添加更多的事件监听器以便调试
        this.$refs.modelContainer.addEventListener('click', this.handleContainerClick);
        this.$refs.modelContainer.addEventListener('mousedown', this.handleContainerMouseDown);
        this.$refs.modelContainer.addEventListener('mousemove', this.handleContainerMouseMove);
      }

      // 加载模型
      this.loadModel();
      // this.loadRemoteModel();

      // 开始动画循环
      this.animate();

      // 处理窗口大小变化 - 使用防抖函数避免频繁触发
      this.debouncedResize = this.debounce(this.onWindowResize, 300);
      window.addEventListener('resize', this.debouncedResize);
    },
    loadModel() {
      // 确保加载指示器可见
      this.isLoading = true;
      this.loadingText = '加载中...';

      // 创建 GLTF 加载器
      const loader = new GLTFLoader();

      // 使用全局 DRACO 加载器（如果存在）或创建新的加载器
      if (window.dracoLoader) {
        console.log('使用全局 DRACO 加载器');
        loader.setDRACOLoader(window.dracoLoader);
      } else {
        console.log('创建新的 DRACO 加载器');
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
        dracoLoader.setDecoderConfig({ type: 'js' });
        loader.setDRACOLoader(dracoLoader);
      }
      loader.setCrossOrigin('anonymous'); // 设置跨域请求头

      // 设置加载超时
      const loadingTimeout = setTimeout(() => {
        console.log('本地模型加载超时，尝试使用远程模型');
        this.loadRemoteModel();
      }, 10000); // 10秒超时

      // 优先尝试加载本地模型
      console.log('开始加载本地模型: /models/xsqt.glb');
      loader.load(
        '/models/xsqt.glb', // 本地模型路径
        // 成功回调
        (gltf) => {
          // 清除加载超时计时器
          clearTimeout(loadingTimeout);
          console.log('本地模型加载成功');
          this.processLoadedModel(gltf.scene, '本地模型');
        },
        // 进度回调
        (xhr) => {
          const percent = Math.floor((xhr.loaded / xhr.total) * 100);
          this.loadingText = `加载本地模型... ${percent}%`;
          this.loadingProgress = percent;
          console.log(`本地模型加载进度: ${percent}%`);

          // 确保加载指示器可见
          this.isLoading = true;
        },
        // 错误回调
        (error) => {
          // 清除加载超时计时器
          clearTimeout(loadingTimeout);
          console.error('本地模型加载失败:', error);
          this.loadingText = '本地模型加载失败，尝试远程模型...';

          // 尝试加载远程模型
          this.loadRemoteModel();
        }
      );
    },

    // 加载远程模型
    loadRemoteModel() {
      // console.log('尝试加载远程模型: https://glb.bjbrtt.com/xsqt.glb');
      console.log('尝试加载远程模型: https://brtt-bucket.oss-cn-beijing.aliyuncs.com/xsqt.glb');
      this.loadingText = '加载远程模型...';

      // 创建新的 GLTF 加载器
      const remoteLoader = new GLTFLoader();

      // 使用全局 DRACO 加载器（如果存在）或创建新的加载器
      if (window.dracoLoader) {
        console.log('远程加载：使用全局 DRACO 加载器');
        remoteLoader.setDRACOLoader(window.dracoLoader);
      } else {
        console.log('远程加载：创建新的 DRACO 加载器');
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
        dracoLoader.setDecoderConfig({ type: 'js' });
        remoteLoader.setDRACOLoader(dracoLoader);
      }
      remoteLoader.setCrossOrigin('anonymous'); // 设置跨域请求头

      // 设置远程加载超时
      const remoteTimeout = setTimeout(() => {
        console.log('远程模型加载超时，使用备用图片');
        this.createFallbackModel();
      }, 60000); // 60秒超时

      // 尝试加载远程模型 - 确保使用HTTPS协议
      const modelUrl = 'https://brtt-bucket.oss-cn-beijing.aliyuncs.com/xsqt.glb'; // 使用HTTPS协议
      console.log('加载远程模型URL:', modelUrl);
      remoteLoader.load(
        modelUrl, // 远程模型地址
        // 成功回调
        (gltf) => {
          clearTimeout(remoteTimeout);
          console.log('远程模型加载成功');
          this.processLoadedModel(gltf.scene, '远程模型');
        },
        // 进度回调
        (xhr) => {
          const percent = Math.floor((xhr.loaded / xhr.total) * 100);
          this.loadingText = `加载远程模型... ${percent}%`;
          this.loadingProgress = percent;
        },
        // 错误回调
        (error) => {
          clearTimeout(remoteTimeout);
          console.error('远程模型加载失败:', error);
          this.loadingText = '所有模型加载失败，使用备用图片';
          this.createFallbackModel();
        }
      );
    },


    // 处理加载的模型
    processLoadedModel(modelScene, sourceType = '未知来源') {
      this.model = modelScene;

      // 计算模型的包围盒，以便正确缩放
      const box = new THREE.Box3().setFromObject(this.model);
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);

      // 根据模型实际尺寸计算合适的缩放比例
      const scaleFactor = 60 / maxDim;
      console.log(`${sourceType}模型尺寸: ${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}, 缩放比例: ${scaleFactor.toFixed(3)}`);

      // 调整模型大小
      this.model.scale.set(scaleFactor, scaleFactor, scaleFactor);

      // 居中模型
      const center = box.getCenter(new THREE.Vector3());
      this.model.position.set(-center.x * scaleFactor, -center.y * scaleFactor, -center.z * scaleFactor);

      // 微调模型位置，使其在视口中更好地显示
      this.model.position.y -= 8;
      this.model.position.z -= 5;
      this.model.rotation.y = Math.PI / 4;

      // 确保模型在视口中心
      this.camera.lookAt(0, 0, 0);

      // 计算最终模型尺寸
      box.setFromObject(this.model);
      const finalSize = box.getSize(new THREE.Vector3());
      console.log(`最终${sourceType}模型尺寸: ${finalSize.x.toFixed(2)} x ${finalSize.y.toFixed(2)} x ${finalSize.z.toFixed(2)}`);

      // 添加到场景
      this.scene.add(this.model);

      // 更新状态
      this.isLoading = false;
      this.modelStatus = `已加载 (${sourceType})`;
      this.modelStatusColor = '#00ff9d';

      // 为模型材质添加高光效果
      this.model.traverse((child) => {
        if (child.isMesh && child.material) {
          if (!Array.isArray(child.material)) {
            this.enhanceMaterial(child.material);
          } else {
            child.material.forEach(mat => this.enhanceMaterial(mat));
          }
        }
      });

      console.log(`${sourceType}模型处理完成`);
    },

    // 使用备用图片
    createFallbackModel() {
      console.log('使用备用图片替代3D模型');

      // 隐藏3D渲染器
      if (this.renderer && this.renderer.domElement) {
        this.renderer.domElement.style.display = 'none';
      }

      // 显示备用图片
      this.showFallbackImage = true;

      // 更新状态
      this.isLoading = false;
      this.modelStatus = '已加载 (备用图片)';
      this.modelStatusColor = '#ffaa00';

      // 停止动画循环以节省资源
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
    },

    // 增强材质效果
    enhanceMaterial(material) {
      if (!material) return;

      // 增加材质的光泽度
      if (material.roughness !== undefined) {
        material.roughness = 0.3; // 降低粗糙度，增加光泽
      }

      // 增加材质的金属感
      if (material.metalness !== undefined) {
        material.metalness = 0.7; // 增加金属感
      }

      // 确保材质接收阴影
      material.receiveShadow = true;
      material.castShadow = true;

      // 增强材质的环境光遮蔽
      if (material.aoMapIntensity !== undefined) {
        material.aoMapIntensity = 1.5;
      }

      // 增强材质的法线贴图效果
      if (material.normalScale !== undefined) {
        material.normalScale.set(1.5, 1.5);
      }
    },
    animate() {
      // 存储动画帧ID，以便在需要时取消
      this.animationFrameId = requestAnimationFrame(this.animate);

      // 确保场景背景始终为透明
      if (this.scene) {
        this.scene.background = null;
      }

      // 更新控制器
      if (this.controls) {
        this.controls.update();
      }

      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.setClearColor(0x000000, 0); // 确保每帧渲染时背景都是透明的
        this.renderer.render(this.scene, this.camera);
      }
    },
    onWindowResize() {
      if (!this.$refs.modelContainer || !this.camera || !this.renderer) return;

      // 使用窗口的宽高，确保模型始终占据整个屏幕
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      console.log(`3D模型窗口大小调整: ${viewportWidth}x${viewportHeight}`);

      // 更新相机和渲染器
      this.camera.aspect = viewportWidth / viewportHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(viewportWidth, viewportHeight);

      // 确保渲染器像素比例正确
      this.renderer.setPixelRatio(window.devicePixelRatio);
 
      // 检查是否处于全屏模式 - 通过检查document.fullscreenElement
      // 注意：虽然我们检测全屏状态，但目前不需要根据状态做不同处理
      // 因为我们总是希望模型铺满整个屏幕
      const isInFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      console.log(`当前是否处于全屏模式: ${isInFullscreen}`);

      // 确保canvas元素适应容器并铺满整个屏幕
      const rendererElement = this.renderer.domElement;

      // 使用vw/vh单位而不是百分比，确保始终铺满屏幕
      rendererElement.style.width = '100vw';
      rendererElement.style.height = '100vh';
      rendererElement.style.position = 'fixed';
      rendererElement.style.top = '0';
      rendererElement.style.left = '0';
      rendererElement.style.right = '0';
      rendererElement.style.bottom = '0';
      rendererElement.style.margin = '0';
      rendererElement.style.padding = '0';
      rendererElement.style.zIndex = '50'; // 降低z-index，确保不会覆盖左右功能模块
      rendererElement.style.pointerEvents = 'auto !important';
      rendererElement.style.touchAction = 'auto !important';
      rendererElement.style.transformOrigin = '0 0';
      rendererElement.style.transform = 'scale(1)';

      // 确保模型容器也铺满整个屏幕
      const modelContainer = this.$refs.modelContainer;
      if (modelContainer) {
        modelContainer.style.width = '100vw';
        modelContainer.style.height = '100vh';
        modelContainer.style.position = 'fixed';
        modelContainer.style.top = '0';
        modelContainer.style.left = '0';
        modelContainer.style.right = '0';
        modelContainer.style.bottom = '0';
        modelContainer.style.margin = '0';
        modelContainer.style.padding = '0';
        modelContainer.style.zIndex = '50'; // 降低z-index，确保不会覆盖左右功能模块
        modelContainer.style.overflow = 'visible';
        modelContainer.style.transformOrigin = '0 0';
        modelContainer.style.transform = 'scale(1)';
      }

      // 如果模型已加载，确保它在视口中心
      if (this.model) {
        // 重新计算模型的包围盒
        const box = new THREE.Box3().setFromObject(this.model);
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);

        // 根据新的视口尺寸调整模型缩放
        const scaleFactor = 60 / maxDim; // 与初始加载时使用相同的缩放因子（从40增加到60）
        this.model.scale.set(scaleFactor, scaleFactor, scaleFactor);

        // 确保模型在视口中心
        this.camera.lookAt(0, 0, 0);

        console.log(`模型重新缩放: ${scaleFactor.toFixed(3)}`);
      }
    },
    cleanupModelViewer() {
      // 取消动画帧
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }

      // 移除事件监听器
      window.removeEventListener('resize', this.debouncedResize);

      // 移除渲染器上的事件监听器
      if (this.renderer && this.renderer.domElement) {
        this.renderer.domElement.removeEventListener('mousedown', this.handleMouseDown);
        this.renderer.domElement.removeEventListener('mouseup', this.handleMouseUp);
        this.renderer.domElement.removeEventListener('mousemove', this.handleMouseMove);
      }

      // 移除模型容器上的事件监听器
      if (this.$refs.modelContainer) {
        this.$refs.modelContainer.removeEventListener('click', this.handleContainerClick);
        this.$refs.modelContainer.removeEventListener('mousedown', this.handleContainerMouseDown);
        this.$refs.modelContainer.removeEventListener('mousemove', this.handleContainerMouseMove);
      }

      // 清理THREE.js资源
      if (this.renderer) {
        this.renderer.dispose();
        if (this.renderer.domElement && this.renderer.domElement.parentNode) {
          this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
        }
      }

      // 清理场景
      if (this.scene) {
        this.scene.traverse((object) => {
          if (object.geometry) {
            object.geometry.dispose();
          }
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else {
              object.material.dispose();
            }
          }
        });
      }

      // 清理控制器
      if (this.controls) {
        this.controls.dispose();
      }
    },
    rotateLeft() {
      if (this.model) {
        this.model.rotation.y += Math.PI / 4;
      }
    },
    rotateRight() {
      if (this.model) {
        this.model.rotation.y -= Math.PI / 4;
      }
    },
    resetModel() {
      if (this.model) {
        this.model.rotation.set(0, 0, 0);
        if (this.controls) {
          this.controls.reset();
        }
      }
    },
    showDetails() {
      alert('设备详情：\n型号：JQ-900型造桥机\n制造商：中国重工集团\n最大起重量：900吨\n最大工作半径：45米\n最大工作高度：60米\n功率：1200kW');
    },

    // 增强材质效果
    // 事件处理方法
    handleMouseDown(event) {
      console.log('模型接收到鼠标按下事件', event);
      if (this.controls) {
        this.controls.autoRotate = false;
      }
    },

    handleMouseUp(event) {
      console.log('模型接收到鼠标释放事件', event);
      if (this.controls) {
        // 清除之前的定时器（如果存在）
        if (this.autoRotateTimer) {
          clearTimeout(this.autoRotateTimer);
        }

        // 设置新的定时器，5分钟后恢复自动旋转
        this.autoRotateTimer = setTimeout(() => {
          console.log('恢复模型自动旋转');
          this.controls.autoRotate = true;
        }, 5 * 60 * 1000); // 5分钟 = 5 * 60 * 1000毫秒
      }
    },

    handleMouseMove(event) {
      // 只记录日志，不执行其他操作，避免性能问题
      if (event.buttons > 0) {
        console.log('模型接收到鼠标拖动事件', event.clientX, event.clientY);
      }
    },

    handleContainerClick(event) {
      console.log('模型容器接收到点击事件', event);
      // 点击容器也暂停自动旋转
      if (this.controls) {
        this.controls.autoRotate = false;

        // 清除之前的定时器（如果存在）
        if (this.autoRotateTimer) {
          clearTimeout(this.autoRotateTimer);
        }

        // 设置新的定时器，5分钟后恢复自动旋转
        this.autoRotateTimer = setTimeout(() => {
          console.log('恢复模型自动旋转（容器点击）');
          this.controls.autoRotate = true;
        }, 5 * 60 * 1000); // 5分钟
      }
    },

    handleContainerMouseDown(event) {
      console.log('模型容器接收到鼠标按下事件', event);
      // 鼠标按下时暂停自动旋转
      if (this.controls) {
        this.controls.autoRotate = false;
      }
    },

    handleContainerMouseMove(event) {
      if (event.buttons > 0) {
        console.log('模型容器接收到鼠标移动事件', event.clientX, event.clientY);
      }
    },

    // 检查 DRACO 解码器是否存在
    checkDracoDecoders() {
      console.log('检查 DRACO 解码器...');

      // 使用 fetch 检查 DRACO 解码器文件是否存在
      fetch('/draco/draco_decoder.js')
        .then(response => {
          if (!response.ok) {
            console.warn('DRACO 解码器文件不存在，将使用 CDN 路径');
            // 如果本地文件不存在，使用 CDN 路径
            this.useCdnDracoDecoders();
          } else {
            console.log('DRACO 解码器文件存在，使用本地路径');
          }
        })
        .catch(error => {
          console.error('检查 DRACO 解码器文件失败:', error);
          // 出错时使用 CDN 路径
          this.useCdnDracoDecoders();
        });
    },

    // 使用 CDN 路径的 DRACO 解码器
    useCdnDracoDecoders() {
      // 修改 DRACOLoader 的路径为 CDN 路径
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      dracoLoader.setDecoderConfig({ type: 'js' });

      // 保存到全局变量，供后续使用
      window.dracoLoader = dracoLoader;

      console.log('已设置 DRACO 解码器为 CDN 路径');
    },

    enhanceMaterial(material) {
      if (!material) return;

      // 提高材质亮度和对比度
      if (material.map) {
        material.map.anisotropy = 16; // 提高纹理质量
      }

      // 增强金属感和光泽度
      if (material.metalness !== undefined) {
        material.metalness = Math.min(material.metalness + 0.3, 1.0); // 增加金属感
      }

      if (material.roughness !== undefined) {
        material.roughness = Math.max(material.roughness - 0.3, 0.1); // 减少粗糙度，增加光泽
      }

      // 增加材质亮度
      if (material.color) {
        // 提高颜色亮度，使模型更加明亮
        const color = material.color;
        color.r = Math.min(color.r * 1.2, 1.0);
        color.g = Math.min(color.g * 1.2, 1.0);
        color.b = Math.min(color.b * 1.2, 1.0);
      }

      // 增加环境光反射强度
      if (material.envMapIntensity !== undefined) {
        material.envMapIntensity = 1.5; // 增强环境反射
      }

      // 确保材质接收阴影
      material.receiveShadow = true;
      material.castShadow = true;

      // 确保材质使用物理渲染
      material.needsUpdate = true;
    }
  }
}
</script>

<style scoped>
.center-column {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 全屏高度 */
  width: 100vw; /* 全屏宽度 */
  max-width: none; /* 移除最大宽度限制 */
  position: fixed !important; /* 固定定位，确保始终占据整个屏幕 */
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible;
  z-index: 50; /* 降低z-index，确保不会覆盖左右功能模块 */
  pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
  background-color: transparent !important;
  transform-origin: 0 0;
  transform: scale(1);
}

.model-container {
  width: 100vw !important;
  height: 100vh !important;
  position: fixed !important;
  overflow: visible !important;
  z-index: 50 !important; /* 降低z-index，确保不会覆盖左右功能模块 */
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  transform-origin: 0 0;
  transform: scale(1);
  background-color: transparent !important;
}

/* 大屏幕模式下的特殊处理 */
@media screen and (min-width: 1920px) {
  .center-column {
    height: 100vh !important;
    width: 100vw !important;
    overflow: visible !important;
    pointer-events: auto !important; /* 确保鼠标事件可以传递到模型 */
    position: fixed !important; /* 固定定位，确保始终占据整个屏幕 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    background-color: transparent !important;
    transform: scale(1) !important;
  }

  .model-container {
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    overflow: visible !important;
    z-index: 50 !important; /* 降低z-index，确保不会覆盖左右功能模块 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: scale(1) !important;
    background-color: transparent !important;
  }
}

/* 导入中央列样式 */
@import '../../../../assets/css/center-column.css';

/* 光晕效果样式 - 匹配纯深蓝色星空背景 */
.tech-glow {
  position: absolute;
  border-radius: 50%;
  filter: blur(15px); /* 更小的模糊半径 */
  opacity: 0.15; /* 更低的不透明度 */
  pointer-events: none;
  z-index: 0;
}

/* 移除大部分光晕，只保留几个非常微弱的光点 */
.glow1 {
  width: 50px;
  height: 50px;
  background: radial-gradient(circle, rgba(180, 220, 255, 0.2) 0%, rgba(100, 150, 220, 0.1) 40%, rgba(30, 70, 120, 0) 70%);
  top: 25%;
  left: 35%;
  animation: none; /* 移除动画 */
}

.glow2 {
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(180, 220, 255, 0.15) 0%, rgba(100, 150, 220, 0.08) 40%, rgba(30, 70, 120, 0) 70%);
  top: 65%;
  left: 70%;
  animation: none; /* 移除动画 */
}

.glow3 {
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, rgba(180, 220, 255, 0.1) 0%, rgba(100, 150, 220, 0.05) 40%, rgba(30, 70, 120, 0) 70%);
  top: 45%;
  left: 85%;
  animation: none; /* 移除动画 */
}
</style>

