<template>
  <div class="left-column" style="display: block !important; visibility: visible !important; z-index: 500 !important; pointer-events: auto !important;">
    <!-- 报警信息小部件 (从右列移动过来) -->
    <div class="widget alarm-info">
      <div class="widget-header">
        <span><i class="fas fa-exclamation-triangle"></i> 报警信息</span>
        <!-- 移除实时状态指示器 -->
        <span class="widget-status"><i class="fas fa-circle"></i> 实时</span>
      </div>
      <div class="widget-content">
        <alarm-table :projectId="projectId" />
      </div>
    </div>

    <!-- 系统信息小部件 - 保持原位 -->
    <div class="widget system-info-chart">
      <div class="widget-header">
        <span><i class="fas fa-info-circle"></i> 系统信息</span>
        <span class="widget-status"><i class="fas fa-sync-alt fa-spin"></i> 更新中</span>
      </div>
      <div class="widget-content fixed-height">
        <system-info-chart :projectId="projectId" />
      </div>
    </div>

    <!-- 姿态水平小部件 - 保持原位 -->
    <div class="widget attitude-level-chart">
      <div class="widget-header">
        <span><i class="fas fa-compass"></i> 姿态水平状态</span>
        <span class="widget-status"><i class="fas fa-circle"></i> 实时</span>
      </div>
      <div class="widget-content fixed-height">
        <div class="attitude-container">
          <!-- 传感器数据列表 -->
          <div class="sensor-data-scrollable-container">
            <div class="scroll-fade-top"></div>
            <div class="sensor-data-list" v-if="sensorData.length > 0" :class="{'auto-scroll': shouldAutoScroll}" ref="sensorList">
              <div
                v-for="(sensor, index) in displaySensorData"
                :key="index"
                class="sensor-item"
                :class="getSensorClass(sensor.value, sensor.warningThreshold, sensor.dangerThreshold)"
              >
                <div class="sensor-info">
                  <div class="sensor-name">{{sensor.name}}</div>
                  <div class="sensor-value" :class="getSensorClass(sensor.value, sensor.warningThreshold, sensor.dangerThreshold)">
                    {{sensor.value.toFixed(1)}}{{sensor.unit}}
                  </div>
                </div>
                <div class="sensor-bar-container">
                  <div
                    class="sensor-bar"
                    :style="{
                      width: getSensorBarWidth(sensor.value, sensor.maxValue),
                      backgroundColor: getSensorBarColor(sensor.value, sensor.warningThreshold, sensor.dangerThreshold)
                    }"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div class="no-data-container mini" v-if="sensorData.length === 0">
              <i class="fas fa-exclamation-circle"></i>
              <p>暂无姿态水平数据</p>
            </div>
            <div class="scroll-fade-bottom"></div>
            <div class="scroll-indicator" v-if="sensorData.length > 3">
              <i class="fas fa-chevron-down"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SystemInfoChart from './charts/SystemInfoChart.vue'
import AlarmTable from './charts/AlarmTable.vue'
import { getProjectAngleData } from '@/api/system/angle'

export default {
  name: 'LeftColumn',
  components: {
    SystemInfoChart,
    AlarmTable
  },
  props: {
    projectId: {
      type: [Number, String],
      default: ''
    },
    selectedProject: {
      type: Object,
      default: () => null
    },
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      projectProgress: 42.5,
      // 姿态水平数据
      attitudeX: 0,
      attitudeY: 0,
      attitudeUpdateTimer: null,
      angleApiRefreshTimer: null,
      attitudeData: [], // 存储从API获取的姿态数据
      // 传感器数据
      sensorData: [] // 使用空数组，只从API获取数据
    }
  },
  computed: {
    // 项目数据相关计算属性
    projectData() {
      return this.selectedProject || {};
    },
    projectName() {
      return this.projectData.name || '未知项目';
    },
    projectDescription() {
      return this.projectData.description || '暂无描述';
    },
    projectLocation() {
      return this.projectData.location || '江苏南京';
    },
    projectNumber() {
      // 使用接口中的 number 字段作为项目号
      return this.projectData.number || '未知项目号';
    },
    projectCoordinates() {
      if (this.projectData.longitude && this.projectData.latitude) {
        // 确保经纬度是数字类型
        const lat = parseFloat(this.projectData.latitude);
        const lng = parseFloat(this.projectData.longitude);

        if (!isNaN(lat) && !isNaN(lng)) {
          return `${lat.toFixed(4)}° N, ${lng.toFixed(4)}° E`;
        }
      }
      return '32.0584° N, 118.7965° E';
    },
    equipmentCount() {
      // 优先使用传入的 deviceList 计算设备数量
      if (this.deviceList && this.deviceList.length > 0) {
        return this.deviceList.length;
      }

      // 如果 deviceList 为空，尝试从 bridge_machine_models 字段获取设备数量
      if (this.projectData.bridge_machine_models) {
        try {
          // 如果是字符串，尝试解析为数组
          if (typeof this.projectData.bridge_machine_models === 'string') {
            const models = JSON.parse(this.projectData.bridge_machine_models);
            return Array.isArray(models) ? models.length : 1;
          }
          // 如果已经是数组
          else if (Array.isArray(this.projectData.bridge_machine_models)) {
            return this.projectData.bridge_machine_models.length;
          }
        } catch (e) {
          console.error('解析设备型号出错:', e);
        }
      }

      // 如果都没有数据，使用本地设备数组长度
      return this.devices.length || 3; // 默认显示3台设备
    },
    projectProgressValue() {
      try {
        if (this.projectData.progress_percentage !== undefined) {
          const progress = parseFloat(this.projectData.progress_percentage);
          return !isNaN(progress) ? progress : this.projectProgress;
        } else if (this.projectData.progress !== undefined) {
          const progress = parseFloat(this.projectData.progress) * 100;
          return !isNaN(progress) ? progress : this.projectProgress;
        }
      } catch (e) {
        console.error('解析项目进度出错:', e);
      }
      return this.projectProgress;
    },
    startDate() {
      return this.projectData.installation_date ? this.formatDate(this.projectData.installation_date) : '23/05/15';
    },
    endDate() {
      return this.projectData.estimated_completion ? this.formatDate(this.projectData.estimated_completion) : '25/12/30';
    },
    // 添加姿态水平气泡样式计算
    attitudeBubbleStyle() {
      // 限制气泡移动范围在±30px内
      const maxOffset = 30;
      // 增加倍数，使视觉效果更明显
      const xOffset = Math.min(Math.max(this.attitudeX * 8, -maxOffset), maxOffset);
      const yOffset = Math.min(Math.max(-this.attitudeY * 8, -maxOffset), maxOffset);

      // 根据倾角大小改变气泡颜色
      let bubbleColor = '#00ff9d'; // 默认绿色

      // 计算总倾角大小
      const totalTilt = Math.sqrt(this.attitudeX * this.attitudeX + this.attitudeY * this.attitudeY);

      // 根据倾角大小设置颜色
      if (totalTilt > 1.5) {
        bubbleColor = '#ff5555'; // 危险红色
      } else if (totalTilt > 0.8) {
        bubbleColor = '#ffaa00'; // 警告黄色
      }

      return {
        transform: `translate(${xOffset}px, ${yOffset}px)`,
        color: bubbleColor
      };
    },

    // 添加倾斜指针位置计算
    pointerPosition() {
      // 将值从 -3 到 3 的范围映射到 0% 到 100% 的位置
      // 限制在 -3 到 3 度范围内
      const limitedValue = Math.min(Math.max(this.attitudeX, -3), 3);
      // 将 -3 到 3 映射到 0 到 100
      const position = ((limitedValue + 3) / 6) * 100;

      return {
        left: `${position}%`
      };
    },

    // 添加水平条样式计算
    levelBarStyle() {
      // 计算水平条宽度，基于倾角值
      // 限制在 -3 到 3 度范围内
      const limitedValue = Math.min(Math.max(this.attitudeX, -3), 3);
      // 将 -3 到 3 映射到 0 到 100
      const position = ((limitedValue + 3) / 6) * 100;

      // 计算宽度和位置
      const width = Math.abs(position - 50) * 2; // 宽度是从中心点到当前位置的距离的两倍
      const left = position < 50 ? position : 50; // 如果在左边，起点是当前位置；如果在右边，起点是中心点

      return {
        width: `${width}%`,
        left: `${left}%`
      };
    },

    // 添加倾斜严重程度类
    tiltSeverityClass() {
      const absValue = Math.abs(this.attitudeX);
      if (absValue > 1.5) {
        return 'danger';
      } else if (absValue > 0.8) {
        return 'warning';
      } else {
        return 'normal';
      }
    },
    // 添加自动滚动判断
    shouldAutoScroll() {
      return this.sensorData.length > 3;
    },

    // 添加用于显示的传感器数据（包括复制的数据，用于无缝滚动）
    displaySensorData() {
      if (this.shouldAutoScroll) {
        // 复制一份数据，用于无缝滚动
        return [...this.sensorData, ...this.sensorData];
      }
      return this.sensorData;
    }
  },
  mounted() {
    // 如果有项目ID，获取姿态水平数据
    if (this.projectId) {
      this.fetchAngleData();
    } else {
      // 如果没有项目ID，生成示例数据
      this.generateSampleSensorData();
    }

    // 添加视觉效果
    this.addVisualEffects();

    // 启动姿态水平模拟更新
    this.startAttitudeSimulation();

    // 启动API数据刷新
    this.startApiDataRefresh();

    // 启动传感器数据自动滚动
    this.startSensorAutoScroll();

    // 如果没有传感器数据，生成示例数据
    this.$nextTick(() => {
      if (this.sensorData.length === 0) {
        this.generateSampleSensorData();
      }
    });
  },

  beforeDestroy() {
    // 清除所有定时器
    if (this.attitudeUpdateTimer) {
      clearInterval(this.attitudeUpdateTimer);
      this.attitudeUpdateTimer = null;
    }

    if (this.angleApiRefreshTimer) {
      clearInterval(this.angleApiRefreshTimer);
      this.angleApiRefreshTimer = null;
    }
  },

  watch: {
    // 监听项目ID变化
    projectId: {
      handler(newVal) {
        if (newVal) {
          // 获取姿态水平数据
          this.fetchAngleData();
        }
      },
      immediate: false
    }
  },
  methods: {



    // 格式化日期为 YY/MM/DD 格式
    formatDate(dateString) {
      try {
        const date = new Date(dateString);
        const year = date.getFullYear().toString().slice(-2);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}/${month}/${day}`;
      } catch (e) {
        console.error('日期格式化错误:', e);
        return dateString;
      }
    },



    addVisualEffects() {
      // 为进度条添加动画效果
      const progressFills = document.querySelectorAll('.mini-progress-fill, .timeline-fill');
      progressFills.forEach(fill => {
        // 添加脉冲动画
        setInterval(() => {
          fill.style.boxShadow = '0 0 8px rgba(0, 255, 157, 0.7)';
          setTimeout(() => {
            fill.style.boxShadow = 'none';
          }, 500);
        }, 5000);
      });
    },

    showProjectInfo() {
      const projectNumber = this.projectNumber;
      const projectName = this.projectName;
      const projectStatus = this.projectData.status === 1 ? '进行中' : '筹备中';
      const createdAt = this.projectData.created_at || '未知';
      const updatedAt = this.projectData.updated_at || '未知';

      // 显示项目基本信息
      alert(`项目编号：${projectNumber}\n项目名称：${projectName}\n项目状态：${projectStatus}\n创建时间：${createdAt}\n更新时间：${updatedAt}`);
    },

    showLocationMap() {
      const location = this.projectLocation;
      const coordinates = this.projectCoordinates;

      alert(`将打开项目位置地图：${location}\n坐标：${coordinates}`);
    },

    showProgressDetails() {
      const progress = (this.projectProgressValue || 0).toFixed(1);

      // 接口中没有 completed_tasks 字段，我们可以根据项目名称和描述生成一些任务
      let tasks = [];

      // 使用项目描述中的内容作为任务
      if (this.projectData.description) {
        // 尝试按分隔符拆分描述为多个任务
        const descParts = this.projectData.description.split(/[,，、;；.。]/);
        tasks = descParts.filter(part => part.trim().length > 0);
      }

      // 如果没有足够的任务，添加一些默认任务
      if (tasks.length < 2) {
        const defaultTasks = [
          '桥墩基础施工',
          '首跨梁段安装',
          '第二跨梁段吊装',
          '第三跨梁段安装'
        ];

        // 只添加缺少的任务数量
        const tasksToAdd = defaultTasks.slice(0, Math.max(2 - tasks.length, 0));
        tasks = [...tasks, ...tasksToAdd];
      }

      // 根据进度计算已完成的任务数量
      const completedTaskCount = Math.max(1, Math.floor((tasks.length * progress) / 100));

      // 生成已完成任务文本
      const completedTasks = tasks.slice(0, completedTaskCount);
      const tasksText = completedTasks.map(task => `- ${task}`).join('\n');

      // 显示项目状态
      const statusText = this.projectData.status === 1 ? '进行中' : '筹备中';

      alert(`项目名称：${this.projectName}\n项目状态：${statusText}\n当前进度：${progress}%\n已完成工作：\n${tasksText}`);
    },

    showEquipmentList() {
      let equipmentText = '';

      // 优先使用传入的 deviceList
      if (this.deviceList && this.deviceList.length > 0) {
        equipmentText = this.deviceList.map((device, index) => {
          const deviceName = device.name || device.device_name || device.type || '未知设备';
          const deviceId = device.id || device.device_id || index + 1;
          const deviceType = device.type || device.device_type || '标准型';
          return `- ${deviceName}(ID: ${deviceId})：${deviceType}`;
        }).join('\n');
      } else {
        // 如果没有 deviceList，尝试从 bridge_machine_models 字段获取设备型号
        let models = [];

        if (this.projectData.bridge_machine_models) {
          try {
            // 如果是字符串，尝试解析为数组
            if (typeof this.projectData.bridge_machine_models === 'string') {
              models = JSON.parse(this.projectData.bridge_machine_models);
              if (!Array.isArray(models)) {
                models = [models];
              }
            }
            // 如果已经是数组
            else if (Array.isArray(this.projectData.bridge_machine_models)) {
              models = this.projectData.bridge_machine_models;
            }
          } catch (e) {
            console.error('解析设备型号出错:', e);
            // 如果解析失败，使用原始字符串
            models = [this.projectData.bridge_machine_models];
          }
        }

        // 如果没有设备型号数据，使用本地设备数组
        if (models.length === 0 && this.devices.length > 0) {
          models = this.devices.map(device => device.name);
        }

        // 如果仍然没有数据，使用默认值
        if (models.length === 0) {
          models = ['BCM5583', 'BCM7907', 'BCM7448'];
        }

        // 格式化设备型号为可读文本
        equipmentText = models.map((model, index) => {
          // 移除可能的引号
          const cleanModel = String(model).replace(/['"]/g, '');
          return `- ${cleanModel}型造桥机：${index + 1}台`;
        }).join('\n');
      }
      alert(`项目设备清单(共${this.equipmentCount}台)：\n${equipmentText}`);
    },

    // 添加从API获取姿态水平状态数据的方法
    fetchAngleData() {
      if (!this.projectId) {
        console.warn('No project ID provided for angle data');
        // 如果没有项目ID，清空数据
        this.attitudeData = [];
        this.sensorData = [];
        return;
      }

      console.log('获取项目姿态水平状态数据，项目ID:', this.projectId);

      getProjectAngleData(this.projectId)
        .then(response => {
          if (response && response.code === 0 && response.data) {
            console.log('获取到姿态水平状态数据，数据条数:', response.data.length);

            // 处理可用的数据
            this.processAngleData(response.data);
          } else {
            console.warn('姿态水平状态数据API返回错误或空数据:', response);
            // 如果API返回错误或空数据，清空数据
            this.attitudeData = [];
            this.sensorData = [];
            // 重置姿态角度值
            this.attitudeX = 0;
            this.attitudeY = 0;
          }
        })
        .catch(error => {
          console.error('获取姿态水平状态数据失败:', error);
          // 如果获取失败，清空数据
          this.attitudeData = [];
          this.sensorData = [];
          // 重置姿态角度值
          this.attitudeX = 0;
          this.attitudeY = 0;
        });
    },

    // 处理API返回的姿态水平状态数据
    processAngleData(data) {
      if (!Array.isArray(data) || data.length === 0) {
        console.warn('姿态水平状态数据格式不正确或为空');
        // 如果数据为空，生成示例数据
        this.generateSampleSensorData();
        return;
      }

      // 记录原始数据
      console.log(`开始处理 ${data.length} 条姿态水平状态数据`);

      // 检查数据是否包含必要的字段
      const validData = data.filter(sensor => {
        const hasDeviceName = !!sensor.device_name;
        const hasSensorName = !!(sensor.sensor_description || sensor.sensor_name);
        const hasValue = sensor.value !== undefined && sensor.value !== null;

        if (!hasDeviceName || !hasSensorName || !hasValue) {
          console.warn('发现无效的传感器数据:', sensor);
          return false;
        }

        return true;
      });

      console.log(`过滤后有效数据: ${validData.length} 条`);

      // 如果有效数据为空，清空数据
      if (validData.length === 0) {
        console.warn('没有有效的传感器数据');
        this.attitudeData = [];
        this.sensorData = [];
        // 重置姿态角度值
        this.attitudeX = 0;
        this.attitudeY = 0;
        return;
      }

      // 清空现有数据
      this.attitudeData = [];

      // 重置传感器数据
      this.sensorData = [];

      // 处理每个传感器数据
      validData.forEach(sensor => {
        // 设备名称
        const deviceName = sensor.device_name || '未知设备';
        // 网关名称
        const gatewayName = sensor.gateway_name || '未知网关';
        // 传感器名称 (优先使用传感器描述，如果没有则使用传感器名称)
        const sensorName = sensor.sensor_description || sensor.sensor_name || '未知传感器';
        // 传感器单位
        const unit = sensor.unit || '°';

        // 传感器值，如果无效则默认为0
        let sensorValue = 0;
        try {
          sensorValue = parseFloat(sensor.value);
          if (isNaN(sensorValue)) sensorValue = 0;
        } catch (e) {
          console.warn(`传感器 ${sensorName} 的值无效:`, sensor.value);
        }

        // 记录处理的数据
        console.log(`处理传感器: ${deviceName} - ${gatewayName} - ${sensorName}, 值: ${sensorValue}${unit}`);

        // 添加到姿态水平状态数据数组
        this.attitudeData.push({
          device: deviceName,
          gateway: gatewayName,
          sensor: sensorName,
          value: sensorValue,
          unit: unit
        });

        // 只添加包含"水平"或"倾角"的传感器到传感器数据列表
        if (sensorName.includes('水平') || sensorName.includes('倾角')) {
          this.sensorData.push({
            name: `${deviceName}-${gatewayName}-${sensorName}`,
            value: sensorValue,
            unit: unit,
            warningThreshold: 0.8,
            dangerThreshold: 1.5,
            maxValue: 5
          });
        }
      });

      // 更新主要的姿态角度值（用于气泡显示）
      if (this.sensorData.length > 0) {
        // 找到绝对值最大的传感器值作为X轴倾角
        let maxAbsValue = 0;
        let maxValue = 0;

        this.sensorData.forEach(sensor => {
          if (Math.abs(sensor.value) > maxAbsValue) {
            maxAbsValue = Math.abs(sensor.value);
            maxValue = sensor.value;
          }
        });

        this.attitudeX = maxValue;

        // Y轴倾角设为X轴的一半，但符号相反，以模拟真实情况
        this.attitudeY = -this.attitudeX * 0.5;
      } else {
        // 如果没有传感器数据，重置姿态角度值
        this.attitudeX = 0;
        this.attitudeY = 0;
      }

      console.log('姿态水平状态数据处理完成，传感器数量:', this.sensorData.length);

      // 数据更新后，重新调整滚动速度
      this.$nextTick(() => {
        this.adjustScrollSpeed();
      });
    },

    // 生成示例传感器数据
    generateSampleSensorData() {
      console.log('生成示例传感器数据');

      // 清空现有数据
      this.attitudeData = [];
      this.sensorData = [];

      // 设备名称列表
      const devices = ['主桥塔', '辅助塔', '监控站', '桥面'];

      // 传感器类型列表
      const sensorTypes = ['横向倾角', '纵向倾角', '水平状态', '垂直倾斜'];

      // 生成6-10个示例传感器数据
      const sensorCount = Math.floor(Math.random() * 5) + 6;

      for (let i = 0; i < sensorCount; i++) {
        // 随机选择设备和传感器类型
        const deviceIndex = i % devices.length;
        const sensorTypeIndex = Math.floor(Math.random() * sensorTypes.length);

        // 随机生成传感器值，大多数在正常范围内，少数在警告或危险范围
        let value = (Math.random() * 2 - 1) * 0.5; // 大多数在 -0.5 到 0.5 之间

        // 10%的几率生成警告值
        if (Math.random() < 0.1) {
          value = (Math.random() * 0.7 + 0.8) * (Math.random() < 0.5 ? 1 : -1); // 0.8 到 1.5 之间
        }

        // 5%的几率生成危险值
        if (Math.random() < 0.05) {
          value = (Math.random() * 1.5 + 1.5) * (Math.random() < 0.5 ? 1 : -1); // 1.5 到 3.0 之间
        }

        // 添加到传感器数据列表
        this.sensorData.push({
          name: `${devices[deviceIndex]}-网关${Math.floor(i/2)+1}-${sensorTypes[sensorTypeIndex]}`,
          value: value,
          unit: '°',
          warningThreshold: 0.8,
          dangerThreshold: 1.5,
          maxValue: 5
        });

        // 添加到姿态水平状态数据数组
        this.attitudeData.push({
          device: devices[deviceIndex],
          gateway: `网关${Math.floor(i/2)+1}`,
          sensor: sensorTypes[sensorTypeIndex],
          value: value,
          unit: '°'
        });
      }

      // 更新主要的姿态角度值
      if (this.sensorData.length > 0) {
        // 找到绝对值最大的传感器值作为X轴倾角
        let maxAbsValue = 0;
        let maxValue = 0;

        this.sensorData.forEach(sensor => {
          if (Math.abs(sensor.value) > maxAbsValue) {
            maxAbsValue = Math.abs(sensor.value);
            maxValue = sensor.value;
          }
        });

        this.attitudeX = maxValue;

        // Y轴倾角设为X轴的一半，但符号相反，以模拟真实情况
        this.attitudeY = -this.attitudeX * 0.5;
      } else {
        // 如果没有传感器数据，重置姿态角度值
        this.attitudeX = 0;
        this.attitudeY = 0;
      }

      console.log('生成示例传感器数据完成，传感器数量:', this.sensorData.length);

      // 数据更新后，重新调整滚动速度
      this.$nextTick(() => {
        this.adjustScrollSpeed();
      });
    },

    // 启动API数据刷新定时器
    startApiDataRefresh() {
      // 每30秒刷新一次API数据
      this.angleApiRefreshTimer = setInterval(() => {
        if (this.projectId) {
          this.fetchAngleData();
        }
      }, 30000); // 30秒
    },

    // 添加姿态水平模拟方法 - 移除模拟数据生成逻辑，仅保留定时器以便清理
    startAttitudeSimulation() {
      // 不再需要模拟数据，仅创建空的定时器以便在组件销毁时清理
      this.attitudeUpdateTimer = setInterval(() => {
        // 空函数，不执行任何操作
      }, 60000); // 设置较长的间隔，减少资源消耗
    },

    // 更新传感器数据 - 不再需要，移除所有逻辑
    updateSensorData() {
      // 空函数，不执行任何操作
    },

    // 获取传感器类名
    getSensorClass(value, warningThreshold, dangerThreshold) {
      const absValue = Math.abs(value);
      if (absValue >= dangerThreshold) {
        return 'danger';
      } else if (absValue >= warningThreshold) {
        return 'warning';
      } else {
        return 'normal';
      }
    },

    // 获取传感器条宽度
    getSensorBarWidth(value, maxValue) {
      // 将值转换为0-100%范围的宽度
      const absValue = Math.abs(value);
      const percentage = Math.min((absValue / maxValue) * 100, 100);
      return `${percentage}%`;
    },

    // 获取传感器条颜色
    getSensorBarColor(value, warningThreshold, dangerThreshold) {
      const absValue = Math.abs(value);
      if (absValue >= dangerThreshold) {
        return '#ff5555'; // 危险红色
      } else if (absValue >= warningThreshold) {
        return '#ffaa00'; // 警告黄色
      } else {
        return '#00ff9d'; // 正常绿色
      }
    },

    // 添加传感器数据自动滚动方法
    startSensorAutoScroll() {
      // 创建一个观察器，当元素可见时才启动动画
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              // 元素可见，调整动画速度
              this.adjustScrollSpeed();
            }
          });
        }, { threshold: 0.1 });

        // 当元素挂载后观察它
        this.$nextTick(() => {
          const sensorList = this.$refs.sensorList;
          if (sensorList) {
            observer.observe(sensorList);
          }
        });
      }
    },

    // 调整滚动速度
    adjustScrollSpeed() {
      const sensorList = this.$refs.sensorList;
      if (!sensorList || !this.shouldAutoScroll) return;

      // 根据传感器数量调整动画持续时间
      const duration = Math.max(this.sensorData.length * 3, 10); // 每个传感器至少3秒，最少10秒
      sensorList.style.animationDuration = `${duration}s`;
    }
  }
}
</script>

<style scoped>
.left-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
  padding: 0;
  margin: 0;
  padding-top: 0;
  margin-top: 0;
  width: 380px;
  max-width: 380px;
  gap: 2px; /* 进一步减小模块间距 */
  z-index: 300 !important; /* 确保在3D模型上方 */
  pointer-events: auto !important; /* 确保可以接收鼠标事件 */
}

/* 导入左侧列样式 */
@import '../../../../assets/css/left-column.css';
/* 导入漂浮模块样式 */
@import '../../../../assets/css/floating-modules.css';

/* 无数据显示样式 */
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px; /* 增加高度 */
  color: rgba(127, 219, 255, 0.6); /* 增加颜色亮度 */
  text-align: center;
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 4px;
  border: 2px dashed rgba(127, 219, 255, 0.4); /* 加粗边框 */
  margin: 10px;
}

.no-data-container i {
  font-size: 4.5rem; /* 增大图标尺寸 */
  margin-bottom: 15px; /* 增加间距 */
  opacity: 0.7; /* 增加不透明度 */
  color: rgba(127, 219, 255, 0.7); /* 单独设置图标颜色 */
}

.no-data-container p {
  font-size: 1.4rem; /* 增大字体尺寸 */
  font-weight: 500; /* 增加字体粗细 */
  margin: 0;
  letter-spacing: 1px; /* 增加字间距 */
}

/* 添加新样式用于减小环境监控模块整体高度 */
.environment-monitor.compact-widget {
  margin-bottom: 4px; /* 增加底部边距 */
  max-height: 130px; /* 增加整个模块的最大高度 */
  background: transparent; /* 设置背景透明 */
}

/* 压缩widget的头部高度 */
.environment-monitor .widget-header.compact-header {
  padding: 2px 8px; /* 增加上下内边距 */
  height: 22px; /* 增加头部高度 */
  min-height: 22px;
  line-height: 22px;
  font-size: 0.75rem;
  background: transparent; /* 设置背景透明 */
  border-bottom: 1px solid rgba(0, 168, 255, 0.2); /* 添加底部边框增强科技感 */
}

.environment-monitor .widget-header.compact-header i {
  font-size: 0.7rem;
}

.environment-monitor .widget-content.ultra-compact {
  max-height: 105px; /* 增加内容区域最大高度 */
  min-height: 105px;
  overflow: hidden;
  padding: 2px; /* 增加内边距 */
  margin-top: 1px; /* 增加与头部的间距 */
  background: transparent; /* 设置背景透明 */
}

.environment-monitor .device-header.compact-header {
  padding: 2px 0; /* 增加上下内边距 */
  margin: 0 0 2px 0; /* 增加底部边距 */
  height: 20px; /* 增加设备头部高度 */
  min-height: 20px;
  width: calc(100% - 4px); /* 调整宽度与内容区域一致 */
  margin-left: auto;
  margin-right: auto;
}

.environment-monitor .device-navigation.compact-nav {
  min-height: 20px;
  height: 20px;
  line-height: 20px;
  width: 100%; /* 确保导航栏占满设备头部宽度 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(0, 30, 60, 0.3); /* 降低背景不透明度 */
  border-radius: 3px; /* 添加圆角 */
  border: 1px solid rgba(0, 168, 255, 0.3); /* 增加边框亮度 */
  padding: 0 4px; /* 添加左右内边距 */
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1); /* 添加发光效果 */
}

.environment-monitor .device-navigation .device-title {
  font-size: 0.7rem; /* 增加字体大小 */
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.environment-monitor .device-navigation .device-title i {
  font-size: 0.75rem;
  margin-right: 3px; /* 增加图标与文字的间距 */
}

.environment-monitor .nav-btn {
  width: 14px; /* 增加按钮尺寸 */
  height: 14px;
  font-size: 0.6rem;
  line-height: 14px;
  margin: 0 2px; /* 增加按钮间距 */
}

/* 超小型无数据容器 */
.environment-monitor .no-data-container.mini {
  min-height: 20px;
  max-height: 44px;
}

.environment-monitor .no-data-container.mini i {
  font-size: 1rem;
  margin-bottom: 1px;
}

.environment-monitor .no-data-container.mini p {
  font-size: 0.55rem;
}

/* 为所有widget添加通用透明背景样式 */
.widget {
  background: transparent !important; /* 设置所有模块背景透明 */
  border: none !important; /* 移除边框 */
  box-shadow: none !important; /* 移除阴影 */
  margin-bottom: 3px; /* 减小模块间距 */
}

.widget-header {
  background: transparent !important; /* 设置所有模块头部背景透明 */
  border-bottom: 1px solid rgba(0, 168, 255, 0.2) !important; /* 添加底部边框增强科技感 */
  height: 22px !important; /* 统一头部高度 */
  line-height: 22px !important; /* 统一行高 */
}

.widget-content {
  background: transparent !important; /* 设置所有模块内容区域背景透明 */
  padding: 3px !important; /* 统一内边距 */
}

/* 项目信息模块科技感样式 */
.widget.project-info {
  background: transparent !important;
}

.widget.project-info .widget-content {
  background: transparent !important;
}

/* 为数据项添加科技感样式 */
.stat-item {
  background-color: rgba(0, 30, 60, 0.3) !important; /* 设置半透明背景 */
  border: 1px solid rgba(0, 168, 255, 0.3) !important; /* 添加发光边框 */
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1) !important; /* 添加发光效果 */
  transition: all 0.3s ease;
}

.stat-item:hover {
  border-color: rgba(0, 168, 255, 0.5) !important;
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.3) !important;
}

/* 环境监控模块科技感样式 */
.environment-monitor.compact-widget {
  max-height: 130px; /* 整个模块的最大高度 */
  background: transparent !important;
  margin-bottom: 3px;
}

.environment-monitor .widget-header.compact-header {
  background: transparent !important;
  border-bottom: 1px solid rgba(0, 168, 255, 0.2) !important;
}

.environment-monitor .widget-content.ultra-compact {
  background: transparent !important;
}

.environment-monitor .device-header.compact-header {
  width: calc(100% - 4px);
  margin-left: auto;
  margin-right: auto;
}

.environment-monitor .device-navigation.compact-nav {
  width: 100%;
  background-color: rgba(0, 30, 60, 0.3);
  border: 1px solid rgba(0, 168, 255, 0.3);
  box-shadow: 0 0 5px rgba(0, 168, 255, 0.1);
}

/* 系统信息模块科技感样式 */
.widget.system-info-chart {
  background: transparent !important;
  min-height: 280px; /* 设置系统信息模块最小高度 */
}

.widget.system-info-chart .widget-content {
  background: transparent !important;
  min-height: 250px; /* 设置系统信息模块内容区域最小高度 */
  height: auto; /* 允许高度自适应 */
}

/* 姿态水平模块科技感样式 */
.widget.attitude-level-chart {
  background: transparent !important;
}

.widget.attitude-level-chart .widget-content {
  background: transparent !important;
}

/* 为大屏幕模式增加widget-content.fixed-height的高度 */
.widget-content.fixed-height {
  height: 220px; /* 增加高度以适应更多传感器数据 */
  min-height: 220px;
  max-height: 220px;
}

.attitude-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* 改为顶部对齐，更好地利用空间 */
  padding: 5px;
  background-color: transparent !important;
  /* background-color: red; */
  height: 100%;
  width: 100%;
}

.attitude-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: transparent !important;
}

.attitude-bubble {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 10px;
  background-color: transparent !important;
  box-shadow: 0 0 15px rgba(0, 168, 255, 0.2);
  border: 1px solid rgba(0, 168, 255, 0.4);
  overflow: hidden;
}

.bubble-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease;
  background-color: transparent !important;
}

.bubble-inner i {
  color: #00ff9d;
  font-size: 12px;
  filter: drop-shadow(0 0 3px rgba(0, 255, 157, 0.8));
  transition: all 0.5s ease;
}

/* 添加一个动态阴影效果，根据颜色变化 */
.bubble-inner i[style*="color: #ffaa00"] {
  filter: drop-shadow(0 0 4px rgba(255, 170, 0, 0.8));
  animation: pulse-warning 1.5s infinite;
}

.bubble-inner i[style*="color: #ff5555"] {
  filter: drop-shadow(0 0 5px rgba(255, 85, 85, 0.8));
  animation: pulse-danger 0.8s infinite;
}

.bubble-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: transparent !important;
}

.grid-line {
  position: absolute;
  background-color: rgba(0, 168, 255, 0.3);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
}

.grid-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
}

.grid-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 1px solid rgba(0, 168, 255, 0.3);
  background-color: transparent !important;
}

.grid-circle.outer {
  width: 90px;
  height: 90px;
}

.grid-circle.middle {
  width: 60px;
  height: 60px;
}

.grid-circle.inner {
  width: 30px;
  height: 30px;
}

.attitude-data {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 5px;
  background-color: transparent !important;
}

.attitude-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent !important;
}

.attitude-value .label {
  font-size: 0.75rem;
  color: #7fdbff;
  margin-bottom: 3px;
}

.attitude-value .value {
  font-size: 0.9rem;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
}

/* 无数据显示样式优化 */
.no-data-container {
  background-color: rgba(0, 30, 60, 0.2) !important; /* 降低背景不透明度 */
  border: 1px dashed rgba(0, 168, 255, 0.3) !important; /* 改为更科技感的边框 */
}

/* 添加发光动画效果 */
@keyframes glow-pulse {
  0% { box-shadow: 0 0 5px rgba(0, 168, 255, 0.2); }
  50% { box-shadow: 0 0 10px rgba(0, 168, 255, 0.4); }
  100% { box-shadow: 0 0 5px rgba(0, 168, 255, 0.2); }
}

.stat-value, .device-title, .widget-status {
  animation: glow-pulse 3s infinite;
}

/* 进度条发光效果 */
.mini-progress-fill, .timeline-fill {
  box-shadow: 0 0 8px rgba(0, 255, 157, 0.3) !important;
}

/* 响应式调整 */
@media (max-width: 1366px) {
  .widget-content.fixed-height {
    height: 200px; /* 增加高度以适应更多传感器数据 */
    min-height: 200px;
    max-height: 200px;
  }

  .attitude-bubble {
    width: 90px;
    height: 90px;
  }

  .grid-circle.outer {
    width: 80px;
    height: 80px;
  }

  .grid-circle.middle {
    width: 50px;
    height: 50px;
  }

  .sensor-data-scrollable-container {
    max-height: 170px; /* 增加传感器数据容器高度 */
  }

  /* 系统信息模块响应式调整 */
  .widget.system-info-chart {
    min-height: 250px; /* 小屏幕下的系统信息模块最小高度 */
  }

  .widget.system-info-chart .widget-content {
    min-height: 220px; /* 小屏幕下的系统信息模块内容区域最小高度 */
  }
}

.attitude-value .value.warning {
  color: #ffaa00;
  text-shadow: 0 0 5px rgba(255, 170, 0, 0.7);
  animation: pulse-warning 1.5s infinite;
}

.attitude-value .value.danger {
  color: #ff5555;
  text-shadow: 0 0 5px rgba(255, 85, 85, 0.7);
  animation: pulse-danger 0.8s infinite;
}

@keyframes pulse-warning {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 左列字体统一样式 */
.left-column .widget-header,
.left-column .widget-header span {
  font-size: 14px !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
}

.left-column .widget-content {
  font-size: 12px !important;
  line-height: 1.4 !important;
}

.left-column .sensor-name {
  font-size: 11px !important;
  line-height: 1.2 !important;
}

.left-column .sensor-value {
  font-size: 12px !important;
  font-weight: bold !important;
  line-height: 1.2 !important;
}

.left-column .widget-status {
  font-size: 10px !important;
  line-height: 1.2 !important;
}

.left-column .no-data-container,
.left-column .no-data-container * {
  font-size: 11px !important;
  line-height: 1.3 !important;
}

@keyframes pulse-danger {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
  100% { opacity: 1; transform: scale(1); }
}

/* 添加水平倾角可视化样式 */
.attitude-horizontal-display {
  width: 100%;
  padding: 10px 5px;
  margin-bottom: 10px;
  background-color: transparent !important;
}

.tilt-meter {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  overflow: hidden;
}

.tilt-scale {
  position: relative;
  width: 100%;
  height: 100%;
}

.scale-mark {
  position: absolute;
  top: 0;
  height: 100%;
}

.scale-mark.center {
  left: 50%;
  transform: translateX(-50%);
}

.mark-line {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 100%;
  background-color: rgba(0, 168, 255, 0.3);
}

.scale-mark.center .mark-line {
  background-color: rgba(0, 168, 255, 0.7);
  width: 2px;
}

.mark-value {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.6rem;
  color: rgba(127, 219, 255, 0.8);
}

.tilt-pointer {
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  transition: left 0.5s ease;
}

.pointer-line {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.7);
}

.pointer-head {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 8px solid #ffffff;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.7));
}

.level-bar {
  position: absolute;
  bottom: 0;
  height: 4px;
  background-color: #00ff9d;
  transition: width 0.5s ease, left 0.5s ease;
}

.level-bar.warning {
  background-color: #ffaa00;
  box-shadow: 0 0 8px rgba(255, 170, 0, 0.7);
  animation: pulse-warning 1.5s infinite;
}

.level-bar.danger {
  background-color: #ff5555;
  box-shadow: 0 0 8px rgba(255, 85, 85, 0.7);
  animation: pulse-danger 0.8s infinite;
}

/* 传感器数据列表样式 */
.sensor-data-scrollable-container {
  width: 100%;
  max-height: 180px; /* 大幅增加高度以显示更多传感器数据 */
  min-height: 150px; /* 设置最小高度确保有足够空间 */
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 168, 255, 0.5) rgba(0, 30, 60, 0.3);
  padding-right: 5px;
  position: relative; /* 为渐变效果添加相对定位 */
}

/* 添加滚动渐变效果 */
.scroll-fade-top, .scroll-fade-bottom {
  position: absolute;
  left: 0;
  right: 0;
  height: 15px;
  z-index: 10;
  pointer-events: none;
}

.scroll-fade-top {
  top: 0;
  background: linear-gradient(to bottom,
    rgba(0, 30, 60, 0.5) 0%,
    rgba(0, 30, 60, 0) 100%);
}

.scroll-fade-bottom {
  bottom: 0;
  background: linear-gradient(to top,
    rgba(0, 30, 60, 0.5) 0%,
    rgba(0, 30, 60, 0) 100%);
}

/* 添加滚动指示器 */
.scroll-indicator {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(0, 168, 255, 0.7);
  font-size: 12px;
  animation: bounce 1.5s infinite;
  z-index: 11;
}

@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-3px); }
}

/* 自定义滚动条样式 */
.sensor-data-scrollable-container::-webkit-scrollbar {
  width: 4px;
}

.sensor-data-scrollable-container::-webkit-scrollbar-track {
  background: rgba(0, 30, 60, 0.3);
  border-radius: 2px;
}

.sensor-data-scrollable-container::-webkit-scrollbar-thumb {
  background: rgba(0, 168, 255, 0.5);
  border-radius: 2px;
}

.sensor-data-scrollable-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 168, 255, 0.7);
}

.sensor-data-list {
  width: 100%;
  margin: 5px 0;
  background-color: transparent !important;
}

/* 添加自动滚动动画 */
@keyframes auto-scroll {
  0% { transform: translateY(0); }
  100% { transform: translateY(-50%); } /* 只滚动一半高度，因为我们复制了数据 */
}

/* 当传感器数据超过3个时启用自动滚动 */
.sensor-data-list.auto-scroll {
  animation: auto-scroll 15s linear infinite;
  animation-play-state: running;
  padding-bottom: 10px; /* 添加底部内边距，确保最后一个项目可以完全滚动出视图 */
}

.sensor-data-list.auto-scroll:hover {
  animation-play-state: paused; /* 鼠标悬停时暂停滚动 */
}

.sensor-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
  padding: 5px;
  background-color: rgba(0, 30, 60, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(0, 168, 255, 0.3);
  transition: all 0.3s ease;
}

.sensor-item:hover {
  border-color: rgba(0, 168, 255, 0.5);
  box-shadow: 0 0 8px rgba(0, 168, 255, 0.3);
}

.sensor-item.warning {
  border-color: rgba(255, 170, 0, 0.5);
  box-shadow: 0 0 8px rgba(255, 170, 0, 0.3);
}

.sensor-item.danger {
  border-color: rgba(255, 85, 85, 0.5);
  box-shadow: 0 0 8px rgba(255, 85, 85, 0.3);
  animation: flash-danger 1s infinite;
}

.sensor-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.sensor-name {
  font-size: 0.75rem;
  color: #7fdbff;
}

.sensor-value {
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff;
}

.sensor-value.warning {
  color: #ffaa00;
  text-shadow: 0 0 5px rgba(255, 170, 0, 0.7);
}

.sensor-value.danger {
  color: #ff5555;
  text-shadow: 0 0 5px rgba(255, 85, 85, 0.7);
  animation: pulse-danger 0.8s infinite;
}

.sensor-bar-container {
  width: 100%;
  height: 4px;
  background-color: rgba(0, 168, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.sensor-bar {
  height: 100%;
  background-color: #00ff9d;
  border-radius: 2px;
  transition: width 0.5s ease, background-color 0.5s ease;
}

@keyframes flash-danger {
  0% { background-color: rgba(0, 30, 60, 0.3); }
  50% { background-color: rgba(255, 85, 85, 0.1); }
  100% { background-color: rgba(0, 30, 60, 0.3); }
}
</style>

