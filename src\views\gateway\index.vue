<template>
  <div class="gateway-container">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i> 新增网关
      </el-button>
      <el-input
        v-model="searchQuery"
        placeholder="搜索网关名称/网关号"
        style="width: 200px; margin-left: 16px"
        clearable
        @clear="handleSearch"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input>
    </div>

    <!-- 网关列表 -->
    <el-table
      v-loading="loading"
      :data="gatewayList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="id" label="网关ID" width="80" fixed="left" />
      <el-table-column prop="gateway_code" label="网关号" width="150" fixed="left" />
      <el-table-column prop="gateway_name" label="网关名称" min-width="150" />
      <el-table-column label="所属设备" min-width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.device_name">{{ scope.row.device_name }}</span>
          <span v-else-if="scope.row.device && scope.row.device.device_name">{{ scope.row.device.device_name }}</span>
          <span v-else class="no-data">未关联设备</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
              class="operation-button"
            >编辑</el-button>
            <el-button
              size="mini"
              type="success"
              @click="handleViewSensors(scope.row)"
              class="operation-button"
            >查看传感器</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              class="operation-button"
            >删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 网关表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="gatewayForm"
        :model="gatewayForm"
        :rules="gatewayRules"
        label-width="120px"
      >
        <el-form-item label="网关号" prop="gateway_code">
          <el-input v-model="gatewayForm.gateway_code" disabled placeholder="网关创建后自动生成"></el-input>
        </el-form-item>
        <el-form-item label="网关名称" prop="gateway_name">
          <el-input v-model="gatewayForm.gateway_name" placeholder="请输入网关名称"></el-input>
        </el-form-item>
        <el-form-item label="所属设备" prop="device_id">
          <el-select 
            v-model="gatewayForm.device_id" 
            placeholder="请选择所属设备" 
            clearable>
            <el-option
              v-for="device in deviceList"
              :key="device.id"
              :label="device.device_name"
              :value="device.id"
            ></el-option>
          </el-select>
          <div class="form-hint">可选，不选择设备将作为独立网关</div>
        </el-form-item>
        <el-form-item label="网关状态" prop="status">
          <el-select v-model="gatewayForm.status" placeholder="请选择网关状态">
            <el-option label="离线" :value="0"></el-option>
            <el-option label="在线" :value="1"></el-option>
            <el-option label="故障" :value="2"></el-option>
            <el-option label="维护中" :value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 修改传感器列表对话框 -->
    <el-dialog
      title="传感器列表"
      :visible.sync="sensorDialogVisible"
      width="800px"
      class="sensor-list-dialog"
    >
      <div v-loading="sensorLoading">
        <!-- 网关信息 -->
        <div class="gateway-info">
          <div class="info-item">
            <span class="label">网关名称:</span>
            <span class="value">{{ currentGateway.gateway_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">网关号:</span>
            <span class="value">{{ currentGateway.gateway_code }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态:</span>
            <el-tag :type="getStatusType(currentGateway.status)">
              {{ getStatusText(currentGateway.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="sensor-table-container">
          <!-- 传感器列表表格 -->
          <el-table
            :data="sensorList"
            border
            style="width: 100%"
            v-if="sensorList.length > 0"
            height="400"
          >
            <el-table-column prop="id" label="传感器ID" width="80" />
            <el-table-column prop="sensor_name" label="传感器名称" min-width="120" />
            <el-table-column prop="sensor_type" label="类型" width="100">
              <template slot-scope="scope">
                <el-tag size="small">{{ scope.row.sensor_type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sensor_value" label="传感器值" width="100">
              <template slot-scope="scope">
                <span :class="getSensorValueClass(scope.row)">
                  {{ scope.row.sensor_value }}
                  <span v-if="scope.row.unit">({{ scope.row.unit }})</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="更新时间" min-width="150" />
          </el-table>
          
          <!-- 无数据提示 -->
          <el-empty
            v-else
            description="暂无传感器数据"
            :image-size="200"
          ></el-empty>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入网关 API 函数
import {
  getGatewayList,
  createGateway,
  updateGateway,
  deleteGateway,
  getGatewayDetail,
  batchDeleteGateways,
  updateGatewayStatus
} from '@/api/gateway'

export default {
  name: 'GatewayManagement',
  data() {
    return {
      loading: false,
      searchQuery: '',
      gatewayList: [],
      deviceList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      gatewayForm: {
        gateway_code: '',
        gateway_name: '',
        device_id: '',
        status: 0
      },
      gatewayRules: {
        gateway_name: [
          { required: true, message: '请输入网关名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        device_id: [
          // 非必填
        ],
        status: [
          { required: true, message: '请选择网关状态', trigger: 'change' }
        ]
      },
      statusFilter: '',
      selectedGateways: [],
      submitLoading: false,
      sensorDialogVisible: false,
      sensorLoading: false,
      currentGateway: {},
      sensorList: []
    }
  },
  created() {
    this.fetchGatewayList()
    this.fetchDeviceList()
  },
  methods: {
    // 获取网关列表
    async fetchGatewayList() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          query: this.searchQuery,
          status: this.statusFilter
        }
        
        const response = await getGatewayList(params)
        
        if (response && response.code === 0 && response.data) {
          // 处理数据，确保字段名称一致性
          this.gatewayList = (response.data.list || []).map(gateway => {
            // 确保字段名称与表单字段一致
            if (gateway.gatewayNo && !gateway.gateway_code) {
              gateway.gateway_code = gateway.gatewayNo
            }
            if (gateway.name && !gateway.gateway_name) {
              gateway.gateway_name = gateway.name
            }
            
            // 处理设备名称
            if (gateway.device && gateway.device.device_name && !gateway.device_name) {
              gateway.device_name = gateway.device.device_name
            } else if (gateway.device && gateway.device.name && !gateway.device_name) {
              gateway.device_name = gateway.device.name
            }
            
            return gateway
          })
          
          this.total = response.data.total || 0
        } else {
          this.$message.error(response?.message || '获取网关列表失败')
          this.gatewayList = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取网关列表失败:', error)
        this.$message.error('获取网关列表失败，请稍后重试')
        this.gatewayList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 获取设备列表
    async fetchDeviceList() {
      try {
        // 导入设备API
        const { getDeviceList } = require('@/api/device')
        
        // 获取设备列表
        const params = {
          pageSize: 100 // 获取足够多的设备以便选择
        }
        const response = await getDeviceList(params)
        
        if (response && response.code === 0 && response.data) {
          // 确保使用正确的设备名称字段
          this.deviceList = (response.data.list || []).map(device => {
            return {
              id: device.id,
              device_name: device.device_name || device.name || '未命名设备',
              // 其他需要的设备字段...
            }
          })
        } else {
          console.warn('获取设备列表错误:', response)
          this.deviceList = []
        }
      } catch (error) {
        console.error('获取设备列表失败:', error)
        this.deviceList = []
      }
    },

    // 生成网关号
    generateGatewayNo() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `GW${year}${month}${day}${random}`
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchGatewayList()
    },

    // 新增网关
    handleAdd() {
      this.dialogTitle = '新增网关'
      this.dialogVisible = true
      this.gatewayForm = {
        gateway_code: this.generateGatewayNo(),
        gateway_name: '',
        device_id: '',
        status: 0
      }
    },

    // 编辑网关
    async handleEdit(row) {
      this.dialogTitle = '编辑网关'
      this.dialogVisible = true
      this.submitLoading = true
      
      try {
        // 获取网关详情
        const response = await getGatewayDetail(row.id)
        
        if (response && response.code === 0 && response.data) {
          // 将详情数据填充到表单
          this.gatewayForm = { ...response.data }
        } else {
          this.$message.error(response?.message || '获取网关详情失败')
        }
      } catch (error) {
        console.error('获取网关详情失败:', error)
        this.$message.error('获取网关详情失败，请稍后重试')
      } finally {
        this.submitLoading = false
      }
    },

    // 删除网关
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该网关吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await deleteGateway(row.id)
        
        if (response && response.code === 0) {
          this.$message.success('删除网关成功')
          this.fetchGatewayList()
        } else {
          this.$message.error(response?.message || '删除网关失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除网关失败:', error)
          this.$message.error('删除网关失败，请稍后重试')
        }
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.gatewayForm.validate().catch(() => false)
        if (!valid) {
          this.$message.warning('请完善表单信息')
          return
        }
        
        this.submitLoading = true
        
        // 准备提交的数据
        const submitData = { ...this.gatewayForm }
        
        // 如果没有选择设备，将 device_id 设为 null 或删除
        if (!submitData.device_id) {
          submitData.device_id = null // 或者删除: delete submitData.device_id
        }
        
        let response
        if (submitData.id) {
          // 更新网关
          response = await updateGateway(submitData)
        } else {
          // 创建网关
          response = await createGateway(submitData)
        }
        
        if (response && response.code === 0) {
          this.$message.success(submitData.id ? '更新网关成功' : '添加网关成功')
          this.dialogVisible = false
          this.fetchGatewayList()
        } else {
          this.$message.error(response?.message || (submitData.id ? '更新网关失败' : '添加网关失败'))
        }
      } catch (error) {
        console.error('提交网关数据失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        this.submitLoading = false
      }
    },

    // 网关状态
    getStatusType(status) {
      const statusMap = {
        0: 'info',      // 离线
        1: 'success',   // 在线
        2: 'danger',    // 故障
        3: 'warning'    // 维护中
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        0: '离线',
        1: '在线',
        2: '故障',
        3: '维护中'
      }
      return statusMap[status] || '未知'
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchGatewayList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchGatewayList()
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.gatewayForm.resetFields()
    },

    // 更新网关状态
    async handleStatusChange(row) {
      // 获取当前状态的反状态
      const newStatus = row.status === 1 ? 0 : 1
      
      try {
        const response = await updateGatewayStatus(row.id, newStatus)
        
        if (response && response.code === 0) {
          this.$message.success(`网关已${newStatus === 1 ? '启用' : '禁用'}`)
          // 更新本地数据，避免重新请求列表
          row.status = newStatus
        } else {
          this.$message.error(response?.message || '更新状态失败')
        }
      } catch (error) {
        console.error('更新网关状态失败:', error)
        this.$message.error('更新状态失败，请稍后重试')
      }
    },

    // 查看传感器
    async handleViewSensors(gateway) {
      this.currentGateway = gateway
      this.sensorDialogVisible = true
      this.sensorLoading = true
      
      try {
        // 导入正确的 API 函数
        const { getSensorListByGatewayCode } = require('@/api/sensor')
        
        // 使用网关编码获取所有传感器
        const response = await getSensorListByGatewayCode(gateway.gateway_code)
        
        if (response && response.code === 0 && response.data) {
          this.sensorList = response.data.list || []
        } else {
          this.$message.warning(response?.message || '获取传感器列表失败')
          this.sensorList = []
        }
      } catch (error) {
        console.error('获取传感器列表失败:', error)
        this.sensorList = []
        this.$message.error('获取传感器列表失败，请稍后重试')
      } finally {
        this.sensorLoading = false
      }
    },

    // 获取传感器值显示类型
    getSensorValueClass(sensor) {
      // 根据传感器值或状态返回不同的样式类名
      if (sensor.status === 'normal' || 
          (sensor.min_threshold !== undefined && 
           sensor.max_threshold !== undefined && 
           sensor.sensor_value >= sensor.min_threshold && 
           sensor.sensor_value <= sensor.max_threshold)) {
        return 'sensor-value normal'
      } else if (sensor.status === 'warning' || 
                (sensor.warning_threshold !== undefined && 
                 sensor.sensor_value >= sensor.warning_threshold)) {
        return 'sensor-value warning'
      } else if (sensor.status === 'danger' || 
                (sensor.danger_threshold !== undefined && 
                 sensor.sensor_value >= sensor.danger_threshold)) {
        return 'sensor-value danger'
      }
      return 'sensor-value'
    }
  }
}
</script>

<style lang="scss" scoped>
.gateway-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.operation-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-start;
  align-items: flex-start;
  padding-left: 8px;
}

.operation-button {
  width: 90px;
  text-align: center;
  padding: 0;
  margin: 0;
  height: 28px;
  line-height: 26px;
}

.sensor-value {
  font-weight: 500;
  
  &.normal {
    color: #67c23a;
  }
  
  &.warning {
    color: #e6a23c;
  }
  
  &.danger {
    color: #f56c6c;
  }
}

.sensor-list-dialog {
  .gateway-info {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    
    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .label {
        color: #606266;
        font-weight: 500;
      }
      
      .value {
        color: #303133;
      }
    }
  }
  
  .sensor-table-container {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.form-hint {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
</style> 