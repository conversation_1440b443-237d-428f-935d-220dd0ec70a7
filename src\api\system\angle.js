import request from '@/utils/axios'

export function getProjectAngleData(projectId) {
  console.log('Calling force API for project:', projectId);
  
  // 确保使用小写的方法名
  return request({
    path: `/api/system/gateway/project/${projectId}/angle/sensors`,
    method: 'get'
  }).then(response => {
    console.log('Angle API response received:', response);
    return response;
  }).catch(error => {
    console.error('Angle API error:', error);
    throw error;
  });
}