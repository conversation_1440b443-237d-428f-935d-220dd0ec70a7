import request from '@/utils/axios'

// 获取所有造桥机型号
export function getAllModels(params = {}) {
  return request({
    path: '/api/system/model',
    method: 'get',
    params
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('Get models API error:', error);
    throw error;
  });
}

// 添加造桥机型号
export function addModel(data) {
  return request({
    path: '/api/system/model',
    method: 'post',
    data
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('Add model API error:', error);
    throw error;
  });
}

// 根据ID获取造桥机型号详情
export function getModelById(id) {
  return request({
    path: `/api/system/model/${id}`,
    method: 'get'
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('Get model by ID API error:', error);
    throw error;
  });
}

// 更新造桥机型号
export function updateModel(id, data) {
  return request({
    path: `/api/system/model/${id}`,
    method: 'put',
    data
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('Update model API error:', error);
    throw error;
  });
}

// 删除造桥机型号
export function deleteModel(id) {
  return request({
    path: `/api/system/model/${id}`,
    method: 'delete'
  }).then(response => {
    return response;
  }).catch(error => {
    console.error('Delete model API error:', error);
    throw error;
  });
} 