<template>
  <div :class="{'has-logo':showLogo}" class="sidebar-wrapper">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
        ref="sidebarMenu"
      >
        <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  name: 'Sidebar',
  components: { SidebarItem, Logo },
  data() {
    return {
      isFullscreen: false
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  mounted() {
    // 监听全屏变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
    
    // 初始检查全屏状态
    this.checkFullscreenState()
  },
  beforeDestroy() {
    // 移除全屏监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  methods: {
    // 检查当前全屏状态
    checkFullscreenState() {
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      )
    },
    
    // 处理全屏变化
    handleFullscreenChange() {
      const wasFullscreen = this.isFullscreen
      this.checkFullscreenState()
      
      // 如果从全屏状态退出，强制重新渲染菜单
      if (wasFullscreen && !this.isFullscreen) {
        this.$nextTick(() => {
          // 强制重新渲染菜单
          if (this.$refs.sidebarMenu) {
            this.$refs.sidebarMenu.$forceUpdate()
          }
          
          // 强制重新渲染所有子组件
          this.$children.forEach(child => {
            if (child.$forceUpdate) {
              child.$forceUpdate()
            }
          })
          
          // 触发窗口大小变化事件，确保布局正确应用
          window.dispatchEvent(new Event('resize'))
        })
      }
    }
  }
}
</script>

<style scoped>
.sidebar-wrapper {
  height: 100%;
  width: 100%;
}
</style>
