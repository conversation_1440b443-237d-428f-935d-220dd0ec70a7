import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import gsap from 'gsap'
import * as d3 from 'd3'

import ToolTip from '../tooltip'
import {
  drawLineBetween2Spot,
  generateMapLabel2D,
  generateMapObject3D,
  generateMapSpot,
  getDynamicMapScale,
  drawMapPoint
} from './drawFunc'
import { drawRadar, radarData } from './radar'
import { initScene } from './scene'
import { mapConfig } from './mapConfig'
import { initCamera } from './camera'

let lastPick = null

export default {
  name: 'Map3D',
  components: {
    ToolTip
  },
  props: {
    projectId: {
      type: String,
      default: ''
    },
    geoJson: {
      type: Object,
      required: true
    },
    dblClickFn: {
      type: Function,
      required: true
    },
    projectionFnParam: {
      type: Object,
      required: true,
      validator: prop => {
        return prop.center && prop.scale
      }
    },
    projectList: {
      type: Array,
      default: () => []
    },
    deviceList: {
      type: Array,
      default: () => []
    },
    gatewayList: {
      type: Array,
      default: () => []
    },
    cameraList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      toolTipData: {
        text: ''
      },
      currentSelectedLabel: null,
      currentSelectedProject: null,
      connectingLines: [],
      pointMarkers: [],
      connectionLines: [],
      rightLabelContainer: null,
      labelContainers: []
    }
  },
  render(h) {
    return h('div', {
      style: {
        width: '100%',
        height: '100%'
      }
    }, [
      h('div', {
        ref: 'mapContainer',
        style: {
          width: '100%',
          height: '100%'
        }
      }),
      h('div', {
        ref: 'map2dContainer',
        style: {
          width: '100%',
          height: '100%'
        }
      }),
      h('div', {
        ref: 'toolTip',
        style: {
          display: 'none',
          position: 'absolute',
          backgroundColor: 'rgba(25, 31, 45, 0.9)',
          color: '#fff',
          padding: '10px',
          borderRadius: '6px',
          fontFamily: 'Arial, sans-serif',
          fontSize: '14px',
          boxShadow: '0 3px 10px rgba(0, 0, 0, 0.25)',
          maxWidth: '200px',
          zIndex: 9999,
          pointerEvents: 'none',
          whiteSpace: 'pre-line' // 保留换行符
        }
      }, [
        h('tool-tip', {
          props: {
            text: this.toolTipData.text
          }
        })
      ])
    ])
  },
  mounted() {
    // 确保DOM加载完成后再初始化地图
    this.$nextTick(() => {
      console.log('开始初始化3D地图...');
      this.initMap();
      // 添加地图标签样式
      // this.addMapLabelStyles();
      console.log('3D地图初始化完成');
    });
  },
  methods: {
    /**
     * 检查WebGL是否可用
     * @returns {boolean} 是否支持WebGL
     */
    isWebGLAvailable() {
      try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && 
          (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
      } catch (e) {
        console.error('WebGL检测失败:', e);
        return false;
      }
    },
    /**
     * 添加地图标签样式
     */
    addMapLabelStyles() {
      // 删除可能存在的旧样式
      const existingStyle = document.getElementById('map3d-label-styles');
      if (existingStyle) {
        existingStyle.remove();
      }

      // 创建状态颜色配置
      const statusColors = {
        'in-progress': {
          background: 'linear-gradient(135deg, rgba(64, 193, 255, 0.85), rgba(43, 132, 255, 0.95))',
          border: '1px solid rgba(100, 217, 255, 0.8)',
          glow: '0 0 12px rgba(64, 193, 255, 0.7)'
        },
        'completed': {
          background: 'linear-gradient(135deg, rgba(73, 220, 147, 0.85), rgba(43, 180, 113, 0.95))',
          border: '1px solid rgba(100, 255, 162, 0.8)',
          glow: '0 0 12px rgba(73, 220, 147, 0.7)'
        },
        'paused': {
          background: 'linear-gradient(135deg, rgba(255, 173, 64, 0.85), rgba(255, 132, 43, 0.95))',
          border: '1px solid rgba(255, 200, 100, 0.8)',
          glow: '0 0 12px rgba(255, 173, 64, 0.7)'
        },
        'preparing': {
          background: 'linear-gradient(135deg, rgba(173, 173, 173, 0.85), rgba(132, 132, 132, 0.95))',
          border: '1px solid rgba(200, 200, 200, 0.8)',
          glow: '0 0 12px rgba(173, 173, 173, 0.7)'
        }
      };

      // 创建样式元素
      const style = document.createElement('style');
      style.id = 'map3d-label-styles';
      style.textContent = `
        .map-label {
          color: white;
          padding: 8px 12px;
          border-radius: 6px;
          font-family: 'Arial', sans-serif;
          font-size: 13px;
          font-weight: 500;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          user-select: none;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(4px);
          transform-origin: center center;
          max-width: 140px;
          min-width: 90px;
          pointer-events: auto;
          z-index: 1000;
          position: absolute;
          transform: translate(-50%, -50%);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          /* 确保标签有适当的深度和立体感 */
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
          /* 添加边框以增强可见度 */
          border: 2px solid rgba(255, 255, 255, 0.7);
        }

        .map-label .project-name {
          margin-bottom: 4px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          font-size: 12px;
          /* 增加字母间距以提高可读性 */
          letter-spacing: 0.5px;
        }

        .map-label .label-icon {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 6px;
          display: inline-block;
          /* 添加轻微发光效果 */
          box-shadow: 0 0 4px rgba(255, 255, 255, 0.7);
        }
        
        .map-label:hover {
          transform: translate(-50%, -50%) scale(1.15);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
          z-index: 1001;
        }
        
        .map-label.clicked {
          transform: translate(-50%, -50%) scale(1.2);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
        }

        /* 状态样式 */
        .map-label.status-in-progress {
          background: ${statusColors['in-progress'].background};
          border: ${statusColors['in-progress'].border};
          box-shadow: ${statusColors['in-progress'].glow};
        }
        .map-label.status-in-progress .label-icon {
          background-color: #40C1FF;
        }

        .map-label.status-completed {
          background: ${statusColors['completed'].background};
          border: ${statusColors['completed'].border};
          box-shadow: ${statusColors['completed'].glow};
        }
        .map-label.status-completed .label-icon {
          background-color: #49DC93;
        }

        .map-label.status-paused {
          background: ${statusColors['paused'].background};
          border: ${statusColors['paused'].border};
          box-shadow: ${statusColors['paused'].glow};
        }
        .map-label.status-paused .label-icon {
          background-color: #FFAD40;
        }

        .map-label.status-preparing {
          background: ${statusColors['preparing'].background};
          border: ${statusColors['preparing'].border};
          box-shadow: ${statusColors['preparing'].glow};
        }
        .map-label.status-preparing .label-icon {
          background-color: #ADADAD;
        }

        @keyframes labelAppear {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }

        .map-label {
          animation: labelAppear 0.3s ease-out;
        }
        
        /* 添加标签位置指示器 - 小三角形指向实际位置 */
        .map-label::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 8px solid rgba(255, 255, 255, 0.7);
          filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.3));
        }
      `;

      // 将样式添加到文档头
      document.head.appendChild(style);
      
      console.log('已添加地图标签样式');
    },
    /**
     * 为特定状态生成样式
     */
    generateStyleFromStatus(status, statusColors) {
      const colorInfo = statusColors[status];
      if (!colorInfo) return '';
      
      return `
        background: ${colorInfo.background};
        border: ${colorInfo.border};
      `;
    },
    initMap(props = {}) {
      const currentDom = this.$refs.mapContainer
      if (!currentDom) return
      
      console.log('Map3D 初始化地图，项目数量：', this.projectList ? this.projectList.length : 0)
      
      const ratio = {
        value: 0,
      }
      
      /**
       * 初始化场景
       */
      const scene = initScene()
      // 保存场景引用以便后续使用
      this.scene = scene
      
      // 清理旧的渲染器和事件侦听器
      if (this._renderer) {
        this._renderer.dispose()
        this._renderer = null
      }
      
      if (this._labelRenderer) {
        // 清理标签渲染器
        this._labelRenderer = null
      }
      
      if (this._cleanup) {
        this._cleanup()
      }


      /**
       * 初始化摄像机
       */
      const { camera } = initCamera(currentDom)
      // 保存相机引用以便后续使用
      this.camera = camera
      
      // 不再需要初始化标签容器
      // this.initLabelContainers()
      
      /**
       * 初始化渲染器
       */
      // 首先检查WebGL是否可用
      const webGLAvailable = this.isWebGLAvailable();
      
      let renderer;
      
      if (!webGLAvailable) {
        console.warn('WebGL不可用，使用备选渲染方式');
        // 显示WebGL不可用的错误消息
        this.$emit('webgl-error', {
          message: 'WebGL不可用，地图3D功能将受限',
          details: '您的浏览器可能不支持WebGL或WebGL已被禁用'
        });
        
        // 创建一个虚拟渲染器，避免组件崩溃
        renderer = {
          setSize: () => {},
          setClearColor: () => {},
          render: () => {},
          domElement: document.createElement('div'),
          setPixelRatio: () => {},
          shadowMap: { enabled: false, type: null },
          dispose: () => {}
        };
      } else {
        try {
          console.log('尝试初始化WebGL渲染器...');
          renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true, // 启用透明背景
            powerPreference: 'high-performance', // 请求高性能WebGL上下文
            precision: 'highp', // 高精度渲染
            preserveDrawingBuffer: false // 提高性能
          });
          
          renderer.setSize(currentDom.clientWidth, currentDom.clientHeight);
          renderer.setClearColor(0x000000, 0); // 确保渲染器背景透明
          renderer.setPixelRatio(window.devicePixelRatio);
          
          // 启用阴影
          renderer.shadowMap.enabled = true;
          renderer.shadowMap.type = THREE.PCFSoftShadowMap;
          
          console.log('WebGL渲染器初始化成功');
        } catch (error) {
          console.error('THREE.WebGLRenderer初始化失败:', error);
          // 显示WebGL初始化失败的错误消息
          this.$emit('webgl-error', {
            message: 'WebGL渲染器初始化失败',
            details: error.message || '创建WebGL上下文时出错'
          });
          
          // 创建一个虚拟渲染器，避免组件崩溃
          renderer = {
            setSize: () => {},
            setClearColor: () => {},
            render: () => {},
            domElement: document.createElement('div'),
            setPixelRatio: () => {},
            shadowMap: { enabled: false, type: null },
            dispose: () => {}
          };
        }
      }
  
      // 保存渲染器引用以便后续清理
      this._renderer = renderer
      
      // 只在WebGL可用时才操作DOM
      if (webGLAvailable && renderer.domElement instanceof HTMLCanvasElement) {
        if (currentDom.childNodes.length > 0 && currentDom.childNodes[0] !== renderer.domElement) {
          // 清空容器
          while (currentDom.firstChild) {
            currentDom.removeChild(currentDom.firstChild);
          }
        }
        
        if (!currentDom.contains(renderer.domElement)) {
          currentDom.appendChild(renderer.domElement);
        }
        
        console.log('WebGL渲染器初始化成功，已添加到DOM');
      } else {
        // 如果WebGL不可用，添加一个包含错误消息的div元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'webgl-error';
        errorDiv.innerHTML = `
          <div style="padding: 20px; background-color: rgba(244, 67, 54, 0.1); border: 1px solid #f44336; border-radius: 4px; text-align: center;">
            <h3 style="color: #f44336; margin-top: 0;">WebGL不可用</h3>
            <p>您的浏览器不支持或已禁用WebGL，3D地图功能无法正常显示。</p>
            <p>请尝试使用最新版Chrome、Firefox或Edge浏览器，并确保已启用硬件加速。</p>
          </div>
        `;
        
        // 清空容器并添加错误消息
        while (currentDom.firstChild) {
          currentDom.removeChild(currentDom.firstChild);
        }
        currentDom.appendChild(errorDiv);
      }

      /**
       * 创建css2 Renderer 渲染器
       */
      let labelRenderer;
     
      // 只在WebGL可用时创建标签渲染器
      if (webGLAvailable) {
        try {
          labelRenderer = new CSS2DRenderer();
          labelRenderer.setSize(currentDom.clientWidth, currentDom.clientHeight);
          labelRenderer.domElement.style.position = 'absolute';
          labelRenderer.domElement.style.top = '0px';
          labelRenderer.domElement.style.left = '0px';
          labelRenderer.domElement.style.pointerEvents = 'auto'; // 允许标签接收事件
          labelRenderer.domElement.style.zIndex = '100'; // 确保CSS2D在其他元素之上
          
          // 保存标签渲染器引用以便后续清理
          this._labelRenderer = labelRenderer;
          
          const labelRendererDom = this.$refs.map2dContainer;
          if (labelRendererDom) {
            // 清空容器
            while (labelRendererDom.firstChild) {
              labelRendererDom.removeChild(labelRendererDom.firstChild);
            }
            labelRendererDom.appendChild(labelRenderer.domElement);
          }
        } catch (error) {
          console.error('CSS2DRenderer初始化失败:', error);
          // 创建一个虚拟标签渲染器
          labelRenderer = {
            setSize: () => {},
            render: () => {},
            domElement: document.createElement('div')
          };
          this._labelRenderer = labelRenderer;
        }
      } else {
        // 创建一个虚拟标签渲染器
        labelRenderer = {
          setSize: () => {},
          render: () => {},
          domElement: document.createElement('div')
        };
        this._labelRenderer = labelRenderer;
      }
      
      // 如果WebGL不可用，提前返回，不继续初始化后续3D相关内容
      if (!webGLAvailable) {
        console.warn('WebGL不可用，跳过3D场景初始化');
        return;
      }

      

      /**
       * 初始化模型（绘制3D模型）
       */
      const { mapObject3D, label2dData } = generateMapObject3D(
        this.geoJson,
        this.projectionFnParam
      )

    
      // 重置label2dData，仅使用项目数据
      let projectLabel2dData = [];
      
      // 直接从projectList生成label2dData，无需使用原始label2dData
      if (this.projectList && this.projectList.length > 0) {
        console.log('从projectList直接生成label2dData:', this.projectList.length);
        
        // 将项目数据转换为label2dData格式
        projectLabel2dData = this.projectList.map(project => {
          // 检查项目是否有经纬度信息
          if (project.longitude && project.latitude) {

            // 使用投影函数转换经纬度到地图坐标
            const projectedPosition = this.projectLatLongToPosition(
              project.longitude, 
              project.latitude, 
              this.projectionFnParam
            );
            
            // 验证投影结果的有效性
            if (!projectedPosition || typeof projectedPosition.x !== 'number' || typeof projectedPosition.y !== 'number') {
              console.warn(`项目 ${project.id} 投影失败:`, projectedPosition);
              return null;
            }
            
            // 记录投影信息，便于调试
            console.log(`项目 ${project.id} 投影结果:`, 
              `经度=${project.longitude}, 纬度=${project.latitude}`,
              `x=${projectedPosition.x}, y=${projectedPosition.y}`
            );
            
            // 根据项目状态确定颜色
            let statusColor = null;
            switch(project.status) {
              case 1:
                statusColor = '#1E88E5'; // 蓝色 - 进行中
                break;
              case 2:
                statusColor = '#43A047'; // 绿色 - 已完成
                break;
              case 3:
                statusColor = '#FB8C00'; // 橙色 - 暂停
                break;
              case 0:
                statusColor = '#8E24AA'; // 紫色 - 准备中
                break;
              default:
                statusColor = '#1E88E5'; // 默认蓝色
            }
            
            // 创建label2dData标准格式的项
            return {
              featureCenterCoord: [projectedPosition.x, projectedPosition.y],
              featureName: project.name || `项目${project.id}`,
              properties: {
                name: project.name || `项目${project.id}`,
                adcode: project.adcode || '000000',
                level: project.level || 'project',
                centroid: [project.longitude, project.latitude],
                status: project.status,
                statusColor: statusColor,
                // yOffset: yOffset // 添加垂直偏移量
              },
              projectData: {
                ...project,
                // 确保ID是数字类型
                id: typeof project.id === 'string' ? parseInt(project.id) : project.id,
                // 确保名称非空
                name: project.name || `项目${project.id}`
              }, // 保存原始项目数据
              statusColor: statusColor, // 在顶层也添加状态颜色，便于访问
              // yOffset: yOffset, // 在顶层也添加垂直偏移量，便于访问
              // connectionPoint: connectionPoint // 添加连接点信息，用于绘制连接线
            };
          }
          return null;
        }).filter(item => item !== null); // 过滤掉无效项
        
        console.log('生成的projectLabel2dData:', projectLabel2dData);
      }
      
      
      scene.add(mapObject3D)

      
      
      // 保存mapObject3D的引用，便于后续操作
      this.mapObject3D = mapObject3D;
      
      console.log('地图3D对象已添加到场景');

      /**
       * 动态地图缩放大小
       */
      const mapScale = getDynamicMapScale(mapObject3D, currentDom)
      
      // 计算地图的边界框
      const box = new THREE.Box3().setFromObject(mapObject3D)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())
      
      // 计算合适的相机位置
      const maxDim = Math.max(size.x, size.y, size.z)
      const fov = camera.fov * (Math.PI / 180)
      let cameraZ = Math.abs(maxDim / Math.tan(fov / 2))
      
      // 设置相机位置
      camera.position.set(center.x, center.y, cameraZ * 1.2)
      camera.lookAt(center)
      
      // 设置地图初始缩放
      const scale = Math.min(
        currentDom.clientWidth / size.x,
        currentDom.clientHeight / size.y
      ) * 0.2 // 添加一些边距
      mapObject3D.scale.set(scale, scale, scale)


      /**
       * 绘制 2D 面板
       */
      // 在调用generateMapLabel2D之前检查数据
      if (!projectLabel2dData || projectLabel2dData.length === 0) {
         projectLabel2dData = [];
      }
      
      // 确保projectLabel2dData是数组类型
      if (!Array.isArray(projectLabel2dData)) {
        console.error('projectLabel2dData不是数组类型');
        projectLabel2dData = [];
      }
      
      // 打印每个项目数据，用于调试
      if (projectLabel2dData.length > 0) {
        console.log('准备生成标签的项目数据:');
        projectLabel2dData.forEach((item, index) => {
          console.log(`项目 #${index}: `, {
            name: item.featureName,
            position: item.featureCenterCoord,
            status: item.projectData ? item.projectData.status : 'unknown'
          });
        });
      }
    
      
      // 只在有有效数据时生成标签
      let labelObject2D = new THREE.Object3D(); // 默认创建空对象
      if (projectLabel2dData.length > 0) {
        console.log('即将生成标签，数据数量:', projectLabel2dData.length);
        labelObject2D = generateMapLabel2D(projectLabel2dData);
        console.log('生成的标签对象:', labelObject2D);
      } else {
        console.warn('跳过标签生成，没有有效数据');
      }

    
      
      // 确保标签被添加到地图对象
      // mapObject3D.add(labelObject2D);
      
      /**
       * 绘制点位
       */
      // 只在有数据时绘制点位
      let spotList = [], spotObject3D = new THREE.Object3D();
      if (projectLabel2dData && projectLabel2dData.length > 0) {
        const spotResult = generateMapSpot(projectLabel2dData);
        spotList = spotResult.spotList;
        spotObject3D = spotResult.spotObject3D;
      }
      mapObject3D.add(spotObject3D)

      /**
       * 模型加载
       */
      const modelObject3D = new THREE.Object3D()
      let modelMixer = []
      const loader = new GLTFLoader()
      const dracoLoader = new DRACOLoader()
      dracoLoader.setDecoderPath('/draco/')
      loader.setDRACOLoader(dracoLoader)

      // 只在有项目数据时加载模型
      if (projectLabel2dData && projectLabel2dData.length > 0) {
        loader.load('/models/cone.glb', (glb) => {
          projectLabel2dData.forEach((item) => {
            const { featureCenterCoord, projectData } = item
            
            // 跳过无效数据
            if (!featureCenterCoord || !Array.isArray(featureCenterCoord) || featureCenterCoord.length < 2) {
              console.warn('无效的坐标数据:', featureCenterCoord);
              return;
            }
            
            const clonedModel = glb.scene.clone()
            
            try {
              const mixer = new THREE.AnimationMixer(clonedModel)
              const clonedAnimations = glb.animations.map((clip) => {
                return clip.clone()
              })
              clonedAnimations.forEach((clip) => {
                mixer.clipAction(clip).play()
              })
  
              // 添加每个model的mixer
              modelMixer.push(mixer)
            } catch (error) {
              console.error('创建动画混合器错误:', error);
              // 继续执行，忽略动画
            }

            // 设置模型位置
            clonedModel.position.set(
              featureCenterCoord[0],
              -featureCenterCoord[1],
              mapConfig.spotZIndex
            )
            
            // 设置模型大小 - 根据项目状态调整大小
            let scaleSize = 0.3;
            if (projectData && projectData.status) {
              switch(projectData.status) {
                case 'in-progress':
                  scaleSize = 0.35; // 进行中的项目模型稍大
                  break;
                case 'completed':
                  scaleSize = 0.3;
                  break;
                case 'paused':
                  scaleSize = 0.25;
                  break;
                case 'preparing':
                  scaleSize = 0.27;
                  break;
              }
            }
            
            clonedModel.scale.set(scaleSize, scaleSize, scaleSize * 2)
            
            // 将项目数据附加到模型上
            clonedModel.userData = {
              projectData: projectData
            }
            
            // 确保模型能被点击
            clonedModel.traverse(function(object) {
              if (object.isMesh) {
                object.userData.clickable = true;
                
                // 设置纹理参数，避免"GL_INVALID_OPERATION: Texture is immutable"错误
                if (object.material && object.material.map) {
                  // 在纹理加载前设置参数，避免纹理变为不可变
                  if (!object.material.map.isTexture) {
                    // 可能是纹理加载器，等待完成
                    if (typeof object.material.map.then === 'function') {
                      object.material.map.then(texture => {
                        texture.generateMipmaps = false;
                        texture.minFilter = THREE.LinearFilter;
                        texture.magFilter = THREE.LinearFilter;
                        texture.needsUpdate = true;
                      });
                    }
                  } else {
                    // 对当前纹理对象直接设置参数
                    object.material.map.generateMipmaps = false;
                    object.material.map.minFilter = THREE.LinearFilter;
                    object.material.map.magFilter = THREE.LinearFilter;
                    
                    // 创建新的纹理副本（如果原始纹理已经不可变）
                    if (object.material.map.image) {
                      try {
                        const newTexture = object.material.map.clone();
                        newTexture.generateMipmaps = false;
                        newTexture.minFilter = THREE.LinearFilter;
                        newTexture.magFilter = THREE.LinearFilter;
                        newTexture.needsUpdate = true;
                        object.material.map = newTexture;
                      } catch (e) {
                        console.warn('无法克隆纹理:', e);
                      }
                    } else {
                      object.material.map.needsUpdate = true;
                    }
                  }
                  
                  // 创建新材质以确保纹理参数被正确应用
                  const oldMaterial = object.material;
                  const newMaterial = oldMaterial.clone();
                  
                  // 确保新材质使用正确的纹理参数
                  if (newMaterial.map) {
                    newMaterial.map.generateMipmaps = false;
                    newMaterial.map.minFilter = THREE.LinearFilter;
                    newMaterial.map.magFilter = THREE.LinearFilter;
                    newMaterial.map.needsUpdate = true;
                  }
                  
                  // 替换材质
                  object.material = newMaterial;
                  
                  // 清理旧材质
                  if (oldMaterial !== newMaterial) {
                    oldMaterial.dispose();
                  }
                }
                
                // 优化材质
                if (object.material) {
                  // 使用低精度的着色器以提高性能
                  object.material.precision = 'lowp';
                  
                  // 禁用不必要的渲染特性
                  object.material.fog = false;
                  
                  // 使用低精度的灯光
                  if (object.material.type.includes('Phong') || object.material.type.includes('Lambert')) {
                    object.material.shininess = object.material.shininess || 5;
                    object.material.flatShading = true;
                  }
                }
              }
            });
            
            // modelObject3D.add(clonedModel)
          })

          // mapObject3D.add(modelObject3D)
        }, 
        // 添加加载进度回调
        (progress) => {
          // 可以在这里添加加载进度指示
          if (progress.lengthComputable) {
            const percentComplete = Math.round((progress.loaded / progress.total) * 100);
            console.log(`模型加载进度: ${percentComplete}%`);
          }
        },
        // 添加错误处理
        (error) => {
          console.error('模型加载失败:', error);
        })
      }

      /**
       * 绘制雷达
       */
      // 对雷达数据进行防错处理
      if (radarData && radarData.length > 0) {
        radarData.forEach((item) => {
          try {
            const planeMesh = drawRadar(item, ratio)
            scene.add(planeMesh)
          } catch (error) {
            console.error('绘制雷达错误:', error);
          }
        })
      }

      /**
       * 初始化 CameraHelper - 已禁用
       */
      // 不添加相机辅助线，保持地图视图简洁
      // scene.add(cameraHelper)

      /**
       * 初始化 AxesHelper - 已禁用
       */
      // 不添加坐标轴辅助线，保持地图视图简洁
      // const axesHelper = new THREE.AxesHelper(100)
      // scene.add(axesHelper)

      /**
       * 初始化控制器
       */
      new OrbitControls(camera, labelRenderer.domElement)

      /**
       * 新增光源
       */
      // 移除原有的光源
      scene.children = scene.children.filter(child => !(child instanceof THREE.Light));
      
      // 添加主光源 - 强度更高的点光源
      const mainLight = new THREE.PointLight(0xffffff, 2.5);
      mainLight.position.set(0, 0, 50);
      scene.add(mainLight);
      
      // 添加环境光 - 减弱环境光强度
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
      scene.add(ambientLight);
      
      // 添加方向光 - 来自左上方
      const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0);
      directionalLight1.position.set(-30, 30, 20);
      directionalLight1.castShadow = true;
      scene.add(directionalLight1);
      
      // 添加方向光 - 来自右上方 
      const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight2.position.set(30, -30, 20);
      directionalLight2.castShadow = true;
      scene.add(directionalLight2);
      
      // 启用阴影
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;

      // 光源辅助线 - 已禁用
      // const lightHelper = new THREE.PointLightHelper(mainLight);
      // scene.add(lightHelper);

      // 视窗伸缩
      const onResizeEvent = () => {
        console.log('Map3D组件响应resize事件，当前容器尺寸:', currentDom.clientWidth, 'x', currentDom.clientHeight);
        // 更新摄像头
        camera.aspect = currentDom.clientWidth / currentDom.clientHeight
        // 更新摄像机的投影矩阵
        camera.updateProjectionMatrix()
        // 更新渲染器
        renderer.setSize(currentDom.clientWidth, currentDom.clientHeight)
        // 添加安全检查，确保labelRenderer存在且setSize是一个函数
        if (labelRenderer && typeof labelRenderer.setSize === 'function') {
          try {
            labelRenderer.setSize(currentDom.clientWidth, currentDom.clientHeight)
          } catch (error) {
            console.warn('更新标签渲染器大小时出错:', error);
          }
        }
        // 设置渲染器的像素比例
        renderer.setPixelRatio(window.devicePixelRatio)
        
        // 更新标签位置
        if (this.updateLabelPositions) {
          this.updateLabelPositions();
        }
      }
      
      // 添加窗口大小变化事件监听
      window.addEventListener('resize', onResizeEvent)
      
      // 添加全屏变化事件监听
      document.addEventListener('fullscreenchange', onResizeEvent)
      document.addEventListener('webkitfullscreenchange', onResizeEvent)
      document.addEventListener('mozfullscreenchange', onResizeEvent)
      document.addEventListener('MSFullscreenChange', onResizeEvent)

      /**
       * 设置 raycaster
       */
      const raycaster = new THREE.Raycaster()
      const pointer = new THREE.Vector2()

      // 鼠标移入事件
      const onMouseMoveEvent = (e) => {
        // 更新指针位置
        const rect = currentDom.getBoundingClientRect()
        pointer.x = ((e.clientX - rect.left) / rect.width) * 2 - 1
        pointer.y = -((e.clientY - rect.top) / rect.height) * 2 + 1

        // 更新拾取器
        raycaster.setFromCamera(pointer, camera)

        const intersects = raycaster.intersectObjects(
          mapObject3D.children,
          true
        )

        // 更新鼠标tooltip
        if (intersects.length) {
          const firstModel = intersects.find((item) => {
            return item.object?.parent?.customProperties
          })
          if (firstModel?.object?.parent) {
            const parentModel = firstModel?.object?.parent
            // lastPick 存储最后一次触摸的元素
            if (lastPick !== parentModel) {
              if (lastPick) {
                // 恢复上一个对象的原始颜色
                if (lastPick.customProperties?.statusColor && 
                    lastPick.children && 
                    lastPick.children[0] && 
                    lastPick.children[0].material) {
                  lastPick.children[0].material.color.set(lastPick.customProperties.statusColor)
                }
              }
              lastPick = parentModel
              
              // 高亮当前选中的项目
              if (parentModel.children && 
                  parentModel.children[0] && 
                  parentModel.children[0].material &&
                  parentModel.children[0].material.color) {
                parentModel.children[0].material.color.set(0xff0000) // 设置为红色高亮
              }

              const customProperties = parentModel.customProperties
              if (customProperties) {
                const projectInfo = customProperties.projectData || {}
                // 更详细的tooltip内容
                this.toolTipData = {
                  text: `${customProperties.name || '未命名项目'}\n状态: ${projectInfo.status || '未知'}\n进度: ${projectInfo.progress || 0}%\n点击查看详情`
                }
              }

              // tooltip 展示
              this.$refs.toolTip.style.left = `${e.offsetX}px`
              this.$refs.toolTip.style.top = `${e.offsetY}px`
              this.$refs.toolTip.style.display = 'block'
            }
          }
        } else {
          if (lastPick) {
            // 恢复上一个对象的原始颜色
            if (lastPick.customProperties?.statusColor && 
                lastPick.children && 
                lastPick.children[0] && 
                lastPick.children[0].material) {
              lastPick.children[0].material.color.set(lastPick.customProperties.statusColor)
            }
          }
          lastPick = null
          // 隐藏tooltip
          this.$refs.toolTip.style.display = 'none'
        }
      }

      // 标签点击事件委托处理
      const labelClickHandler = (event) => {
        // 防止事件冒泡
        event.stopPropagation()

        // 获取点击的元素
        const clickedElement = event.target

        // 检查是否点击了标签元素
        const isLabel = clickedElement.classList.contains('map-label') ||
                        clickedElement.parentElement?.classList.contains('map-label')

        // 确定实际的标签元素（不论是直接点击还是点击子元素）
        const labelElement = isLabel ? 
          (clickedElement.classList.contains('map-label') ? clickedElement : clickedElement.parentElement) : 
          null

        console.log('点击事件捕获到元素：', clickedElement, '是否为标签:', isLabel, '标签元素:', labelElement)

        if (isLabel && labelElement) {
          // 显示点击反馈
          this.showClickFeedback(labelElement)

          // 获取项目ID
          const projectId = labelElement.dataset.projectId
          console.log('点击了项目标签, ID:', projectId, '名称:', labelElement.dataset.projectName)

          if (projectId) {
            let projectData = null
            
            // 查找对应的项目数据
            if (this.projectList && this.projectList.length > 0) {
              projectData = this.projectList.find(item => {
                const itemId = typeof item.id === 'string' ? parseInt(item.id, 10) : item.id
                return itemId === parseInt(projectId, 10)
              })
            }

            if (projectData) {
              console.log('找到项目数据：', projectData)
              
              // 如果有自定义双击函数，调用它
              if (typeof this.dblClickFn === 'function') {
                this.dblClickFn(projectData)
              } else {
                // 触发全局项目点击事件
                window.dispatchEvent(new CustomEvent('bridge-project-click', { 
                  detail: { project: projectData }
                }))
              }
            } else {
              console.warn(`未找到ID为${projectId}的项目数据`)
            }
          }
        }
      }
      
      // 保存事件处理器引用，便于后续清理
      this._labelClickHandler = labelClickHandler
      
      // 添加多种事件监听，确保能捕获所有类型的点击
      labelRenderer.domElement.addEventListener('click', labelClickHandler, { passive: true })
      labelRenderer.domElement.addEventListener('mousedown', labelClickHandler, { passive: true })
      
      // 监听自定义标签点击事件
      const customLabelClickHandler = (event) => {
        if (event.detail && event.detail.projectId) {
          const numericProjectId = parseInt(event.detail.projectId, 10)
          
          // 查找对应的项目数据
          const projectData = this.projectList.find(p => {
            const pId = typeof p.id === 'string' ? parseInt(p.id, 10) : Number(p.id) 
            return pId === numericProjectId
          })
          
          if (projectData) {
            console.log('自定义标签点击事件 - 找到项目:', projectData)
            
            // 触发项目点击事件 - 使用dblClickFn直接调用
            if (this.dblClickFn && typeof this.dblClickFn === 'function') {
              console.log('调用dblClickFn处理项目点击:', projectData.id);
              this.dblClickFn(projectData);
            } else {
              // 作为备用，仍然触发全局事件
              console.log('触发全局项目点击事件');
              window.dispatchEvent(
                new CustomEvent('project-click', {
                  detail: { project: projectData }
                })
              );
            }
            
            // 显示点击反馈 - 查找相关标签
            const relatedLabel = document.querySelector(`.map-label[data-project-id="${numericProjectId}"]`)
            if (relatedLabel) {
              this.showClickFeedback(relatedLabel)
            }
          }
        }
      }
      
      // 添加自定义事件监听
      this._customLabelClickHandler = customLabelClickHandler
      labelRenderer.domElement.addEventListener('label-click', customLabelClickHandler, { passive: true })

      // 注册模型点击事件处理
      const onModelClickEvent = (event) => {
        // 如果事件已被处理则退出
        if (event.defaultPrevented) return
        
        // 根据鼠标位置更新射线检测点
        pointer.x = (event.clientX / window.innerWidth) * 2 - 1
        pointer.y = -(event.clientY / window.innerHeight) * 2 + 1
        
        // 射线检测与3D对象的交互
        raycaster.setFromCamera(pointer, camera)
        const intersects = raycaster.intersectObjects(scene.children, true)
        
        if (intersects.length > 0) {
          // 查找第一个有效交叉点
          for (const intersect of intersects) {
            let object = intersect.object
            
            // 尝试查找对象或其父对象上的项目ID
            let projectId = null
            let currentObj = object
            
            // 向上遍历对象层次结构查找项目ID
            while (currentObj && !projectId) {
              if (currentObj.userData && currentObj.userData.projectId) {
                projectId = currentObj.userData.projectId
              } else if (currentObj.userData && currentObj.userData.projectData && currentObj.userData.projectData.id) {
                projectId = currentObj.userData.projectData.id
              } else if (currentObj.name && currentObj.name.includes('project-')) {
                // 尝试从对象名称中提取项目ID
                const match = currentObj.name.match(/project-(\d+)/)
                if (match && match[1]) {
                  projectId = match[1]
                }
              }
              currentObj = currentObj.parent
            }
            
            // 如果找到项目ID，触发点击事件
            if (projectId) {
              const numericProjectId = typeof projectId === 'string' ? parseInt(projectId, 10) : Number(projectId)
              
              // 查找对应的项目数据 - 处理Vue响应式对象
              const projectData = this.projectList.find(p => {
                // 从响应式对象中获取原始ID
                const pId = typeof p.id === 'string' ? parseInt(p.id, 10) : Number(p.id)
                return pId === numericProjectId
              })
              
              if (projectData) {
                console.log('3D模型点击 - 找到项目:', projectData)
                
                // 使用dblClickFn直接调用
                if (this.dblClickFn && typeof this.dblClickFn === 'function') {
                  console.log('调用dblClickFn处理项目点击:', projectData.id);
                  this.dblClickFn(projectData);
                } else {
                  // 作为备用，触发全局事件
                  console.log('触发全局项目点击事件');
                  window.dispatchEvent(
                    new CustomEvent('project-click', {
                      detail: { project: projectData }
                    })
                  );
                }
                
                // 显示反馈 - 如果有关联标签可以突出显示它
                const relatedLabel = document.querySelector(`.map-label[data-project-id="${numericProjectId}"]`)
                if (relatedLabel) {
                  this.showClickFeedback(relatedLabel)
                }
                
                // 终止循环，避免多次触发
                break
              } else {
                console.log('3D模型点击 - 未找到项目数据:', numericProjectId, '可用ID:', this.projectList.map(p => p.id))
              }
            }
          }
        }
      }
      
      // 添加模型点击事件监听
      renderer.domElement.addEventListener('click', onModelClickEvent, { passive: true })
      
      // 工具函数 辅助动画
      const aniSpotList = (spotList, ratio) => {
        spotList.forEach((item) => {
          item.position.z = Math.abs(Math.sin(ratio.value)) * 5
          item.material.opacity = Math.abs(Math.sin(ratio.value * 0.5))
        })
      }

      // 工具函数 辅助飞线动画
      // const aniFlySpotList = (flySpotList, ratio) => {
      //   flySpotList.forEach((item) => {
      //     item.material.uniforms.dashOffset.value = ratio.value
      //   })
      // }
      console.log('**************** 添加事件监听');
      // 添加事件监听
      window.addEventListener('resize', onResizeEvent, { passive: true })
      currentDom.addEventListener('mousemove', onMouseMoveEvent, { passive: true })
      
      // 存储清理函数
      this._cleanup = () => {
        window.removeEventListener('resize', onResizeEvent)
        currentDom.removeEventListener('mousemove', onMouseMoveEvent)
        renderer.domElement.removeEventListener('click', onModelClickEvent)
        
        // 移除标签点击事件处理
        if (this._labelClickHandler && labelRenderer) {
          labelRenderer.domElement.removeEventListener('click', this._labelClickHandler)
          labelRenderer.domElement.removeEventListener('mousedown', this._labelClickHandler)
          this._labelClickHandler = null
        }
        
        // 移除自定义标签点击事件处理
        if (this._customLabelClickHandler && labelRenderer) {
          labelRenderer.domElement.removeEventListener('label-click', this._customLabelClickHandler)
          this._customLabelClickHandler = null
        }
        
        // 清理Three.js资源
        scene.traverse(object => {
          if (object.geometry) {
            object.geometry.dispose()
          }
          
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose())
            } else {
              object.material.dispose()
            }
          }
        })
      }

      // 设置缩放动画
      gsap.to(mapObject3D.scale, {
        x: mapScale,
        y: mapScale,
        z: mapScale,
        duration: 1,
      })

      // 设置渐渐消失动画
      gsap.to(ratio, {
        value: 10,
        duration: 5,
        repeat: -1, // 无限循环
        yoyo: false, // 不来回运动
        // ease: 'power2.inOut', // 差值器，可以理解为补间动画
        onUpdate: () => {
          aniSpotList(spotList, ratio)
          // aniFlySpotList(flySpotList, ratio)
        },
      })

      // 设置时钟
      const clock = new THREE.Clock()
      
      // 优化的渲染函数
      let prevTime = 0;
      const targetFPS = 30; // 目标帧率
      const frameInterval = 1000 / targetFPS;
      let rafId = null;
      
      // 对象池和缓存
      const vector3Pool = Array(10).fill().map(() => new THREE.Vector3());
      let vectorPoolIndex = 0;
      const getVector3 = () => {
        const vec = vector3Pool[vectorPoolIndex];
        vectorPoolIndex = (vectorPoolIndex + 1) % vector3Pool.length;
        return vec;
      };
      
      // 视锥体剔除
      const frustum = new THREE.Frustum();
      const projScreenMatrix = new THREE.Matrix4();
      
      // 保存对this的引用，避免在Promise中丢失上下文
      const self = this;
      
      // 设置优化的动画循环
      const animate = (timestamp) => {
        rafId = requestAnimationFrame(animate);
        
        // 限制帧率
        const elapsed = timestamp - prevTime;
        if (elapsed < frameInterval) return;
        
        // 更新时间戳
        prevTime = timestamp - (elapsed % frameInterval);
        
        // 计算视锥体以便进行视锥体剔除
        projScreenMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
        frustum.setFromProjectionMatrix(projScreenMatrix);
        
        // 优化性能的更新和渲染
        const delta = clock.getDelta();
        
        // 只在需要时更新动画混合器，并且批量更新
        if (modelMixer.length > 0) {
          // 限制每帧更新的混合器数量
          const maxMixersPerFrame = 10;
          const mixersToUpdate = Math.min(modelMixer.length, maxMixersPerFrame);
          
          for (let i = 0; i < mixersToUpdate; i++) {
            modelMixer[i].update(delta);
          }
          
          // 如果有更多混合器，将它们移到数组前面以便下一帧更新
          if (modelMixer.length > maxMixersPerFrame) {
            modelMixer.push(...modelMixer.splice(0, maxMixersPerFrame));
          }
        }
        
        // 使用微任务推迟渲染
        Promise.resolve().then(() => {
          // 只渲染当前视锥体内的对象
          scene.traverse(object => {
            if (object.isMesh && object.userData.frustumCulled !== false) {
              // 使用对象池中的向量计算包围球
              const center = getVector3().copy(object.position);
              object.updateMatrixWorld();
              center.applyMatrix4(object.matrixWorld);
              
              // 对不在视锥体内的对象暂时隐藏
              const isVisible = frustum.containsPoint(center);
              if (object.visible !== isVisible) {
                object.visible = isVisible;
              }
            }
          });
          
          // 更新标签位置
          if (self.updateLabelPositions) {
            self.updateLabelPositions();
          }
          
          // 使用低精度渲染以提高性能
          renderer.render(scene, camera);
          // 只在必要时渲染标签，添加类型检查和错误处理
          if (labelRenderer && typeof labelRenderer.render === 'function') {
            try {
              labelRenderer.render(scene, camera);
            } catch (error) {
              console.warn('渲染标签时出错:', error);
            }
          }
        });
      }
      
      // 开始动画循环
      rafId = requestAnimationFrame(animate);
      
      // 添加地图标签样式
      this.addMapLabelStyles();
      
      // 创建所有项目标签
      this.createAllProjectLabels();
      
      console.log('完成地图初始化，已禁用连接线');
      
      // 保存事件处理函数引用，以便后续清理
      this._onResizeEvent = onResizeEvent;
      
      // 销毁事件
      this.$once('hook:beforeDestroy', () => {
        console.log('Map3D组件销毁，清理事件监听器和资源...');
        
        // 移除窗口大小变化事件监听
        window.removeEventListener('resize', onResizeEvent);
        
        // 移除全屏变化事件监听
        document.removeEventListener('fullscreenchange', onResizeEvent);
        document.removeEventListener('webkitfullscreenchange', onResizeEvent);
        document.removeEventListener('mozfullscreenchange', onResizeEvent);
        document.removeEventListener('MSFullscreenChange', onResizeEvent);
        
        // 停止动画循环
        if (rafId !== null) {
          cancelAnimationFrame(rafId);
          rafId = null;
        }
        
        if (this._cleanup) {
          this._cleanup()
          this._cleanup = null
        }
        
        if (this._renderer) {
          this._renderer.dispose()
          this._renderer = null
        }
        
        // 清理Three.js资源
        scene.traverse(object => {
          if (object.geometry) {
            object.geometry.dispose()
          }
          
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose())
            } else {
              object.material.dispose()
            }
          }
        })
      })

      // 初始化标签相关属性
      this.labelVerticalPositions = {};
      this.labelElements = {};
    },

    /**
     * 设置连接线自动更新器
     */
    setupConnectionLinesUpdater() {
      // 此方法不再需要使用，所有连接线功能已禁用
      console.log('连接线功能已禁用');
      return;
    },

    /**
     * 组件销毁前的清理
     */
    beforeDestroy() {
      console.log('Map3D组件销毁，清理资源...');
      
      // 清理标签位置更新定时器
      if (this._labelPositionUpdateInterval) {
        clearInterval(this._labelPositionUpdateInterval);
        this._labelPositionUpdateInterval = null;
      }
      
      // 清理连接线定时器
      if (this._connectionLinesTimer) {
        clearInterval(this._connectionLinesTimer);
        this._connectionLinesTimer = null;
      }
      
      // 清除所有连接线和DOM点标记
      this.clearConnectingLines();
      
      // 清除所有标签
      this.clearProjectLabels();
      
      // 清理标签渲染器
      if (this._labelRenderer) {
        // 移除标签渲染器的DOM元素
        const labelRendererDom = this._labelRenderer.domElement;
        if (labelRendererDom && labelRendererDom.parentNode) {
          labelRendererDom.parentNode.removeChild(labelRendererDom);
        }
        this._labelRenderer = null;
      }
      
      // 清理主渲染器
      if (this._renderer) {
        // 移除主渲染器的DOM元素
        const rendererDom = this._renderer.domElement;
        if (rendererDom && rendererDom.parentNode) {
          rendererDom.parentNode.removeChild(rendererDom);
        }
        
        this._renderer.dispose();
        this._renderer = null;
      }
      
      // 清理THREE.js中的点位资源
      if (this.projectPoints) {
        // 删除所有点位
        while (this.projectPoints.children.length > 0) {
          const point = this.projectPoints.children[0];
          this.projectPoints.remove(point);
          if (point.geometry) point.geometry.dispose();
          if (point.material) point.material.dispose();
        }
        
        // 从场景中移除点组
        if (this.scene) {
          this.scene.remove(this.projectPoints);
        }
        
        this.projectPoints = null;
      }
      
      // 清理场景中的所有对象
      if (this.scene) {
        // 递归遍历场景中的所有对象并释放资源
        this.scene.traverse(object => {
          if (object.geometry) {
            object.geometry.dispose();
          }
          
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose());
            } else {
              object.material.dispose();
            }
          }
          
          // 移除CSS2D标签对象
          if (object instanceof CSS2DObject) {
            if (object.element && object.element.parentNode) {
              object.element.parentNode.removeChild(object.element);
            }
          }
        });
        
        // 清空场景
        while (this.scene.children.length > 0) {
          this.scene.remove(this.scene.children[0]);
        }
        
        this.scene = null;
      }
      
      // 清理相机
      this.camera = null;
      
      // 移除地图样式
      const mapStyles = document.getElementById('map3d-label-styles');
      if (mapStyles) {
        mapStyles.remove();
      }
      
      console.log('Map3D组件资源清理完成');
    },

    // 将经纬度转换为地图上的位置坐标
    projectLatLongToPosition(longitude, latitude, projectionFnParam) {
      try {
        // 验证输入参数
        longitude = parseFloat(longitude);
        latitude = parseFloat(latitude);
        
        if (isNaN(longitude) || isNaN(latitude)) {
          console.error('无效的经纬度:', longitude, latitude);
          return null;
        }
        
        // 获取投影参数
        if (!projectionFnParam || !projectionFnParam.center || !projectionFnParam.scale) {
          console.error('无效的投影参数:', projectionFnParam);
          return null;
        }
        
        const center = projectionFnParam.center;
        const scale = projectionFnParam.scale;
        
        console.log(`投影参数: 中心=${center}, 比例=${scale}`);
        console.log(`正在投影: 经度=${longitude}, 纬度=${latitude}`);
        
        // 使用d3.geoMercator()进行投影
        const projection = d3.geoMercator()
          .center(center)
          .scale(scale)
          .translate([0, 0]);
        
        // 执行投影
        const [x, y] = projection([longitude, latitude]);
        
        if (isNaN(x) || isNaN(y)) {
          console.error('投影结果无效:', x, y);
          return null;
        }
        
        console.log(`投影结果: x=${x}, y=${y}`);
        
        return { x, y };
      } catch (error) {
        console.error('投影过程出错:', error);
        return null;
      }
    },
    
    // 显示标签点击反馈效果
    showClickFeedback(element) {
      // 如果不是有效元素则退出
      if (!element || !element.classList) return;
      
      // 记录当前选中的标签
      if (this.currentSelectedLabel && this.currentSelectedLabel !== element) {
        // 移除之前选中标签的高亮
        this.currentSelectedLabel.classList.remove('clicked');
      }
      
      // 添加高亮效果
      element.classList.add('clicked');
      
      // 标签突出显示 - 添加明亮边框和光晕效果
      element.style.borderColor = 'rgba(0, 220, 255, 1)';
      element.style.backgroundColor = 'rgba(0, 60, 120, 0.95)';
      element.style.boxShadow = '0 0 20px rgba(0, 200, 255, 0.8)';
      element.style.transform = 'scale(1.1)';
      element.style.zIndex = '1000';
      
      // 保存当前选中的标签
      this.currentSelectedLabel = element;
      
      // 如果有项目ID，找出并记录项目数据
      const projectId = element.dataset.projectId;
      if (projectId) {
        const numericId = parseInt(projectId, 10);
        const project = this.projectList.find(p => {
          const pId = typeof p.id === 'string' ? parseInt(p.id, 10) : p.id;
          return pId === numericId;
        });
        
        if (project) {
          console.log('当前选中项目:', project.name, project);
          // 存储当前选中的项目数据
          this.currentSelectedProject = project;
          
          // 打印项目详细信息
          console.log(`项目详情: 
            名称: ${project.name}
            状态: ${project.status}
            进度: ${project.progress || 0}%
            地点: ${project.location || '未知'}`
          );
        }
      }
      
      // 设置定时器，超时后移除高亮效果（保持1秒）
      setTimeout(() => {
        if (element && element.classList) {
          // 不移除clicked类，只恢复正常样式
          element.style.boxShadow = '';
          element.style.backgroundColor = '';
          element.style.borderColor = '';
          element.style.transform = '';
          
          // 如果不再是当前选中，则完全移除高亮
          if (this.currentSelectedLabel !== element) {
            element.classList.remove('clicked');
          }
        }
      }, 1000);
    },
    /**
     * 绘制2D标签
     * @param {Array} data - 要绘制的数据
     * @param {String} className - 标签的CSS类名
     */
    draw2dLabel(data, className = 'map-label') {
      // 移除之前的标签
      document.querySelectorAll(`.${className}`).forEach(el => {
        el.remove();
      });

      // 移除先前创建的连接线
      document.querySelectorAll('.map-connection-line').forEach(line => {
        line.remove();
      });

      if (!data || !data.length) return;

      const container = document.querySelector('.amap-layers');
      if (!container) return;

      data.forEach(item => {
        if (!item.centroid) return;

        try {
          // 创建标签元素
          const label = document.createElement('div');
          label.className = className;
          
          // 设置数据属性，用于事件处理
          if (item.id) {
            label.dataset.id = item.id;
          }
          // 确保项目ID被设置
          if (item.projectId) {
            label.dataset.projectId = item.projectId;
          }
          if (item.name) {
            label.dataset.name = item.name;
          }
          if (item.adcode) {
            label.dataset.adcode = item.adcode;
          }
          
          // 添加标签文本
          label.textContent = item.name || '';

          // 获取经纬度坐标并转换为像素坐标
          const lnglat = new AMap.LngLat(item.centroid[0], item.centroid[1]);
          const pixel = this.map.lngLatToContainer(lnglat);

          // 设置标签位置
          label.style.position = 'absolute';
          label.style.left = `${pixel.x}px`;
          label.style.top = `${pixel.y}px`;
          label.style.transform = 'translate(-50%, -50%)';

          // 将标签添加到容器
          container.appendChild(label);
          
          // 注：已移除连接线创建代码
        } catch (error) {
          console.error('绘制标签时出错:', error, item);
        }
      });
      
      console.log(`已绘制 ${data.length} 个标签，不含连接线`);
    },
    /**
     * 初始化标签容器，包括标准的CSS2D容器和右侧固定容器
     */
    initLabelContainers() {
      // 右侧标签容器不再需要，因为标签将直接显示在地图坐标上
      // 移除现有的容器（如果存在）
      if (this.rightLabelContainer) {
        if (document.body.contains(this.rightLabelContainer)) {
          document.body.removeChild(this.rightLabelContainer);
        }
        this.rightLabelContainer = null;
      }

      // 移除页面上可能存在的容器
      const existingContainer = document.querySelector('.label-right-container');
      if (existingContainer) {
        existingContainer.parentNode.removeChild(existingContainer);
      }
      
      console.log('标签容器初始化完成 - 标签将直接显示在地图上');
    },
    
    /**
     * 处理右侧标签点击事件
     * @param {Event} event - 点击事件
     */
    rightLabelClickHandler(event) {
      // 查找最近的带有projectId的元素
      let target = event.target;
      let projectId = null;
      
      while (target && !projectId && target !== this.rightLabelContainer) {
        projectId = target.dataset.projectId;
        if (!projectId) {
          target = target.parentElement;
        }
      }
      
      if (projectId) {
        // 移除已有的高亮
        const allLabels = this.rightLabelContainer.querySelectorAll('.map-label-container');
        allLabels.forEach(label => {
          label.classList.remove('clicked');
        });
        
        // 添加高亮到当前标签
        if (target.classList.contains('map-label')) {
          target.parentElement.classList.add('clicked');
        } else if (target.classList.contains('map-label-container')) {
          target.classList.add('clicked');
        }
        
        // 触发全局事件
        const event = new CustomEvent('project-label-click', {
          detail: {
            projectId: projectId
          }
        });
        window.dispatchEvent(event);
        
        // 延迟移除高亮
        setTimeout(() => {
          const clickedLabel = this.rightLabelContainer.querySelector('.map-label-container.clicked');
          if (clickedLabel) {
            clickedLabel.classList.remove('clicked');
          }
        }, 1000);
      }
    },
    
    /**
     * 生成项目标签数据
     * @param {Object} project - 项目数据
     * @returns {Object|null} 标签数据或null（如果数据不完整）
     */
    generateLabelData(project) {
      // ... existing code ...
      
      // 处理返回数据（删除垂直位置属性）
      const labelData = {
        id: project.id,
        name: project.name || '未命名项目',
        status: status,
        position: {
          x: projectedPosition.x,
          y: projectedPosition.y,
          z: mapConfig.labelZIndex || 2.0
        }
      };
      
      return labelData;
    },
    
    /**
     * 添加项目标签到地图
     * @param {Object} project - 项目数据
     */
    addProjectLabel(project) {
      // 创建项目标签
      const labelElement = this.createProjectLabel(project);
      
      // 确保标签渲染器更新
      if (this._labelRenderer && this.scene && this.camera) {
        this._labelRenderer.render(this.scene, this.camera);
      }
      
      return labelElement;
    },
    
    /**
     * 清除所有项目标签
     */
    clearAllProjectLabels() {
      // Call the new method to clear project labels
      this.clearProjectLabels();
    },
    
    /**
     * 更新场景，在动画循环中调用
     */
    updateScene() {
      // ... existing code ...
      
      // 更新标签位置，包括连接线
      this.updateLabelPositions();
      
      // ... existing code ...
    },
    
    /**
     * 清除项目标签
     */
    clearProjectLabels() {
      console.log('清除所有项目标签...');
      
      // 清除场景中的标签对象
      if (this.labelObjects) {
        this.labelObjects.forEach((labelObject, projectId) => {
          if (labelObject && labelObject.parent) {
            labelObject.parent.remove(labelObject);
          }
        });
        this.labelObjects.clear();
      }

      // 清除DOM中的标签元素
      const labels = document.querySelectorAll('.map-label');
      labels.forEach(label => {
        if (label && label.parentNode) {
          label.parentNode.removeChild(label);
        }
      });

      // 重置标签相关数据
      this.labelVerticalPositions = {};
      this.labelElements = {};
      
      // 清除连接线和点标记
      if (typeof this.clearConnectingLines === 'function') {
        this.clearConnectingLines();
      }
      
      console.log('项目标签已清除');
    },
    
    /**
     * 更新标签位置
     */
    updateLabelPositions() {
      if (!this.labelObjects || !this.camera) return;

      this.labelObjects.forEach((labelObject, projectId) => {
        if (labelObject && labelObject.element) {
          try {
            // 获取标签在屏幕上的位置
            const position = labelObject.position.clone();
            
            // 计算到相机的距离，用于缩放和透明度调整
            const distanceToCamera = this.camera.position.distanceTo(position);
            
            // 检查标签是否需要根据地图比例更新位置
            if (this.mapObject3D && labelObject.userData && labelObject.userData.originalPosition) {
              const currentMapScale = this.mapObject3D.scale.x;
              const appliedScale = labelObject.userData.appliedScale || 1;
              
              // 如果地图比例已经改变，重新设置标签位置
              if (Math.abs(currentMapScale - appliedScale) > 0.001) {
                const originalPos = labelObject.userData.originalPosition;
                labelObject.position.set(
                  originalPos.x * currentMapScale,
                  -originalPos.y * currentMapScale,
                  labelObject.position.z
                );
                
                // 更新已应用的比例
                labelObject.userData.appliedScale = currentMapScale;
                console.log(`更新标签位置比例: ${projectId}, 从 ${appliedScale} 到 ${currentMapScale}`);
              }
            }
            
            // 投影到屏幕空间
            const projectedPosition = position.clone().project(this.camera);

            // 检查标签是否在视锥体内（略微扩大可见区域，避免标签突然消失）
            const isVisible = 
              projectedPosition.x >= -1.2 && projectedPosition.x <= 1.2 &&
              projectedPosition.y >= -1.2 && projectedPosition.y <= 1.2 &&
              projectedPosition.z >= -1;

            // 更新标签可见性
            labelObject.element.style.display = isVisible ? 'block' : 'none';

            // 根据距离调整标签大小和透明度
            if (isVisible) {
              // 根据到相机的距离计算缩放因子
              // 距离越远，标签越小，但保持在可读范围内
              const maxDistance = 1000; // 可调整的最大距离参考值
              const minScale = 0.7;   // 最小缩放
              const maxScale = 1.2;   // 最大缩放
              
              // 计算缩放因子 - 使用平滑的缩放曲线
              const distanceFactor = Math.min(distanceToCamera / maxDistance, 1);
              const scale = maxScale - (maxScale - minScale) * distanceFactor;
              
              // 计算透明度 - 距离越远越透明，但保持可见度
              const opacity = Math.max(0.6, 1 - distanceFactor * 0.4);
              
              // 应用样式变换
              labelObject.element.style.transform = `translate(-50%, -50%) scale(${scale})`;
              labelObject.element.style.opacity = opacity;
              
              // 确保重要标签始终可见 (可选：根据项目状态或其他条件)
              if (labelObject.userData && labelObject.userData.projectData) {
                const project = labelObject.userData.projectData;
                // 如果是重要项目（例如进行中的项目），确保标签更突出
                if (project.status === 1) { // 进行中的项目
                  labelObject.element.style.zIndex = '1000';
                  labelObject.element.style.opacity = Math.max(0.85, opacity);
                }
              }
            }
          } catch (error) {
            console.error(`更新标签位置出错, projectId=${projectId}:`, error);
          }
        }
      });

      // 确保标签渲染器更新
      if (this._labelRenderer && this.scene && this.camera) {
        this._labelRenderer.render(this.scene, this.camera);
      }
    },
    /**
     * 为单个项目创建标签
     * @param {Object} project - 项目数据
     * @returns {HTMLElement} 创建的标签元素
     */
    createProjectLabel(project) {
      if (!this.scene || !this.camera) {
        console.warn('场景或相机未初始化');
        return null;
      }

      try {
        const projectId = typeof project.id === 'string' ? parseInt(project.id, 10) : project.id;
        
        // 检查是否已存在此项目的标签对象
        if (this.labelObjects && this.labelObjects.has(projectId)) {
          console.log(`项目标签已存在: ${projectId}`);
          return this.labelObjects.get(projectId).element;
        }

        // 检查项目数据是否完整
        if (!project.longitude || !project.latitude) {
          console.warn(`项目 ${projectId} 缺少经纬度信息`);
          return null;
        }

        // 计算标签位置
        const position = this.projectLatLongToPosition(project.longitude, project.latitude, this.projectionFnParam);
        if (!position || (position.x === 0 && position.y === 0)) {
          console.warn(`项目 ${projectId} 位置计算失败`);
          return null;
        }
        
        // 创建标签元素
        const label = document.createElement('div');
        label.className = `map-label status-${this.getProjectStatus(project.status)}`;
        label.dataset.projectId = projectId.toString();
        label.dataset.projectName = project.name || `项目${projectId}`;
        
        // 添加项目名称
        const nameSpan = document.createElement('span');
        nameSpan.className = 'project-name';
        nameSpan.textContent = project.name || `项目${projectId}`;
        label.appendChild(nameSpan);
        
        // 添加状态图标
        const statusIcon = document.createElement('span');
        statusIcon.className = 'label-icon';
        label.insertBefore(statusIcon, nameSpan);

        // 创建CSS2D对象并设置位置 - 直接使用计算的位置坐标
        const labelObject = new CSS2DObject(label);
        
        // 获取地图对象的当前缩放比例，确保标签位置与地图比例一致
        const mapScale = this.mapObject3D ? this.mapObject3D.scale.x : 1;
        
        // 使用正确的坐标系位置 - 使用x和-y保持与地图一致的坐标系
        // 应用相同的缩放比例，使标签和地图对象保持一致
        labelObject.position.set(
          position.x * mapScale, 
          -position.y * mapScale, 
          mapConfig.labelZIndex || 5.0
        );
        
        // 保存原始位置以便后续参考
        labelObject.userData = {
          originalPosition: { x: position.x, y: position.y },
          projectData: project,
          appliedScale: mapScale // 记录应用的缩放比例
        };
        
        // 添加点击事件监听
        label.addEventListener('click', (event) => {
          event.stopPropagation();
          this.showClickFeedback(label);
          
          if (this.dblClickFn && typeof this.dblClickFn === 'function') {
            this.dblClickFn(project);
          }
        });

        // 将标签添加到场景
        this.scene.add(labelObject);
        
        // 存储标签引用
        if (!this.labelObjects) {
          this.labelObjects = new Map();
        }
        this.labelObjects.set(projectId, labelObject);
        
        // 确保标签渲染器更新
        if (this._labelRenderer) {
          this._labelRenderer.render(this.scene, this.camera);
        }

        console.log(`已创建项目标签: ${projectId}, 位置: x=${position.x}, y=${position.y}, 应用缩放: ${mapScale}`);
        return label;
      } catch (error) {
        console.error(`为项目 ${project.id} 创建标签时出错:`, error);
        return null;
      }
    },
    
    /**
     * 辅助方法：获取项目状态类名
     */
    getProjectStatus(status) {
      switch(Number(status)) {
        case 1: return 'in-progress';
        case 2: return 'completed';
        case 3: return 'paused';
        default: return 'preparing';
      }
    },
    
    /**
     * 创建所有项目标签
     */
    createAllProjectLabels() {
      // 清除现有标签
      this.clearProjectLabels();
      
      // 检查项目列表
      if (!this.projectList || !this.projectList.length) {
        console.warn('没有项目数据可用于创建标签');
        return;
      }
      
      console.log(`开始创建 ${this.projectList.length} 个项目标签`);
      
      // 检查场景和相机是否已初始化
      if (!this.scene || !this.camera) {
        console.error('场景或相机未初始化，无法创建标签');
        return;
      }
      
      // 检查渲染器是否已初始化
      if (!this._labelRenderer) {
        console.error('标签渲染器未初始化，无法创建标签');
        return;
      }
      
      // 检查投影参数是否有效
      if (!this.projectionFnParam || !this.projectionFnParam.center || !this.projectionFnParam.scale) {
        console.error('投影参数无效，无法创建标签', this.projectionFnParam);
        return;
      }
      
      // 检查地图对象是否已经创建
      if (!this.mapObject3D) {
        console.error('地图3D对象未初始化，无法创建标签');
        return;
      }
      
      // 初始化标签对象存储
      if (!this.labelObjects) {
        this.labelObjects = new Map();
      }
      
      // 添加或确保已添加标签样式
      this.addMapLabelStyles();
      
      // 已创建标签的计数
      let createdCount = 0;
      let errorCount = 0;
      
      // 创建缓存来避免标签重叠
      const positionCache = new Map();
      const minDistanceThreshold = 20; // 最小标签间距（像素单位）
      
      console.log('当前地图比例:', this.mapObject3D.scale.x);
      
      // 为每个项目创建标签
      this.projectList.forEach(project => {
        try {
          if (project && project.id && project.longitude && project.latitude) {
            // 验证经纬度的有效性
            const longitude = parseFloat(project.longitude);
            const latitude = parseFloat(project.latitude);
            
            if (isNaN(longitude) || isNaN(latitude) || 
                longitude < -180 || longitude > 180 || 
                latitude < -90 || latitude > 90) {
              console.warn(`项目 ${project.id} 经纬度无效: 经度=${longitude}, 纬度=${latitude}`);
              errorCount++;
              return;
            }
            
            // 计算项目位置，用于检查重叠
            const position = this.projectLatLongToPosition(
              longitude, 
              latitude, 
              this.projectionFnParam
            );
            
            // 验证投影结果
            if (!position || typeof position.x !== 'number' || typeof position.y !== 'number') {
              console.warn(`项目 ${project.id} 位置计算失败:`, position);
              errorCount++;
              return;
            }
            
            // 项目ID
            const projectId = typeof project.id === 'string' ? parseInt(project.id, 10) : project.id;
            
            // 检查位置是否与现有标签重叠
            let hasOverlap = false;
            
            // 检查此位置是否已经有标签
            positionCache.forEach((pos, id) => {
              if (id !== projectId) {
                const dx = Math.abs(pos.x - position.x);
                const dy = Math.abs(pos.y - position.y);
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < minDistanceThreshold) {
                  hasOverlap = true;
                  console.log(`项目 ${projectId} 与项目 ${id} 位置重叠，距离: ${distance.toFixed(2)}`);
                }
              }
            });
            
            // 无论是否重叠，都创建标签，但调整Z位置避免重叠
            const label = this.createProjectLabel({
              ...project,
              longitude: longitude,
              latitude: latitude
            });
            
            if (label) {
              // 存储位置用于后续检查
              positionCache.set(projectId, { x: position.x, y: position.y });
              
              // 如果有重叠，调整标签Z位置避免完全重叠
              if (hasOverlap) {
                const labelObject = this.labelObjects.get(projectId);
                if (labelObject) {
                  // 重叠时，根据项目状态调整Z位置
                  switch(Number(project.status)) {
                    case 1: // 进行中的项目 - 放在最上层
                      labelObject.position.z += 2;
                      labelObject.element.style.zIndex = '1001';
                      break;
                    case 2: // 已完成的项目 - 次层
                      labelObject.position.z += 1;
                      labelObject.element.style.zIndex = '999';
                      break;
                    case 0: // 准备中的项目
                      labelObject.position.z += 0.5;
                      labelObject.element.style.zIndex = '998';
                      break;
                    case 3: // 已暂停的项目 - 最底层
                      labelObject.position.z += 0.2;
                      labelObject.element.style.zIndex = '997';
                      break;
                    default:
                      labelObject.position.z += 0.1;
                      labelObject.element.style.zIndex = '996';
                  }
                }
              }
              
              createdCount++;
            } else {
              errorCount++;
            }
          } else {
            console.warn(`项目数据不完整:`, 
              project ? `ID=${project.id}, 经度=${project.longitude}, 纬度=${project.latitude}` : '项目为null');
            errorCount++;
          }
        } catch (error) {
          console.error('创建项目标签出错:', error, project);
          errorCount++;
        }
      });
      
      // 确保标签渲染器更新
      if (this._labelRenderer && this.scene && this.camera) {
        this._labelRenderer.render(this.scene, this.camera);
        console.log('标签渲染器更新完成');
      } else {
        console.warn('标签渲染器未正确初始化，无法更新');
      }
      
      console.log(`项目标签创建完成: 成功=${createdCount}, 失败=${errorCount}, 标签对象数量=${this.labelObjects ? this.labelObjects.size : 0}`);
      
      // 立即更新标签位置
      this.updateLabelPositions();
      
      // 设置标签位置自动更新
      if (!this._labelPositionUpdateInterval) {
        this._labelPositionUpdateInterval = setInterval(() => {
          if (this.updateLabelPositions) {
            this.updateLabelPositions();
          }
        }, 500); // 每500ms更新一次
        
        // 确保组件销毁时清理定时器
        this.$once('hook:beforeDestroy', () => {
          if (this._labelPositionUpdateInterval) {
            clearInterval(this._labelPositionUpdateInterval);
            this._labelPositionUpdateInterval = null;
          }
        });
      }
    },
    /**
     * 更新连接线
     */
    updateConnectingLines() {
      // 此方法已禁用，不再创建连接线
      console.log('连接线功能已禁用');
      return;
      
      // 以下代码已不再执行
      /*
      // 如果没有标签容器，则退出
      if (!this.rightLabelContainer) {
        return;
      }
      
      // 获取所有标签容器
      const labelContainers = this.rightLabelContainer.querySelectorAll('.map-label-container');
      if (!labelContainers || labelContainers.length === 0) {
        return;
      }
      
      // 处理每个标签容器
      labelContainers.forEach(container => {
        const projectId = container.dataset.projectId;
        if (!projectId) return;
        
        // 获取对应的地图点标记
        const mapPoint = document.querySelector(`.map-point-marker[data-project-id="${projectId}"]`);
        if (!mapPoint) return;
        
        // 获取标签和点标记的位置
        const labelRect = container.getBoundingClientRect();
        const pointRect = mapPoint.getBoundingClientRect();
        
        // 计算连接点
        const labelX = labelRect.left;
        const labelY = labelRect.top + labelRect.height / 2;
        const pointX = pointRect.left + pointRect.width / 2;
        const pointY = pointRect.top + pointRect.height / 2;
        
        // 创建或更新连接线
        this.drawConnectingLine(labelX, labelY, pointX, pointY, projectId);
      });
      */
    },
    /**
     * 清除连接线
     */
    clearConnectingLines() {
      // 移除所有连接线DOM元素
      document.querySelectorAll('.map-connection-line').forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
      
      // 移除所有点标记DOM元素
      document.querySelectorAll('.map-point-marker').forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
      
      // 清空连接线数组
      this.connectionLines = [];
      this.pointMarkers = [];
      
      console.log('已清除所有连接线和点标记');
    }
  }
}