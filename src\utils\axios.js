// 1.引入vue
// import Vue from 'vue'
// 2.引入axios库
import requestAxios from "axios";
import router from "@/router/index";
import store from '@/store'//引入store(vuex)
import { Message } from "element-ui";
import { getToken, setToken, removeToken } from "@/utils/auth";

requestAxios.defaults.timeout = 5000; // 请求超时时间

requestAxios.interceptors.request.use(
  // 请求拦截
  (config) => {
    if (getToken()) config.headers.common["token"] = getToken();
    if (sessionStorage.getItem("captcha")) config.headers.common["captcha"] = sessionStorage.getItem("captcha");
    return config;
  },

  (err) => {
    return Promise.reject(err);
  }
);

requestAxios.interceptors.response.use(
  (response) => {
    // 相应拦截
    let { data } = response;
    if (data.code == -1) {
      Message.error(data.msg || "请求异常！");
      return Promise.reject(data);
    }
    if (data.code == 203) {
      Message.error(data.msg || "登陆失效！");
      removeToken();
      store.dispatch('user/logout');
      return Promise.reject(data);
    }
    try {
      if (data.data.token) setToken(data.data.token);
      response.headers.captcha&&sessionStorage.setItem("captcha",response.headers.captcha);
    } catch (err) {

    }
    return response;
  },
  (err) => {
    Message.error("请求异常！！");
    return Promise.reject(err);
  }
);
// requestAxios.defaults.baseURL=""
const axios = function ({ path, method = "POST", data = {}, responseType } = {}) {
  return new Promise((resolve, reject) => {
    let requestConfig = {
      url: process.env.VUE_APP_BASE_API + path,
      method,
      responseType
    };
    
    // GET、DELETE等方法使用params，POST、PUT等方法使用data
    if (method === "GET" || method === "DELETE") {
      requestConfig.params = data;
    } else {
      requestConfig.data = data;
    }
    
    requestAxios(requestConfig)
      .then((res) => {
        if(responseType) return resolve(res);
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
export default axios;
