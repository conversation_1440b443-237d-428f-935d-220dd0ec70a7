<template>
  <div class="login-wrap">
    <div class="ms-login">
      <div class="ms-title">工业云平台</div>
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm" label-width="0px" class="ms-content">
        <el-form-item prop="name">
          <el-input v-model="loginForm.name" placeholder="用户名" @keyup.enter.native="handleLogin">
            <el-button slot="prepend" icon="el-icon-user-solid"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item prop="pwd">
          <el-input type="password" placeholder="密码" v-model="loginForm.pwd" @keyup.enter.native="handleLogin">
            <el-button slot="prepend" icon="el-icon-question"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="captcha-box">
            <div class="captcha-input-row"> <!-- 新增行容器 -->
              <el-input 
                type="text" 
                placeholder="请输入验证码" 
                v-model="loginForm.captcha" 
                @keyup.enter.native="handleLogin"
                style="flex:1">
                <el-button slot="prepend">验证码</el-button>
              </el-input>
              <div class="captcha-svg" @click="getCaptcha" v-html="captchaSvg"></div>
            </div>
            <div class="captcha-hint" @click="getCaptcha">看不清？点击更换</div>
          </div>
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click.native.prevent="handleLogin">登录</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { login ,getCaptcha } from '@/api/admin';
import md5 from 'js-md5';
export default {
  name: "Login",
  data () {
    return {
      loginForm: {
        name: "",
        pwd: "",
      },
      loginRules: {
        name: [
          {
            required: true,
            trigger: "blur",
            message: "请输入用户名",
          },
        ],
        pwd: [
          { required: true, trigger: "blur", message: "请输入密码" },
        ],
        captcha: [
          { required: true, trigger: "blur", message: "请输入验证码" },
        ],
      },
      loading: false,
      redirect: undefined,
      captchaSvg:""
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCaptcha();
  },
  methods: {
    //点击登陆
    handleLogin () {
      this.$refs.loginForm.validate(async (valid) => {
        if (!valid) return
        await login({...this.loginForm,pwd:md5(this.$nodeMD5+this.loginForm.pwd)});
        this.$router.push({ path: this.redirect || "/" });
      });
    },
    async getCaptcha(){
      let {data}=await getCaptcha();
      this.captchaSvg=data;
    },
  },
};
</script>


<style scoped lang="scss">
.login-wrap {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="%23e4e8eb"/></svg>') repeat;
    opacity: 0.5;
  }
}

.ms-login {
  width: 500px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ms-title {
  font-size: 28px;
  font-weight: 600;
  letter-spacing: 2px;
  margin: 40px 0;
  text-align: center;
  width: 100%;
  color: #1a1a1a;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #1975FF;
    border-radius: 2px;
  }
}

.ms-content {
  padding: 40px 50px;
  width: 100%;
}

.el-input__inner {
  border-radius: 8px !important;
  height: 48px !important;
  line-height: 48px;
  border: 1px solid #e4e8eb;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #1975FF;
    box-shadow: 0 0 0 2px rgba(25, 117, 255, 0.1);
  }
}

.login-btn {
  width: 100%;
  margin-top: 20px;
  
  button {
    width: 100%;
    height: 48px !important;
    font-size: 16px;
    font-weight: 500;
    background: #1975FF;
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
    
    &:hover {
      background: #1560d6;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(25, 117, 255, 0.2);
    }
  }
}

.captcha-box {
  width: 100%;
}

.captcha-input-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.captcha-svg {
  height: 48px;
  border: 1px solid #e4e8eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #1975FF;
    box-shadow: 0 0 0 2px rgba(25, 117, 255, 0.1);
  }
}

.captcha-hint {
  text-align: right;
  margin-top: 8px;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  
  &:hover {
    color: #1975FF;
  }
}

// 添加输入框图标样式
.el-input-group__prepend {
  background: #f5f7fa;
  border: 1px solid #e4e8eb;
  color: #666;
  border-radius: 8px 0 0 8px !important;
  padding: 0 15px;
  
  .el-icon-user-solid,
  .el-icon-question {
    font-size: 18px;
  }
}
</style>
