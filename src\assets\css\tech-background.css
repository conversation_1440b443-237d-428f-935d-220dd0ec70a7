/* 高科技背景样式 - 蓝色科技主题 */
.tech-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0; /* 调整z-index确保背景可见 */
  overflow: hidden;
  background: linear-gradient(135deg, rgba(0, 40, 100, 0.8), rgba(0, 20, 60, 0.7), rgba(0, 10, 40, 0.8));
  perspective: 1000px; /* 添加透视效果 */
  transform-style: preserve-3d; /* 保留3D效果 */
  animation: backgroundPulse 15s ease-in-out infinite alternate;
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

/* 网格线效果 - 增强蓝色科技感 */
.tech-background::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  right: -100%;
  bottom: -100%;
  background-image:
    linear-gradient(rgba(0, 168, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 168, 255, 0.15) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 120s linear infinite;
  opacity: 0.4;
  transform: rotateX(60deg) translateZ(-100px); /* 添加3D旋转效果 */
  box-shadow: 0 0 50px rgba(0, 120, 255, 0.3);
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

/* 光点效果 - 增强蓝色科技感 */
.tech-background::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background-image: radial-gradient(circle, rgba(0, 168, 255, 0.3) 1px, transparent 2px);
  background-size: 35px 35px;
  animation: dotPulse 4s ease-in-out infinite alternate;
  opacity: 0.25;
  transform: rotateX(45deg) translateZ(-50px); /* 添加3D旋转效果 */
  filter: drop-shadow(0 0 2px rgba(0, 150, 255, 0.5));
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

/* 光晕效果 - 增强蓝色科技感 */
.glow-effect {
  position: absolute;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 168, 255, 0.25) 0%, rgba(0, 80, 180, 0.15) 40%, rgba(0, 26, 64, 0) 70%);
  filter: blur(30px);
  opacity: 0.5;
  animation: glowMove 20s linear infinite;
  transform: translateZ(-30px); /* 添加3D效果 */
  box-shadow: 0 0 50px rgba(0, 120, 255, 0.3);
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

.glow-effect:nth-child(1) {
  top: 10%;
  left: 10%;
  width: 600px;
  height: 600px;
  animation-delay: 0s;
}

.glow-effect:nth-child(2) {
  top: 60%;
  left: 80%;
  width: 700px;
  height: 700px;
  animation-delay: -5s;
}

.glow-effect:nth-child(3) {
  top: 80%;
  left: 30%;
  width: 550px;
  height: 550px;
  animation-delay: -10s;
}

/* 添加额外的光晕 */
.glow-effect:nth-child(4) {
  top: 30%;
  left: 60%;
  width: 450px;
  height: 450px;
  animation-delay: -15s;
}

/* 数字雨效果 - 增强蓝色科技感 */
.digital-rain {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  overflow: hidden;
  opacity: 0.15;
  transform: rotateX(30deg) translateZ(-80px); /* 添加3D旋转效果 */
  transform-style: preserve-3d;
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

.rain-column {
  position: absolute;
  top: -100px;
  color: rgba(0, 168, 255, 0.6);
  font-family: monospace;
  font-size: 16px;
  text-align: center;
  animation: digitalRain linear infinite;
  text-shadow: 0 0 8px rgba(0, 168, 255, 0.5);
  filter: drop-shadow(0 0 2px rgba(0, 150, 255, 0.5));
  pointer-events: none; /* 确保不会阻挡鼠标事件 */
}

/* 动画定义 - 增强蓝色科技感 */
@keyframes backgroundPulse {
  0% {
    background: linear-gradient(135deg, rgba(0, 40, 100, 0.8), rgba(0, 20, 60, 0.7), rgba(0, 10, 40, 0.8));
  }
  50% {
    background: linear-gradient(135deg, rgba(0, 50, 120, 0.8), rgba(0, 30, 80, 0.7), rgba(0, 15, 50, 0.8));
  }
  100% {
    background: linear-gradient(135deg, rgba(0, 40, 100, 0.8), rgba(0, 20, 60, 0.7), rgba(0, 10, 40, 0.8));
  }
}

@keyframes gridMove {
  0% {
    transform: rotateX(60deg) translateZ(-100px) translateY(0) translateX(0);
  }
  100% {
    transform: rotateX(60deg) translateZ(-100px) translateY(60px) translateX(60px);
  }
}

@keyframes dotPulse {
  0% {
    opacity: 0.15;
    transform: rotateX(45deg) translateZ(-50px) scale(0.95);
    filter: drop-shadow(0 0 2px rgba(0, 150, 255, 0.3));
  }
  50% {
    opacity: 0.3;
    transform: rotateX(45deg) translateZ(-50px) scale(1.05);
    filter: drop-shadow(0 0 5px rgba(0, 150, 255, 0.6));
  }
  100% {
    opacity: 0.15;
    transform: rotateX(45deg) translateZ(-50px) scale(0.95);
    filter: drop-shadow(0 0 2px rgba(0, 150, 255, 0.3));
  }
}

@keyframes glowMove {
  0% {
    transform: translateZ(-30px) translate(0, 0);
    opacity: 0.4;
  }
  25% {
    transform: translateZ(-30px) translate(80px, 30px);
    opacity: 0.5;
  }
  50% {
    transform: translateZ(-30px) translate(0, 60px);
    opacity: 0.6;
  }
  75% {
    transform: translateZ(-30px) translate(-80px, 30px);
    opacity: 0.5;
  }
  100% {
    transform: translateZ(-30px) translate(0, 0);
    opacity: 0.4;
  }
}

@keyframes digitalRain {
  0% {
    transform: translateY(-100px);
    opacity: 0.8;
    text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 10px rgba(0, 168, 255, 0.7);
  }
  100% {
    transform: translateY(calc(200vh + 100px));
    opacity: 0;
    text-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
  }
}
