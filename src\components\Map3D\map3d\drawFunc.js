import * as THREE from 'three'
import * as d3 from 'd3'
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { Line2 } from 'three/examples/jsm/lines/Line2'
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial'
import { mapConfig } from './mapConfig'

export function getDynamicMapScale(mapObject3D, containerRef) {
  const width = containerRef.clientWidth
  const height = containerRef.clientHeight
  const refArea = width * height

  const boundingBox = new THREE.Box3().setFromObject(mapObject3D)
  // 获取包围盒的尺寸
  const size = new THREE.Vector3()
  boundingBox.getSize(size)
  // 新增 Math.random避免缩放为1，没有动画效果
  const scale =
    Math.round(Math.sqrt(refArea / (size.x * size.y * 400))) +
    parseFloat((Math.random() + 0.5).toFixed(2))
  return scale
}

// 绘制挤出的材质
export function drawExtrudeMesh(point, projectionFn) {
  const shape = new THREE.Shape()
  const pointsArray = []

  for (let i = 0; i < point.length; i++) {
    const [x, y] = projectionFn(point[i]) // 将每一个经纬度转化为坐标点
    if (i === 0) {
      shape.moveTo(x, -y)
    }
    shape.lineTo(x, -y)
    pointsArray.push(x, -y, mapConfig.topLineZIndex)
  }

  const geometry = new THREE.ExtrudeGeometry(shape, {
    depth: mapConfig.mapDepth, // 挤出的形状深度
    bevelEnabled: false, // 对挤出的形状应用是否斜角
  })
  
  // 确定要使用的颜色
  // 如果mapColor为null，则使用传入的provinceColor（在generateMapObject3D中设置）
  const materialColor = mapConfig.mapColor;
  
  // 面材质 - 使用PhongMaterial以获得更好的光照效果
  const material = new THREE.MeshPhongMaterial({
    color: materialColor,
    transparent: mapConfig.mapTransparent,
    opacity: mapConfig.mapOpacity,
    shininess: 80,        // 高光度
    specular: 0xffffff,   // 高光颜色
    flatShading: false,   // 平滑着色
    side: THREE.DoubleSide // 双面显示
  });

  // 侧面材质 - 使用着色器材质
  const materialSide = new THREE.ShaderMaterial({
    uniforms: {
      color1: {
        value: new THREE.Color(mapConfig.mapSideColor1),
      },
      color2: {
        value: new THREE.Color(mapConfig.mapSideColor2),
      },
    },
    vertexShader: `
      varying vec3 vPosition;
      void main() {
        vPosition = position;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 color1;
      uniform vec3 color2;
      varying vec3 vPosition;
      void main() {
        vec3 mixColor = mix(color1, color2, 0.5 - vPosition.z * 0.2); // 使用顶点坐标 z 分量来控制混合
        gl_FragColor = vec4(mixColor, 1.0);
      }
    `,
    side: THREE.DoubleSide, // 双面显示
  })

  // 创建网格对象，使用两种材质
  const mesh = new THREE.Mesh(geometry, [material, materialSide])
  
  // 保存自定义属性
  mesh.userData = {
    isChangeColor: true,
    originalColor: materialColor // 保存原始颜色，方便后续操作
  }

  // 创建轮廓线
  const lineGeometry = new LineGeometry()
  lineGeometry.setPositions(pointsArray)

  const lineMaterial = new LineMaterial({
    color: mapConfig.topLineColor,
    linewidth: mapConfig.topLineWidth,
  })
  lineMaterial.resolution.set(window.innerWidth, window.innerHeight)
  const line = new Line2(lineGeometry, lineMaterial)

  return { mesh, line }
}

// 生成地图3D模型
export function generateMapObject3D(mapdata, projectionFnParam) {
  // 地图对象
  const mapObject3D = new THREE.Object3D()
  // 地图数据
  const { features: basicFeatures } = mapdata

  const { center, scale } = projectionFnParam

  const projectionFn = d3
    .geoMercator()
    .center(center)
    .scale(scale)
    .translate([0, 0])

  const label2dData = [] // 存储自定义 2d 标签数据

  // 每个省的数据
  basicFeatures.forEach((basicFeatureItem, featureIndex) => {
    // 每个省份的地图对象
    const provinceMapObject3D = new THREE.Object3D()
    // 将地图数据挂在到模型数据上
    provinceMapObject3D.customProperties = basicFeatureItem.properties

    // 如果mapColor为null，为每个省份选择一个固定的颜色（基于索引）
    const originalMapColor = mapConfig.mapColor;
    
    // 为当前省份选择颜色
    if (originalMapColor === null) {
      // 确保数组不为空
      if (mapConfig.mapColorGradient && mapConfig.mapColorGradient.length > 0) {
        // 计算颜色索引，确保每个省份使用不同颜色
        const colorIndex = featureIndex % mapConfig.mapColorGradient.length;
        const provinceColor = mapConfig.mapColorGradient[colorIndex];
        
        // 直接设置临时颜色
        mapConfig.mapColor = provinceColor;
      }
    }

    // 每个坐标类型
    const featureType = basicFeatureItem.geometry.type
    // 每个坐标数组
    const featureCoords = basicFeatureItem.geometry.coordinates
    // 每个中心点位置
    const featureCenterCoord =
      basicFeatureItem.properties.centroid &&
      projectionFn(basicFeatureItem.properties.centroid)
    // 名字
    const featureName = basicFeatureItem.properties.name

    if (featureCenterCoord) {
      label2dData.push({
        featureCenterCoord,
        featureName,
      })
    }

    // MultiPolygon 类型
    if (featureType === 'MultiPolygon') {
      featureCoords.forEach((multiPolygon) => {
        multiPolygon.forEach((polygon) => {
          const { mesh, line } = drawExtrudeMesh(polygon, projectionFn)
          provinceMapObject3D.add(mesh)
          provinceMapObject3D.add(line)
        })
      })
    }

    // Polygon 类型
    if (featureType === 'Polygon') {
      featureCoords.forEach((polygon) => {
        const { mesh, line } = drawExtrudeMesh(polygon, projectionFn)
        provinceMapObject3D.add(mesh)
        provinceMapObject3D.add(line)
      })
    }

    // 还原原始颜色设置
    mapConfig.mapColor = originalMapColor;

    mapObject3D.add(provinceMapObject3D)
  })

  return { mapObject3D, label2dData }
}

// 生成地图2D标签
export function generateMapLabel2D(label2dData) {
  const labelObject2D = new THREE.Object3D()
  
  // 强化对空数据的检测
  if (!label2dData) {
    console.warn('generateMapLabel2D: 收到undefined或null数据');
    return labelObject2D;
  }
  
  if (!Array.isArray(label2dData)) {
    console.warn('generateMapLabel2D: 收到的数据不是数组');
    return labelObject2D;
  }
  
  if (label2dData.length === 0) {
    console.warn('generateMapLabel2D: 收到空数组数据');
    return labelObject2D;
  }
  
  console.log('开始生成2D标签，数据数量:', label2dData.length);
  
  try {
    // 创建标签前检查数据质量
    const validItems = label2dData.filter(item => 
      item && 
      item.featureCenterCoord && 
      Array.isArray(item.featureCenterCoord) && 
      item.featureCenterCoord.length >= 2 &&
      item.featureName
    );
    
    if (validItems.length === 0) {
      console.warn('generateMapLabel2D: 所有数据项均无效');
      return labelObject2D;
    }
    
    if (validItems.length < label2dData.length) {
      console.warn(`generateMapLabel2D: 过滤掉了 ${label2dData.length - validItems.length} 个无效数据项`);
    }
    
    // 按优先级排序项目（如果需要）
    validItems.sort((a, b) => {
      // 首先按状态排序
      const statusA = a.projectData?.status || 0;
      const statusB = b.projectData?.status || 0;
      if (statusA !== statusB) return statusA - statusB;
      
      // 状态相同时按ID排序
      const idA = a.projectData?.id || 0;
      const idB = b.projectData?.id || 0;
      return idA - idB;
    });
    
    // 使用过滤后的有效数据项创建标签
    validItems.forEach((item) => {
      const { featureCenterCoord, featureName, statusColor, projectData, properties } = item;
      
      // 提取并处理项目ID (处理Vue响应式对象)
      let projectId = null;
      let projectName = featureName || (properties && properties.name) || "未命名项目";
      
      if (projectData) {
        projectId = projectData.id;
        // 确保名称可用
        if (projectData.name) {
          projectName = projectData.name;
        }
        // 确保ID是数字类型
        if (typeof projectId === 'string') {
          projectId = parseInt(projectId, 10);
        } else if (typeof projectId === 'undefined' || projectId === null) {
          // 如果没有ID，生成一个唯一ID
          projectId = Math.floor(Math.random() * 100000);
        }
      }
      
      // 确定项目状态
      let status = 'in-progress';
      if (projectData && projectData.status !== undefined) {
        // 将数字状态转换为字符串状态
        switch(projectData.status) {
          case 0:
            status = 'preparing'; // 准备中
            break;
          case 1:
            status = 'in-progress'; // 进行中
            break;
          case 2:
            status = 'completed'; // 已完成
            break;
          case 3:
            status = 'paused'; // 已暂停
            break;
          default:
            // 如果是字符串状态，直接使用
            if (typeof projectData.status === 'string') {
              status = projectData.status;
            }
        }
      }
      
      // 创建标签对象 - 直接使用地图坐标位置
      const labelPos = {
        x: featureCenterCoord[0],
        y: -featureCenterCoord[1], // 负Y值使坐标系与Three.js匹配
        z: mapConfig.labelZIndex
      };
      
      const labelObj = draw2dLabel({
        id: projectId,
        name: projectName,
        status: status,
        position: labelPos
      });
      
      // 标记为可点击
      labelObj.element.style.cursor = 'pointer';
      
      // 保存原始项目数据到标签对象中
      if (projectData) {
        labelObj.userData = {
          ...labelObj.userData,
          projectData: projectData,
          clickable: true
        };
      }
      
      labelObject2D.add(labelObj);
    });
  } catch (error) {
    console.error('在生成2D标签过程中发生未处理的错误:', error);
  }
  
  console.log('2D标签生成完成，总数:', labelObject2D.children.length);
  return labelObject2D;
}

// 生成地图点位
export function generateMapSpot(label2dData) {
  const spotObject3D = new THREE.Object3D()
  const spotList = []
  
  label2dData.forEach((item) => {
    const { featureCenterCoord } = item
    const spotObjectItem = drawSpot(featureCenterCoord)
    if (spotObjectItem) {
      spotList.push(spotObjectItem)
      spotObject3D.add(spotObjectItem)
    }
  })
  
  return { spotList, spotObject3D }
}

/**
 * 创建一个2D标签对象用于地图
 * @param {Object} options - 标签配置
 * @returns {CSS2DObject} 2D标签对象
 */
export const draw2dLabel = (options) => {
  const {
    id,                 // 项目ID
    name,               // 项目名称
    status,             // 项目状态
    position,           // 标签位置 {x, y, z}
    backgroundColor,    // 可选：背景颜色
    color               // 可选：文本颜色
  } = options;

  // 创建容器元素 - 直接显示在坐标位置
  const containerElement = document.createElement('div');
  containerElement.className = 'map-label-container';
  containerElement.style.position = 'absolute';
  containerElement.style.pointerEvents = 'none';
  containerElement.style.width = '120px'; // 设置足够宽度来容纳标签
  containerElement.style.height = '30px';
  containerElement.style.zIndex = '999';
  containerElement.style.transform = 'translate(-50%, -50%)'; // 居中对齐到坐标点
  
  // 保存数据属性用于外部识别
  containerElement.dataset.projectId = id;
  containerElement.dataset.projectName = name;

  // 创建标签元素
  const labelElement = document.createElement('div');
  labelElement.className = 'map-label';
  if (status) {
    labelElement.classList.add(`status-${status}`);
  }
  labelElement.style.position = 'absolute';
  labelElement.style.left = '50%';
  labelElement.style.top = '50%';
  labelElement.style.transform = 'translate(-50%, -50%)';
  labelElement.style.pointerEvents = 'auto';
  labelElement.style.whiteSpace = 'nowrap';
  labelElement.style.background = 'rgba(0, 0, 0, 0.7)';
  labelElement.style.color = '#fff';
  labelElement.style.padding = '3px 8px';
  labelElement.style.borderRadius = '4px';
  labelElement.style.fontSize = '12px';
  labelElement.dataset.projectId = id;
  
  // 创建状态图标
  const iconElement = document.createElement('span');
  iconElement.className = 'label-icon';
  
  // 根据状态设置图标颜色
  let statusColor = '#1E88E5'; // 默认为进行中蓝色
  switch(status) {
    case 'completed':
      statusColor = '#4CAF50'; // 完成 绿色
      break;
    case 'paused':
      statusColor = '#FF9800'; // 暂停 橙色
      break;
    case 'preparing':
      statusColor = '#9C27B0'; // 准备中 紫色
      break;
    case 'in-progress':
    default:
      statusColor = '#1E88E5'; // 进行中 蓝色
      break;
  }
  
  iconElement.style.backgroundColor = statusColor;
  iconElement.style.display = 'inline-block';
  iconElement.style.width = '8px';
  iconElement.style.height = '8px';
  iconElement.style.borderRadius = '50%';
  iconElement.style.marginRight = '5px';
  labelElement.appendChild(iconElement);
  
  // 添加项目名称
  const nameText = document.createTextNode(name || '未命名项目');
  labelElement.appendChild(nameText);

  // 将标签添加到容器
  containerElement.appendChild(labelElement);

  // 创建CSS2D对象
  const labelObject = new CSS2DObject(containerElement);
  
  // 设置位置
  if (position) {
    // 使用实际的地图坐标
    labelObject.position.set(
      position.x,
      position.y,
      position.z || mapConfig.labelZIndex || 2.0
    );
    
    console.log(`标签 "${name}" 位置设置为: x=${position.x}, y=${position.y}, z=${position.z || mapConfig.labelZIndex}`);
  }
  
  // 添加用户数据
  labelObject.userData = {
    id: id,
    name: name,
    status: status,
    element: containerElement // 保存DOM元素引用
  };
  
  return labelObject;
}

/**
 * 格式化状态文本
 */
function formatStatus(status) {
  switch (status) {
    case 'in-progress':
      return '进行中';
    case 'completed':
      return '已完成';
    case 'paused':
      return '已暂停';
    case 'preparing':
      return '准备中';
    default:
      return status;
  }
}

// 画点
export const drawSpot = (coord) => {
  if (!coord) return null
  
  const spotGeometry = new THREE.CircleGeometry(0.5, 5)
  const spotMaterial = new THREE.MeshBasicMaterial({
    color: 0x00ff00,
    transparent: true,
    opacity: 0.8,
    side: THREE.DoubleSide,
  })
  
  const mesh = new THREE.Mesh(spotGeometry, spotMaterial)
  mesh.position.set(coord[0], -coord[1], mapConfig.spotZIndex)
  mesh.rotation.x = Math.PI / 2
  mesh.userData = { coord: coord }
  
  return mesh
}

// 飞线点
export const drawflySpot = (curve) => {
  const curveGeometry = new THREE.TubeGeometry(curve, 20, 0.03, 8, false)
  
  const flyLineMaterial = new THREE.ShaderMaterial({
    uniforms: {
      dashOffset: { value: 0.0 },
      color: { value: new THREE.Color('#ffff00') },
      opacity: { value: 1.0 },
    },
    vertexShader: `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float dashOffset;
      uniform float opacity;
      uniform vec3 color;
      varying vec2 vUv;
      void main() {
        float x = fract(dashOffset * 2.0 + vUv.x);
        if(x > 0.5) discard;
        gl_FragColor = vec4(color, opacity);
      }
    `,
    transparent: true,
    side: THREE.DoubleSide,
  })
  
  return new THREE.Mesh(curveGeometry, flyLineMaterial)
}

// 画连接线
export const drawLineBetween2Spot = (coordStart, coordEnd) => {
  // 根据传入的两点画一条线
  const start = new THREE.Vector3(coordStart[0], -coordStart[1], mapConfig.spotZIndex)
  const end = new THREE.Vector3(coordEnd[0], -coordEnd[1], mapConfig.spotZIndex)
  
  // 两点中点
  const middle = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5)
  
  // 抬高中点的高度
  middle.z += 10
  
  const curve = new THREE.QuadraticBezierCurve3(start, middle, end)
  
  // 获取曲线点
  const points = curve.getPoints(50)
  
  const geometry = new THREE.BufferGeometry().setFromPoints(points)
  const material = new THREE.LineBasicMaterial({
    color: mapConfig.flyLineColor,
  })
  
  const flyLine = new THREE.Line(geometry, material)
  const flySpot = drawflySpot(curve)
  
  return { flyLine, flySpot }
}

/**
 * 绘制地图上的点标记，支持脉冲效果
 * @param {Object} options - 配置参数
 * @param {Object} options.position - 点的位置，包含 x, y 坐标
 * @param {HTMLElement} options.container - 容器元素
 * @param {string} options.id - 唯一标识
 * @param {string} options.className - CSS 类名
 * @param {string} options.color - 点的颜色
 * @param {number} options.size - 点的大小，默认为 8
 * @param {number} options.pulseSize - 脉冲效果的大小，默认为 24
 * @param {Function} options.onClick - 点击事件处理函数
 * @returns {HTMLElement} - 创建的标记元素
 */
export function drawMapPoint(options) {
  const { 
    position, 
    container, 
    id, 
    className = '', 
    color = '#4285f4', 
    size = 8, 
    pulseSize = 24,
    onClick 
  } = options;

  // 创建标记容器
  const marker = document.createElement('div');
  marker.className = `map-point-marker ${className}`;
  marker.id = id;
  marker.style.left = `${position.x}px`;
  marker.style.top = `${position.y}px`;
  marker.style.cursor = 'pointer';
  
  // 创建内部点
  const innerPoint = document.createElement('div');
  innerPoint.className = 'map-point-inner';
  innerPoint.style.width = `${size}px`;
  innerPoint.style.height = `${size}px`;
  innerPoint.style.backgroundColor = color;
  innerPoint.style.borderRadius = '50%';
  innerPoint.style.position = 'absolute';
  innerPoint.style.left = '50%';
  innerPoint.style.top = '50%';
  innerPoint.style.transform = 'translate(-50%, -50%)';
  innerPoint.style.boxShadow = `0 0 5px ${color}`;
  innerPoint.style.zIndex = '2';
  
  // 创建外部脉冲圆
  const pulseCircle = document.createElement('div');
  pulseCircle.className = 'map-point-pulse';
  pulseCircle.style.width = `${pulseSize}px`;
  pulseCircle.style.height = `${pulseSize}px`;
  pulseCircle.style.border = `2px solid ${color}`;
  pulseCircle.style.borderRadius = '50%';
  pulseCircle.style.position = 'absolute';
  pulseCircle.style.left = '50%';
  pulseCircle.style.top = '50%';
  pulseCircle.style.transform = 'translate(-50%, -50%)';
  pulseCircle.style.animation = 'pulseMapPoint 2s infinite';
  pulseCircle.style.zIndex = '1';
  
  // 添加点击事件
  if (onClick) {
    marker.addEventListener('click', onClick);
  }
  
  // 组装标记
  marker.appendChild(innerPoint);
  marker.appendChild(pulseCircle);
  container.appendChild(marker);
  
  return marker;
} 